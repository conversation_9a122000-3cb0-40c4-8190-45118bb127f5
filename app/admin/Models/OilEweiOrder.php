<?php
/**
 * ewei与本系统工单关系表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/09/29
 * Time: 11:43:34
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilEweiOrder extends \Framework\Database\Model
{
    protected $table = 'oil_ewei_order';

    protected $guarded = ["id"];
    protected $fillable = ['tb_name','tb_pk','tb_no','ew_id','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By tb_name
        if (isset($params['tb_name']) && $params['tb_name'] != '') {
            $query->where('tb_name', '=', $params['tb_name']);
        }

        //Search By tb_pk
        if (isset($params['tb_pk']) && $params['tb_pk'] != '') {
            $query->where('tb_pk', '=', $params['tb_pk']);
        }

        //Search By tb_no
        if (isset($params['tb_no']) && $params['tb_no'] != '') {
            $query->where('tb_no', '=', $params['tb_no']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By ew_id
        if (isset($params['ew_id']) && $params['ew_id'] != '') {
            $query->where('ew_id', '=', $params['ew_id']);
        }

        //Search By ew_status
        if (isset($params['ew_status']) && $params['ew_status'] != '') {
            $query->where('ew_status', '=', $params['ew_status']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * ewei与本系统工单关系表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilEweiOrder::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * ewei与本系统工单关系表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilEweiOrder::find($params['id']);
    }

    /**
     * ewei与本系统工单关系表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilEweiOrder::create($params);
    }

    /**
     * ewei与本系统工单关系表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilEweiOrder::find($params['id'])->update($params);
    }

    /**
     * ewei与本系统工单关系表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilEweiOrder::destroy($params['ids']);
    }

    /**
     * 按工单类型和工单号查询
     * @param array $params
     * @return mixed
     */
    static public function getByTbNo(array $params)
    {
        \helper::argumentCheck(['tb_name','tb_no'], $params);

        return OilEweiOrder::where('tb_name','=',$params['tb_name'])->where('tb_no','=',$params['tb_no'])->first();
    }

    /**
     * 按工单类型和工单ID
     * @param array $params
     * @return mixed
     */
    static public function getByTbPk(array $params)
    {
        \helper::argumentCheck(['tb_name','tb_pk'], $params);

        return OilEweiOrder::where('tb_name','=',$params['tb_name'])->where('tb_pk','=',$params['tb_pk'])->first();
    }

}