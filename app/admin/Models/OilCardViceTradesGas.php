<?php
/**
 * 油品消费记录 慧加油接口
 * User: liyonghua
 * Date: 2022/10/22
 * Time: 14:20:39
 */

namespace Models;
use Framework\Database\Model;
use Framework\Log;
use Fuel\Defines\TradesType;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCardViceTradesGas extends Model
{

    protected $table = 'oil_card_vice_trades';

    protected $guarded = ["id"];

    public $timestamps = FALSE; //修改不指定时间修改，如需修改必须手动制定updatetime

    public static function getTradesList(array $params)
    {
        $connection = "slave";
        if (API_ENV == 'dev') {
            $connection = "";
        }
        Capsule::connection($connection)->enableQueryLog();
        if (isset($params['trade_type']) && $params['trade_type']) {
            $trade_type_arr = explode(',', $params['trade_type']);
            $trade_type_list = [];
            foreach ($trade_type_arr as $value) {
                if ($value == '交易撤销') {
                    $trade_type_list[] = $value;
                } else {
                    $trade_type_list = array_merge($trade_type_list, TradesType::gettradesZArr($value));
                }
            }
            Log::error('trades-sql:' . var_export($trade_type_list, TRUE), [], 'sql-trade-gas');
            $params['tradeTypeList'] = $trade_type_list;
        }

        //子机构及其以下机构
        if($params['orgcode_in']) {
            $params['orgcode_in'] = self::getSubOrg($params['orgcode_in']);
        }

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $limit = intval($params['limit']) > 0 ? intval($params['limit']) : 50;

        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $fields = 'oil_card_vice_trades.*,oil_type_no.oil_type,oil_org.orgcode';

        $sqlObj = self::getSqlObj($params, $fields, $connection);

        $result = $sqlObj->orderBy("oil_card_vice_trades.trade_time", "desc")->paginate($limit, ['*'], 'page', $params['page'])->toArray();

        $sql = Capsule::connection($connection)->getQueryLog();
        Log::error('trades-sql:' . var_export($sql, TRUE), [], 'sql-trade-gas');
        return $result;
    }


    public static function getSqlObj($params, $fields, $connection = '')
    {

        $sqlObj = Capsule::connection($connection)->table('oil_card_vice_trades as oil_card_vice_trades')
            ->leftJoin('oil_org', function ($query) {
                $query->on('oil_org.id', '=', 'oil_card_vice_trades.org_id')->where('oil_org.is_del', '=', 0);
            })
            ->leftJoin('oil_station', function ($query) {
                $query->on('oil_station.station_name', '=', 'oil_card_vice_trades.trade_place')->where('oil_station.is_del', '=', 0);
            })
            ->leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name')
            ->select(Capsule::raw($fields));

        $sqlObj = (new OilCardViceTrades())->scopeFilter($sqlObj, $params);

        return $sqlObj;
    }


    /**
     * Desc: 获取机构及其子机的所有机构
     *  ---------------------------------------------
     * @param $orgcode
     * @return array
     *  ---------------------------------------------
     * <AUTHOR>
     * @DateTime: 2022-10-22 16:51
     */
    private static function  getSubOrg ($orgcode = '') {
        if(strpos($orgcode,',') === FALSE ) {
            $org_arr[] = $orgcode;
        } else {
            $org_arr = explode(",", $orgcode);
        }
        $return_arr = [];
        foreach ($org_arr as $org) {
            $list = OilOrg::getSubOrgCodes($org,1);
            $return_arr = array_merge($return_arr,$list);
        }
        return array_unique($return_arr);
    }

}