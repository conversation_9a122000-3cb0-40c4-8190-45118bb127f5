<?php
/**
 * 油站表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:19
 */

namespace Models;

use Framework\Cache;
use Framework\Log;
use Fuel\Defines\OilCom;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilStation extends \Framework\Database\Model
{
	protected $table = 'oil_station';

	protected $guarded = ["id"];

	protected $fillable = [
		'oil_com', 'station_name', 'station_code', 'pcode',
		'map_type', 'alias', 'lng', 'lat', 'detail_address',
		'phone', 'locate_time', 'last_trade_id', 'level',
		'regions_id', 'province_code', 'province_name', 'regions_id',
		'is_del', 'station_operators_id', 'creator_id',
		'last_operator', 'createtime', 'updatetime','first_trade_time','latest_trade_time'
	];

	public function getFillAble()
	{
		return $this->fillable;
	}

	public function oilStationOperator()
	{
		return $this->belongsTo('Models\OilStationOperators', 'station_operators_id', 'id');
	}

	/**
	 * 聚集查询
	 * @param $query
	 * @param $params
	 * @return $query
	 */
	public function scopeFilter($query, $params)
	{

		//Search By id
		if (isset($params['id']) && $params['id'] != '') {
			$query->where('id', '=', $params['id']);
		}

		//Search By oil_com
		if (isset($params['oil_com']) && $params['oil_com'] != '') {
			$query->where('oil_com', '=', $params['oil_com']);
		}

		if (isset($params['search_oil_come']) && $params['search_oil_come'] != '') {
			$query->where('oil_com', '=', $params['search_oil_come']);
		}

		//Search By station_name
		if (isset($params['station_name']) && $params['station_name'] != '') {
			$query->where('station_name', '=', $params['station_name']);
		}

		if (isset($params['station_code'])) {
            if (is_array($params['station_code']) && !empty($params['station_code'])) {
                $query->whereIn('station_code', $params['station_code']);
            } elseif ($params['station_code'] != '') {
                $query->where('station_code', '=', $params['station_code']);
            }
        }

		if (isset($params['search_station_name']) && $params['search_station_name'] != '') {
			$query->where('station_name', 'Like', "%" . $params['search_station_name'] . "%");
		}

		//Search By station_name_in
		if (isset($params['station_name_in']) && $params['station_name_in'] != '') {
			$query->whereIn('station_name', $params['station_name_in']);
		}

		//Search By alias
		if (isset($params['alias']) && $params['alias'] != '') {
			$query->where('alias', '=', $params['alias']);
		}

		//Search By lng
		if (isset($params['lng']) && $params['lng'] != '') {
			$query->where('lng', '=', $params['lng']);
		}

		//Search By lat
		if (isset($params['lat']) && $params['lat'] != '') {
			$query->where('lat', '=', $params['lat']);
		}

		//Search By detail_address
		if (isset($params['detail_address']) && $params['detail_address'] != '') {
			$query->where('detail_address', '=', $params['detail_address']);
		}

		//Search By locate_time
		if (isset($params['locate_time']) && $params['locate_time'] != '') {
			$query->where('locate_time', '=', $params['locate_time']);
		}

		//Search By level
		if (isset($params['level']) && $params['level'] != '') {
			$query->where('level', '=', $params['level']);
		}

		//Search By regions_id
		if (isset($params['regions_id']) && $params['regions_id'] != '') {
			$query->where('regions_id', '=', $params['regions_id']);
		}
		if (isset($params['search_regions_id']) && $params['search_regions_id'] != '') {
			if ($params['search_regions_id'] == 99) {
				$query->whereNull('regions_id');
			} else {
				$query->where('regions_id', '=', $params['search_regions_id']);
			}
		}

		//Search By is_del
		if (isset($params['is_del'])) {
			$query->where('is_del', '=', $params['is_del']);
		}

		//Search By creator_id
		if (isset($params['creator_id']) && $params['creator_id'] != '') {
			$query->where('creator_id', '=', $params['creator_id']);
		}

		//Search By last_operator
		if (isset($params['last_operator']) && $params['last_operator'] != '') {
			$query->where('last_operator', '=', $params['last_operator']);
		}

		//Search By createtime
		if (isset($params['createtime']) && $params['createtime'] != '') {
			$query->where('oil_station.createtime', '=', $params['createtime']);
		}

		//Search By updatetime
		if (isset($params['updatetime']) && $params['updatetime'] != '') {
			$query->where('oil_station.updatetime', '=', $params['updatetime']);
		}
		//Search By s_start_time
		if (isset($params['s_start_time']) && $params['s_start_time'] != '') {
			$query->where('oil_station.createtime', '>=', $params['s_start_time']);
		}

		//Search By createtimeLe
		if (isset($params['s_end_time']) && $params['s_end_time'] != '') {
			$query->where('oil_station.createtime', '<=', $params['s_end_time']);
		}

		//Search By s_start_time
		if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
			$query->where('oil_station.createtime', '>=', $params['createtimeGe']);
		}

		//Search By createtimeLe
		if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
			$query->where('oil_station.createtime', '<=', $params['createtimeLe']);
		}

		//Search By updatetimeGe
		if (isset($params['updatetimeGe']) && $params['updatetimeGe'] != '') {
			$query->where('oil_station.updatetime', '>=', $params['updatetimeGe']);
		}

		//Search By updatetimeLe
		if (isset($params['updatetimeLe']) && $params['updatetimeLe'] != '') {
			$query->where('oil_station.updatetime', '<=', $params['updatetimeLe']);
		}

		//Search By updatetimeGe
		if (isset($params['s_start_utime']) && $params['s_start_utime'] != '') {
			$query->where('oil_station.updatetime', '>=', $params['s_start_utime']);
		}

		//Search By station_operators
		if (isset($params['station_operators']) && $params['station_operators'] != '') {
			$query->where('oil_station_operators.operators_name', 'like', '%' . $params['station_operators'] . '%');
		}

		//Search By updatetimeLe
		if (isset($params['s_end_utime']) && $params['s_end_utime'] != '') {
			$query->where('oil_station.updatetime', '<=', $params['s_end_utime']);
		}
		//Search By updatetimeGe
		if (isset($params['updatetimeGe']) && $params['updatetimeGe'] != '') {
			$query->where('oil_station.updatetime', '>=', $params['updatetimeGe']);
		}

		//Search By updatetimeLe
		if (isset($params['updatetimeLe']) && $params['updatetimeLe'] != '') {
			$query->where('oil_station.updatetime', '<=', $params['updatetimeLe']);
		}

		if (isset($params['oil_com_in']) && $params['oil_com_in'] != '') {
			$query->whereIn('oil_com', explode(",", $params['oil_com_in']));
		}

        if (isset($params['first_trade_timeNull']) && $params['first_trade_timeNull'] != '') {
            $query->whereNull('first_trade_time');
        }

        if (isset($params['station_nameNeq']) && $params['station_nameNeq'] != '') {
            $query->whereRaw("station_name is not null and station_name != ''");
        }

        if (isset($params['province_codeNull']) && $params['province_codeNull'] != '') {
            $query->whereRaw("(province_code is null or province_code = '')");
        }

		return $query;
	}

	/**
	 * 油站表 列表查询
	 * @param array $params
	 * @return array
	 */
	static public function getList(array $params)
	{
		$data = [];
		$params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
		$params['page'] = isset($params['page']) ? $params['page'] : 1;
		$sqlObj = OilStation::Filter($params);
		if (isset($params['station_operators']) && $params['station_operators'] != '') {
			$sqlObj->leftjoin("oil_station_operators", 'oil_station_operators.id', '=', 'oil_station.station_operators_id')
				->where("oil_station_operators.status", 1);
		}

		if (isset($params['sortColumns'])) {
			$_sortColumns = explode(" ", $params['sortColumns']);
			$orderField = $_sortColumns[0];
			$orderType = $_sortColumns[1];
		} else {
			$orderField = 'oil_station.createtime';
			$orderType = 'desc';
		}

		if (isset($params['_export']) && $params['_export'] == 1) {
			$data = $sqlObj->orderBy($orderField, $orderType)->get();
		} else {
			$data = $sqlObj->orderBy($orderField, $orderType)->paginate($params['limit'], ['*'], 'page', $params['page']);
		}
		return $data;
	}

	/**
	 * 油站表 详情查询
	 * @param array $params
	 * @return object
	 */
	static public function getById(array $params)
	{
		\helper::argumentCheck(['id'], $params);

		return OilStation::find($params['id']);
	}

	/**
	 * 油站表 新增
	 * @param array $params
	 * @return mixed
	 */
	static public function add(array $params)
	{
		return OilStation::create($params);
	}

	/**
	 * 油站表 编辑
	 * @param array $params
	 * @return mixed
	 */
	static public function edit(array $params)
	{
		\helper::argumentCheck(['id'], $params);

		return OilStation::find($params['id'])->update($params);
	}

	/**
	 * PDO批量修改
	 * @param       $tableName
	 * @param array $apiData
	 * @return bool|int
	 */
	static public function batchEditByPdo($apiData = [])
	{
		//批量入库
		$data = FALSE;
		if ($apiData) {
			$batchSqlArr = [];
			foreach ($apiData as $v) {
				if (!isset($v['where'])) {
					throw new \RuntimeException('where条件缺失', 6);
				}
				$fieldStr = '';
				$where = '';
				foreach ($v as $key => $val) {
					if ($key != 'where') {
						if ($key != 'createtime') {
							if ($val === `NULL`) {
								$fieldStr .= $fieldStr ? ",`" . $key . "`=NULL" : "`" . $key . "`=NULL";
							} else {
								$fieldStr .= $fieldStr ? ",`" . $key . "`='$val'" : "`" . $key . "`='$val'";
							}
						}
					} else
						$where = $val;
				}
				$batchSqlArr[] = "UPDATE oil_station SET $fieldStr WHERE $where";
			}
			$batchSql = implode(";", $batchSqlArr);
			Log::info('$batchSql--' . $batchSql, [], 'updateStationSql');
			$data = Capsule::connection()->getPdo()->exec($batchSql);
		}

		return $data;
	}

	/**
	 * 油站表 根据ids删除或批量删除
	 * @param array $params
	 * @return int
	 */
	static public function remove(array $params)
	{
		\helper::argumentCheck(['ids'], $params);

		return OilStation::destroy($params['ids']);
	}

	/**
	 * 获取最大更新时间
	 * @return string
	 */
	static public function getMaxUpdatetime()
	{
		Capsule::connection()->enableQueryLog();
		$sqlObj = Capsule::connection()->table('oil_station');
		$data = $sqlObj->select(Capsule::connection()->raw("max(updatetime) as max_time"))->whereIn('oil_com', [5, 6, 7, 8])->first();

		return $data->max_time ? $data->max_time : '1900-01-01 00:00:00';
	}

	static public function isExistStationName($station_name)
	{
		return OilStation::where('station_name', '=', $station_name)->first();
	}

	/**
	 * 根据标注级别获取油站数据(油站位置信息解析专用)
	 * @param $stationId
	 * @return mixed
	 * <AUTHOR>
	 */
	static public function getOilStationByLevel($stationId)
	{
		return OilStation::where('id', '>', intval($stationId))->whereIn('level', [4, 5])->whereIn('oil_com', [1, 2])
			->select('id', 'station_name', 'last_trade_id', 'level')->first();
	}

	static public function getStationInfo(array $params, $value = 'id', $key = 'station_name')
	{
		return self::Filter($params)->pluck($value, $key)->toArray();
	}

    static public function getFileds(array $params,$field = 'station_name')
    {
        return self::Filter($params)->pluck($field);
    }

	/**
	 * @title   通过站名获取油站
	 * @desc
	 * @param $name
	 * @return mixed
	 * @returns
	 * mixed
	 * @returns
	 * @package  Models
	 * @since
	 * @params   type filedName required?
	 * @version  1.0.0
	 * <AUTHOR>
	static public function getByName($name)
	{
		$cacheName = __METHOD__ . __FUNCTION__ . 'getByName' . \var_export($name, true);
		$data = Cache::get($cacheName);
		if (!$data) {
			$data = self::where('station_name', trim($name))->first();
			Cache::put($cacheName, $data, 86400);
		}

		return $data;
	}

	static public function batchAdd($data)
	{
		return self::insert($data);
	}

	static public function nameMap()
	{
		return self::whereNotNull("station_name")->whereNull("station_operators_id")->whereIn('oil_com', [20, 21])->orderBy("createtime", "asc")->pluck("id", "station_name")->toArray();
	}

	static public function getByStationCodes($stationCodes)
	{
		return self::whereIn('station_code', $stationCodes)
			->pluck("id", "station_code")
			->toArray();
	}
}