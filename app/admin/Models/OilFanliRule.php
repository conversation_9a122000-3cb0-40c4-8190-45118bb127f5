<?php
/**
 * oil_fanli_rule
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/02/21
 * Time: 09:39:43
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilFanliRule extends \Framework\Database\Model
{
    protected $table = 'oil_fanli_rule';

    protected $guarded = ["id"];
    protected $fillable = ['rule_no','rule_object','rule_name','fanli_way','fanli_type','fanli_coe','fanli_money','coe_unit','fanli_mode','step_fanli_data','add_fanli_edu','level','status','start_time','end_time','oil_amount_limit','oil_money_limit','is_del','createtime','updatetime','creator_name','creator_id'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By id
        if (isset($params['idIn']) && $params['idIn'] != '') {
            $query->whereIn('id', $params['idIn']);
        }

        //Search By rule_no
        if (isset($params['rule_no']) && $params['rule_no'] != '') {
            $query->where('rule_no', '=', $params['rule_no']);
        }

        //Search By rule_object
        if (isset($params['rule_object']) && $params['rule_object'] != '') {
            $query->where('rule_object', '=', $params['rule_object']);
        }

        //Search By rule_name
        if (isset($params['rule_name']) && $params['rule_name'] != '') {
            $query->where('rule_name', '=', $params['rule_name']);
        }

        //Search By rule_nameLk
        if (isset($params['rule_nameLk']) && $params['rule_nameLk'] != '') {
            $query->where('rule_name', 'like', '%'.$params['rule_nameLk'].'%');
        }

        //Search By fanli_way
        if (isset($params['fanli_way']) && $params['fanli_way'] != '') {
            $query->where('fanli_way', '=', $params['fanli_way']);
        }

        //Search By fanli_type
        if (isset($params['fanli_type']) && $params['fanli_type'] != '') {
            $query->where('fanli_type', '=', $params['fanli_type']);
        }

        //Search By fanli_coe
        if (isset($params['fanli_coe']) && $params['fanli_coe'] != '') {
            $query->where('fanli_coe', '=', $params['fanli_coe']);
        }

        //Search By fanli_money
        if (isset($params['fanli_money']) && $params['fanli_money'] != '') {
            $query->where('fanli_money', '=', $params['fanli_money']);
        }

        //Search By coe_unit
        if (isset($params['coe_unit']) && $params['coe_unit'] != '') {
            $query->where('coe_unit', '=', $params['coe_unit']);
        }

        //Search By fanli_mode
        if (isset($params['fanli_mode']) && $params['fanli_mode'] != '') {
            $query->where('fanli_mode', '=', $params['fanli_mode']);
        }

        //Search By step_fanli_data
        if (isset($params['step_fanli_data']) && $params['step_fanli_data'] != '') {
            $query->where('step_fanli_data', '=', $params['step_fanli_data']);
        }

        //Search By add_fanli_edu
        if (isset($params['add_fanli_edu']) && $params['add_fanli_edu'] != '') {
            $query->where('add_fanli_edu', '=', $params['add_fanli_edu']);
        }

        //Search By level
        if (isset($params['level']) && $params['level'] != '') {
            $query->where('level', '=', $params['level']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '' ) {
            $query->where('status', '=', $params['status']);
        }

        //Search By start_time
        if (isset($params['start_time']) && $params['start_time'] != '') {
            $query->where('start_time', '=', $params['start_time']);
        }

        //Search By end_time
        if (isset($params['end_time']) && $params['end_time'] != '') {
            $query->where('end_time', '=', $params['end_time']);
        }

        //Search By end_time
        /*if (isset($params['expire_start']) && $params['expire_start'] != '') {
            $query->where('start_time', '>=', $params['expire_start']." 00:00:00");
        }

        //Search By end_time
        if (isset($params['expire_end']) && $params['expire_end'] != '') {
            $query->where('end_time', '<=', $params['expire_end']." 23:59:59");
        }*/
        if (isset($params['expire_end']) && $params['expire_end'] != '' && isset($params['expire_start']) && $params['expire_start'] != '') {
            //$query->where('end_time', '>=', $params['expire_end']." 23:59:59")->where('start_time', '>=', $params['expire_start']." 00:00:00");
            //$query->orWhere('start_time', '>=', $params['expire_start']." 00:00:00")->where('end_time', '>=', $params['expire_end']." 23:59:59");
            //$query->orWhere('start_time', '>=', $params['expire_start']." 00:00:00")->where('end_time', '<=', $params['expire_end']." 23:59:59");
            $query->orWhere(function($query) use($params){
                $query->Where('start_time', '>=', $params['expire_start']." 00:00:00")->where('end_time', '>=', $params['expire_end']." 23:59:59");
            });
            $query->orWhere(function($query) use($params){
                $query->Where('start_time', '>=', $params['expire_start']." 00:00:00")->where('end_time', '<=', $params['expire_end']." 23:59:59");
            });
            $query->orWhere(function($query) use($params){
                $query->where('end_time', '>=', $params['expire_end']." 23:59:59")->where('start_time', '<=', $params['expire_start']." 00:00:00");
            });
        }

        //Search By oil_amount_limit
        if (isset($params['oil_amount_limit']) && $params['oil_amount_limit'] != '') {
            $query->where('oil_amount_limit', '=', $params['oil_amount_limit']);
        }

        //Search By oil_money_limit
        if (isset($params['oil_money_limit']) && $params['oil_money_limit'] != '') {
            $query->where('oil_money_limit', '=', $params['oil_money_limit']);
        }

        //Search By is_del
        if (isset($params['is_del']) && ($params['is_del'] != '' || $params['is_del'] >= 0)) {
            $query->where('is_del', '=', $params['is_del']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By creator_name
        if (isset($params['creator_name']) && $params['creator_name'] != '') {
            $query->where('creator_name', '=', $params['creator_name']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        return $query;
    }

    /**
     * oil_fanli_rule 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        Capsule::connection()->enableQueryLog();
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilFanliRule::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('createtime', 'desc')->get();
        }elseif(isset($params['_count']) && $params['_count'] == 1){
            return $sqlObj->count();
        }elseif (isset($params['skip']) && isset($params['take'])) {
            $data = $sqlObj->orderBy('createtime', 'desc')->skip(intval($params['skip']))->take(intval($params['take']))->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }
        $ss = Capsule::connection()->getQueryLog();
        //print_r($ss);exit;
        foreach ($data as $key => &$val){
            $condition['_count'] = 1;
            $condition['module_name'] = "oil_fanli_rule";
            $condition["third_id"] = $val->id;
            switch ($val->status){
                case 1:
                    $val->status_txt = "生效";
                    break;
                case 2:
                    $val->status_txt = "失效";
                    break;
                default:
                    $val->status_txt = "待审核";
            }
            $val->way_txt = $val->fanli_way == 2 ? "积分" : "现金";
            $val->object_txt = $val->rule_object == 2 ? "分配记录" : "消费记录";
            $val->expire_time = substr($val->start_time,0,10).'至'.substr($val->end_time,0,10);
            $val->log_num = OilSysOperatorLogs::getList($condition);;
        }

        return $data;
    }

    /**
     * oil_fanli_rule 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilFanliRule::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilFanliRule::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_fanli_rule 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilFanliRule::create($params);
    }

    /**
     * oil_fanli_rule 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilFanliRule::find($params['id'])->update($params);
    }

    /**
     * oil_fanli_rule 根据id批量编辑
     * @param array $params
     * @return mixed
     */
    static public function batchEdit(array $params,$condition)
    {
        return OilFanliRule::whereIn("id",$condition)->update($params);
    }


    /**
     * oil_fanli_rule 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilFanliRule::destroy($params['ids']);
    }

    /**
     * oil_fanli_rule 获取关联的机构
     * @param array $params
     * @return int
     */
    static public function getOrgList(array $params)
    {
        //Capsule::connection()->enableQueryLog();
        $org = self::Filter($params)
            ->leftJoin('oil_org_rule_relations','oil_fanli_rule.rule_no','=','oil_org_rule_relations.rule_no')
            ->leftJoin("oil_org","oil_org.id",'=','oil_org_rule_relations.org_id')
            ->where("oil_fanli_rule.id",$params['rule_id'])
            ->select("oil_org.orgcode","oil_org.org_name","oil_org_rule_relations.org_id")
            ->first();
        //$sql = Capsule::connection()->getQueryLog();

        if(count($org) == 0){
            return "-";
        }
        if($org->org_id == -1){
            return "全部";
        }else{
            return $org->orgcode." ".$org->org_name;
        }
    }

    /**
     * @title 生成返利单号
     * <AUTHOR>
     * @return string
     */
    static public function createOrderNo()
    {
        $order_no = 'FL'.date('ymd');
        $fanli_no   = self::where('fanli_no','like',$order_no.'%')->max('fanli_no');
        if($fanli_no){
            $order_no .= sprintf("%04d",(substr($fanli_no,-4) + 1));
        }else{
            $order_no .= '0001';
        }

        return $order_no;
    }

    /**
     * @title 生成返利单号
     * <AUTHOR>
     * @return string
     */
    static public function getLastNo()
    {
        return self::max('rule_no');
    }

    /**
     * @title 生成返利单号
     * <AUTHOR>
     * @return string
     */
    static public function getInfoByName($name)
    {
        return self::where('rule_name',$name)->where("is_del",0)->first();
    }

    /**
     * @title 检查返利是否存在
     * <AUTHOR>
     * @return string
     */
    static public function checkExist(array $params)
    {
        $detail = $params['detail'];
        Capsule::connection()->enableQueryLog();
        $data = self::where('oil_fanli_rule.is_del', 0)
            ->leftjoin("oil_fanli_rule_details","oil_fanli_rule_details.rule_id","=","oil_fanli_rule.id")
            ->where('oil_fanli_rule.rule_object', $params['rule_object'])
            ->where('oil_fanli_rule_details.oil_com', $detail['oil_com'])
            ->where('oil_fanli_rule_details.oil_vice_provider', $detail['oil_vice_provider'])
            ->where('oil_fanli_rule_details.station_opreater_id', $detail['station_opreater_id'])
            ->where('oil_fanli_rule_details.station_id', $detail['station_id'])
            ->where('oil_fanli_rule_details.region_id', $detail['region_id'])
            ->where('oil_fanli_rule.start_time', '<=', $params['end_time'])
            ->where('oil_fanli_rule.end_time', '>=', $params['start_time'])
            ->where('oil_fanli_rule_details.oil_type', $detail['oil_type'])
            ->where('oil_fanli_rule_details.main_id', $detail['main_id'])
            ->pluck('oil_fanli_rule.id')->toArray();
        $sss = Capsule::connection()->getQueryLog();
        //print_r($sss);exit;
        return $data;
    }

    /**
     * 油卡返利政策 获取特殊返利规则
     * @param array $params
     * @return array
     */
    static public function getSpecialRule(array $ruleNo)
    {
        //Capsule::connection()->enableQueryLog();
        $data = self::where("oil_fanli_rule.rule_object",'=',1)->where("oil_fanli_rule.is_del",'=',0)->whereIn("oil_fanli_rule.rule_no",$ruleNo)
            ->leftJoin('oil_fanli_rule_details','oil_fanli_rule.id','=','oil_fanli_rule_details.rule_id')
            ->leftJoin('oil_org_rule_relations','oil_fanli_rule.rule_no','=','oil_org_rule_relations.rule_no')
            ->select('oil_org_rule_relations.org_id','oil_fanli_rule.rule_name','oil_fanli_rule.rule_no','oil_fanli_rule.fanli_way','oil_fanli_rule.fanli_type',
                'oil_fanli_rule.fanli_coe','oil_fanli_rule.start_time','oil_fanli_rule.end_time','oil_fanli_rule.step_fanli_data',"oil_fanli_rule.oil_amount_limit","oil_fanli_rule.oil_money_limit",
                'oil_fanli_rule.fanli_money','oil_fanli_rule.coe_unit','oil_fanli_rule.add_fanli_edu','oil_fanli_rule.fanli_mode','oil_fanli_rule.start_time','oil_fanli_rule.end_time','oil_fanli_rule_details.*')
            ->orderBy('oil_fanli_rule.level', 'desc')->get();
        //$sssql = Capsule::connection()->getQueryLog();
        return $data;
    }

}