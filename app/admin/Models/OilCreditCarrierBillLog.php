<?php
/**
 * 峰松授信账单状态日志
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/10/23
 * Time: 15:29:16
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCreditCarrierBillLog extends \Framework\Database\Model
{
    protected $table = 'oil_credit_carrier_bill_log';

    protected $guarded = ["id"];
    protected $fillable = ['bill_id','status','createtime','operator_id','operator_name','money','remark'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }
        if (isset($params['bill_id']) && $params['bill_id'] != '') {
            $query->where('bill_id', '=', $params['bill_id']);
        }


        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By operator_id
        if (isset($params['operator_id']) && $params['operator_id'] != '') {
            $query->where('operator_id', '=', $params['operator_id']);
        }

        //Search By operator_name
        if (isset($params['operator_name']) && $params['operator_name'] != '') {
            $query->where('operator_name', '=', $params['operator_name']);
        }

        return $query;
    }

    /**
     * 峰松授信账单 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCreditCarrierBillLog::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('createtime', 'desc')->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 峰松授信账单 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCreditCarrierBillLog::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCreditCarrierBillLog::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 峰松授信账单 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        global $app;
        $params['createtime'] = \helper::nowTime();
        $params['updatetime'] = \helper::nowTime();
        $params['operator_id'] = $app->myAdmin->id;
        $params['operator_name'] = $app->myAdmin->true_name;
        return OilCreditCarrierBillLog::create($params);
    }

    /**
     * 峰松授信账单 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCreditCarrierBillLog::find($params['id'])->update($params);
    }

    /**
     * 峰松授信账单 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCreditCarrierBillLog::destroy($params['ids']);
    }




}