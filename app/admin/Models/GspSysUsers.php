<?php
/**
 * 用户表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:17
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class GspSysUsers extends \Framework\Database\Model
{
    protected $table = 'gsp_sys_users';

    protected $guarded = ["id"];

    protected $fillable = ['old_id','user_code','user_name','org_id','true_name','user_email','locked_time',
        'user_mobile','user_pwd','last_login_time','creator_id','last_operator','remark','createtime',
        'updatetime','is_admin','status','to_k3_status','k3_id','is_del','unlocked_time'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function roles()
    {
        return $this->hasMany('Models\GspSysUserRoles','user_id','id');
    }

    public function organ()
    {
        return $this->belongsTo('Models\GspSysOrgs','org_id','id');
    }


    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By old_id
        if (isset($params['old_id']) && $params['old_id'] != '') {
            $query->where('old_id', '=', $params['old_id']);
        }

        //Search By user_code
        if (isset($params['user_code']) && $params['user_code'] != '') {
            $query->where('user_code', '=', $params['user_code']);
        }

        //Search By user_name
        if (isset($params['user_name']) && $params['user_name'] != '') {
            $query->where('user_name', '=', $params['user_name']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By true_name
        if (isset($params['true_name']) && $params['true_name'] != '') {
            $query->where('true_name', '=', $params['true_name']);
        }

        if (isset($params['true_name_lk']) && $params['true_name_lk'] != '') {
            $query->where('true_name', 'like', "%".$params['true_name_lk']."%");
        }

        //Search By user_email
        if (isset($params['user_email']) && $params['user_email'] != '') {
            $query->where('user_email', '=', $params['user_email']);
        }

        //Search By user_mobile
        if (isset($params['user_mobile']) && $params['user_mobile'] != '') {
            $query->where('user_mobile', '=', $params['user_mobile']);
        }

        //Search By user_pwd
        if (isset($params['user_pwd']) && $params['user_pwd'] != '') {
            $query->where('user_pwd', '=', $params['user_pwd']);
        }

        //Search By last_login_time
        if (isset($params['last_login_time']) && $params['last_login_time'] != '') {
            $query->where('last_login_time', '=', $params['last_login_time']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By is_admin
        if (isset($params['is_admin']) && $params['is_admin'] != '') {
            $query->where('is_admin', '=', $params['is_admin']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By to_k3_status
        if (isset($params['to_k3_status']) && $params['to_k3_status'] != '') {
            $query->where('to_k3_status', '=', $params['to_k3_status']);
        }

        //Search By k3_id
        if (isset($params['k3_id']) && $params['k3_id'] != '') {
            $query->where('k3_id', '=', $params['k3_id']);
        }

        //Search By is_del
        if (isset($params['is_del']) && $params['is_del'] != '') {
            $query->where('is_del', '=', $params['is_del']);
        }


        return $query;
    }

    /**
     * 用户表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $sqlObj = GspSysUsers::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 用户表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspSysUsers::find($params['id']);
    }

    /**
     * 获取$key=>$value一维数组
     * @param array $userName
     * @param string $value
     * @param string $key
     * @return mixed
     */
    static public function getByTrueNameMap(array $userName,$value='id',$key='true_name')
    {
        return GspSysUsers::whereIn('true_name',$userName)->lists($value,$key)->toArray();
    }

    static public function getByTrueName($trueName)
    {
        return GspSysUsers::where('true_name', trim($trueName))->first();
    }

    /**
     * 获取$key=>$value一维数组
     * @param array $userId
     * @param string $value
     * @param string $key
     * @return mixed
     */
    static public function getByIdMap(array $userId,$value='true_name',$key='id')
    {
        return GspSysUsers::whereIn('id',$userId)->lists($value,$key)->toArray();
    }

    /**
     * 用户表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return GspSysUsers::create($params);
    }

    /**
     * 用户表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspSysUsers::find($params['id'])->update($params);
    }

    /**
     * 用户表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return GspSysUsers::destroy($params['ids']);
    }

    /**
     * @param $userName
     * @return mixed
     */
    static public function getByUserName($userName)
    {
        return GspSysUsers::where('user_name','=',$userName)->where('status','=',1)->first();
    }

    /**
     * 根据用户ID获取用户、角色、机构信息
     * @param $userId
     * @return mixed
     */
    static public function getUserDetailByUserId($userId)
    {
        $userInfo = GspSysUsers::select('id','user_name','true_name', 'org_id','user_email','user_type','operator_id')->where('id','=',$userId)
            ->with([
                'roles' => function($query){
                    $query->select('user_id','role_id');
                },
                'organ'   => function($query){
                    $query->select('id','org_code','org_name');
                }
            ])
            ->where('is_del','=','0')
            ->first()
            ->toArray();

        if($userInfo && $userInfo['roles']){
            $roles = [];
            foreach($userInfo['roles'] as $v){
                $roles[] = $v['role_id'];
            }

            $userInfo['roles'] = $roles;
        }

        return $userInfo;
    }
}
