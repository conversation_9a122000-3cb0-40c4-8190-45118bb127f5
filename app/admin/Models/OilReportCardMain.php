<?php
/**
 * 主卡总账（每日）
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:19
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilReportCardMain extends \Framework\Database\Model
{
    protected $table = 'oil_report_card_main';

    protected $guarded = ["id"];

    protected $fillable = ['day_no','main_id','charge_total','fanli_money_total','fanli_jifen_total','assign_money_total','oil_num_total','oil_expend_total','createtime','updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By day_no
        if (isset($params['day_no']) && $params['day_no'] != '') {
            $query->where('day_no', '=', $params['day_no']);
        }

        //Search By main_id
        if (isset($params['main_id']) && $params['main_id'] != '') {
            $query->where('main_id', '=', $params['main_id']);
        }

        //Search By charge_total
        if (isset($params['charge_total']) && $params['charge_total'] != '') {
            $query->where('charge_total', '=', $params['charge_total']);
        }

        //Search By fanli_money_total
        if (isset($params['fanli_money_total']) && $params['fanli_money_total'] != '') {
            $query->where('fanli_money_total', '=', $params['fanli_money_total']);
        }

        //Search By fanli_jifen_total
        if (isset($params['fanli_jifen_total']) && $params['fanli_jifen_total'] != '') {
            $query->where('fanli_jifen_total', '=', $params['fanli_jifen_total']);
        }

        //Search By assign_money_total
        if (isset($params['assign_money_total']) && $params['assign_money_total'] != '') {
            $query->where('assign_money_total', '=', $params['assign_money_total']);
        }

        //Search By oil_num_total
        if (isset($params['oil_num_total']) && $params['oil_num_total'] != '') {
            $query->where('oil_num_total', '=', $params['oil_num_total']);
        }

        //Search By oil_expend_total
        if (isset($params['oil_expend_total']) && $params['oil_expend_total'] != '') {
            $query->where('oil_expend_total', '=', $params['oil_expend_total']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }


        return $query;
    }

    /**
     * 主卡总账（每日） 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilReportCardMain::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 主卡总账（每日） 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReportCardMain::find($params['id']);
    }

    /**
     * 主卡总账（每日） 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilReportCardMain::create($params);
    }

    /**
     * 主卡总账（每日） 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReportCardMain::find($params['id'])->update($params);
    }

    /**
     * 主卡总账（每日） 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilReportCardMain::destroy($params['ids']);
    }




}