<?php
/**
 * 定时任务监控
 *
 * <AUTHOR>
 * @datetime 2023-01-19 15:24:01
 */
namespace Models;
class OilCronMonitor extends \Framework\Database\Model
{
    protected $table = 'oil_cron_monitor';

    protected $guarded = ["id"];

    protected $fillable = ['event_name','run_type','oid','cron_note','min_hour','max_hour','remark','createtime', 'updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }


    /**
     * Desc:获取要监控的列表
     *  ---------------------------------------------
     * @return mixed
     *  ---------------------------------------------
     * <AUTHOR>
     * @DateTime: 2023-01-19 15:28
     */
    static public function getList()
    {
        return  OilCronMonitor::where('status','=','1')->get()->toArray();
    }

    /**
     * Desc:获取指定的事件信息
     *  ---------------------------------------------
     * @param array $params
     * @return mixed
     *  ---------------------------------------------
     * <AUTHOR>
     * @DateTime: 2023-01-19 15:29
     */
    static public function getMonitorByType(array $params)
    {
        \helper::argumentCheck(['event_name'],$params);

        return  OilCronMonitor::where('event_name','=',$params['event_name'])->first();
    }

    /**
     * Desc:自动增加报警
     *  ---------------------------------------------
     * @param array $params
     * @return OilCronMonitor
     *  ---------------------------------------------
     * <AUTHOR>
     * @DateTime: 2023-01-19 15:29
     */
    static public function add(array $params)
    {
        \helper::argumentCheck(['event_name'],$params);
//        $params['created_at'] = \helper::nowTime();
//        $params['updated_at'] = \helper::nowTime();
        return OilCronMonitor::create($params);
    }

    /**
     * Desc:修改信息
     *  ---------------------------------------------
     * @param array $params
     * @return mixed
     *  ---------------------------------------------
     * <AUTHOR>
     * @DateTime: 2023-01-19 15:31
     */
    static public function edit(array $params,$run_time = false)
    {
        \helper::argumentCheck(['event_name'],$params);
        $params['updatetime'] = \helper::nowTime();
        if($run_time) {
            $params['cron_note'] = self::cronRunTime();
        }
        return OilCronMonitor::where('event_name',$params['event_name'])->update($params);
    }


    static  public  function cronRunTime() {
        global $timeStart;
        $endTime = self::_getTime();
        $useTime = round($endTime - $timeStart, 4);
        $useMem = round(memory_get_usage(TRUE)/1024/1024,2).'M';
        return 'run_time:'.$useTime.'s,use_mem:'.$useMem.',GET-params:'.json_encode($_GET);
    }

    static  public  function _getTime()
    {
        list($usec, $sec) = explode(" ", microtime());

        return ((float)$usec + (float)$sec);
    }

}