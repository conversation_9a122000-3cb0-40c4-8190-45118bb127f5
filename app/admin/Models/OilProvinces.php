<?php
/**
 * 省份表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:18
 */

namespace Models;

use Framework\Cache;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilProvinces extends \Framework\Database\Model
{
	protected $table = 'oil_provinces';
	
	protected $guarded = ["id"];
	
	protected $fillable = ['code', 'province', 'createtime', 'updatetime'];
	
	public function getFillAble()
	{
		return $this->fillable;
	}
	
	
	/**
	 * 聚集查询
	 * @param $query
	 * @param $params
	 * @return $query
	 */
	public function scopeFilter($query, $params)
	{
		
		//Search By id
		if (isset($params['id']) && $params['id'] != '') {
			$query->where('id', '=', $params['id']);
		}
		
		//Search By code
		if (isset($params['code']) && $params['code'] != '') {
			$query->where('code', '=', $params['code']);
		}
		
		//Search By province
		if (isset($params['province']) && $params['province'] != '') {
			$query->where('province', '=', $params['province']);
		}
		
		//Search By createtime
		if (isset($params['createtime']) && $params['createtime'] != '') {
			$query->where('createtime', '=', $params['createtime']);
		}
		
		//Search By updatetime
		if (isset($params['updatetime']) && $params['updatetime'] != '') {
			$query->where('updatetime', '=', $params['updatetime']);
		}
		
		
		return $query;
	}
	
	/**
	 * 省份表 列表查询
	 * @param array $params
	 * @return array
	 */
	static public function getList(array $params)
	{
		$data = [];
		$params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
		$params['page'] = isset($params['page']) ? $params['page'] : 1;
		$sqlObj = OilProvinces::Filter($params);
		if (isset($params['_export']) && $params['_export'] == 1) {
			$data = $sqlObj->get();
		} else {
			$data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
		}
		
		return $data;
	}
	
	/**
	 * 省份表 详情查询
	 * @param array $params
	 * @return object
	 */
	static public function getById(array $params)
	{
		\helper::argumentCheck(['id'], $params);
		
		return OilProvinces::find($params['id']);
	}
	
	/**
	 * 省份表 新增
	 * @param array $params
	 * @return mixed
	 */
	static public function add(array $params)
	{
		return OilProvinces::create($params);
	}
	
	/**
	 * 省份表 编辑
	 * @param array $params
	 * @return mixed
	 */
	static public function edit(array $params)
	{
		\helper::argumentCheck(['id'], $params);
		
		return OilProvinces::find($params['id'])->update($params);
	}
	
	static public function getProvince()
	{
		$oilProvince = OilProvinces::select('id', 'province')->get();
		$data = [];
		foreach ($oilProvince as $v) {
			$data[$v->id] = $v->province;
		}
		return $data;
	}
	
	/**
	 * @title 获取单条记录
	 * @desc
	 * @param array $params
	 * @return mixed
	 * @returns
	 * []
	 * @returns
	 * @package Models
	 * @since
	 * @params  type filedName required?
	 * @version
	 * @level 1
	 * <AUTHOR>
	 */
	static public function getSingleRecord(array $params)
	{
		return self::Filter($params)->first();
	}
	
	/**
	 * 省份表 根据ids删除或批量删除
	 * @param array $params
	 * @return int
	 */
	static public function remove(array $params)
	{
		\helper::argumentCheck(['ids'], $params);
		
		return OilProvinces::destroy($params['ids']);
	}
	
	/**
	 * @title 根据省份名称获取ID
	 * @param $province
	 * @return mixed
	 * <AUTHOR>
	 */
	static public function getByProvince($province)
	{
		return self::where('province', $province)->first();
	}
	
	/**
	 * @title   获取省市键值对
	 * @desc
	 * @return mixed
	 * @returns
	 * mixed
	 * @returns
	 * <AUTHOR> @package  Models
	 * @since
	 * @params   type filedName required?
	 * @version  1.0.0
	 */
	static public function fetchAll()
	{
		$cacheName = \var_export('CITY_' . __CLASS__ . __METHOD__ . __FUNCTION__, true);
		$data = Cache::get($cacheName);
		if (!$data) {
			$data = OilProvinces::lists('province', 'id', 'code')->toArray();
			Cache::put($cacheName, $data, 86400 * 31);
		}
		
		return $data;
	}
	
	/**
	 * @title   获取省市键值对
	 * @desc
	 * @return mixed
	 * @returns
	 * mixed
	 * @returns
	 * <AUTHOR> @package  Models
	 * @since
	 * @params   type filedName required?
	 * @version  1.0.0
	 */
	static public function getAll()
	{
		$cacheName = \var_export('Provice_' . __CLASS__ . __METHOD__ . __FUNCTION__, true);
		$newData = Cache::get($cacheName);
		if (!$newData) {
			$data = OilProvinces::select('province', 'id', 'code')->get()->toArray();
			foreach ($data as $_val) {
				$newData[$_val['province']] = array("id" => $_val['id'], "code" => $_val['code']);
			}
			Cache::put($cacheName, $newData, 86400 * 31);
		}
		
		return $newData;
	}
	
	public static function getByCode($code)
	{
		$cacheName = \var_export('Provice_' . __METHOD__ . $code, true);
		$data = Cache::get($cacheName);
		if (!$data) {
			$data = self::where('code', $code)->first();
			Cache::put($cacheName, $data, 86400 * 31);
		}
		return $data;
	}

	/*
	 * 根据中文字模糊匹配
	 */
	public static function getByProvinceNameLk($keyWord)
    {
        return self::where('province','like',$keyWord.'%')->first();
    }

    /**
     * 省份列表
     */
    public static function getProviceMap($isMap = false)
    {
        $result = [];
        $result = Cache::get("allProvice-getProvices");
        if(empty($result) || count($result) < 2) {
            $data = OilProvinces::select('province', 'id', 'code')->get();
            foreach ($data as $_item) {
//                if (empty($_item->code) || in_array($_item->code, ['000000', '710000', '810000', '820000', '100000', '321000'])) {
                if (empty($_item->code) || in_array($_item->code, ['000000', '710000', '810000', '820000', '321000'])) { // 不能排除龙禹
                    continue;
                }
                $result[$_item->code] = ['code' => $_item->code, 'name' => $_item->province];
            }
            Cache::put("allProvice-getProvices",$result,60*60*5);
        }
        if($isMap){
            return $result;
        }else {
            return array_values($result);
        }
    }
}