<?php
/**
 * 预分发工单
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2017/12/25
 * Time: 11:50:40
 */
namespace Models;
use Framework\Helper;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilPreDispatch extends \Framework\Database\Model
{
    protected $table = 'oil_pre_dispatch';

    protected $guarded = ["id"];
    protected $fillable = ['type','sn','app_id','app_time','status','message', 'time_range', 'ext_data', 'createtime','updatetime','org_id'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By type
        if (isset($params['type']) && $params['type'] != '') {
            $query->where('type', '=', $params['type']);
        }

        //Search By sn
        if (isset($params['sn']) && $params['sn'] != '') {
            $query->where('sn', '=', $params['sn']);
        }

        //Search By app_id
        if (isset($params['app_id']) && $params['app_id'] != '') {
            $query->where('app_id', '=', $params['app_id']);
        }

        //Search By app_time
        if (isset($params['app_time']) && $params['app_time'] != '') {
            $query->where('app_time', '=', $params['app_time']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 预分发工单 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilPreDispatch::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 预分发工单 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilPreDispatch::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilPreDispatch::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 预分发工单 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        $params['sn'] =Helper::uuid();

        return OilPreDispatch::create($params);
    }

    /**
     * 预分发工单 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilPreDispatch::find($params['id'])->update($params);
    }

    /**
     * 预分发工单 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilPreDispatch::destroy($params['ids']);
    }




}