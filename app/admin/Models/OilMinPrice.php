<?php
/**
 * 免惠最低价
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/06/05
 * Time: 15:52:32
 */

namespace Models;

use Illuminate\Database\Capsule\Manager as Capsule;

class OilMinPrice extends \Framework\Database\Model
{
    protected $table = 'oil_min_price';
    
    protected $guarded  = ["id"];
    protected $fillable = ['sync_date', 'beijing', 'beijing_diff', 'tianjin', 'tianjin_diff', 'hebei', 'hebei_diff', 'shanxi', 'shanxi_diff', 'henan', 'henan_diff', 'shandong', 'shandong_diff', 'shanghai', 'shanghai_diff', 'jiangsu', 'jiangsu_diff', 'zhejiang', 'zhejiang_diff', 'fujian', 'fujian_diff', 'anhui', 'anhui_diff', 'jiangxi', 'jiangxi_diff', 'hubei', 'hubei_diff', 'hunan', 'hunan_diff', 'guangdong', 'guangdong_diff', 'guangxi', 'guangxi_diff', 'guizhou', 'guizhou_diff', 'yunnan', 'yunnan_diff', 'hainan', 'hainan_diff', 'heilongjiang', 'heilongjiang_diff', 'jilin', 'jilin_diff', 'liaoning', 'liaoning_diff', 'shanxi2', 'shanxi2_diff', 'xinjiang', 'xinjiang_diff', 'neimeng', 'neimeng_diff', 'qinghai', 'qinghai_diff', 'ningxia', 'ningxia_diff', 'gansu', 'gansu_diff', 'sichuan', 'sichuan_diff', 'chongqing', 'chongqing_diff', 'xizang', 'xizang_diff', 'createtime', 'updatetime'];
    
    public function getFillAble()
    {
        return $this->fillable;
    }
    
    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By sync_date
        if (isset($params['sync_date']) && $params['sync_date'] != '') {
            $query->where('sync_date', '=', $params['sync_date']);
        }
    
        if (isset($params['sync_date_ge']) && $params['sync_date_ge'] != '') {
            $query->where('sync_date', '>=', $params['sync_date_ge']);
        }
    
        if (isset($params['sync_date_le']) && $params['sync_date_le'] != '') {
            $query->where('sync_date', '<=', $params['sync_date_le']);
        }
        
        //Search By beijing
        if (isset($params['beijing']) && $params['beijing'] != '') {
            $query->where('beijing', '=', $params['beijing']);
        }
        
        //Search By beijing_diff
        if (isset($params['beijing_diff']) && $params['beijing_diff'] != '') {
            $query->where('beijing_diff', '=', $params['beijing_diff']);
        }
        
        //Search By tianjin
        if (isset($params['tianjin']) && $params['tianjin'] != '') {
            $query->where('tianjin', '=', $params['tianjin']);
        }
        
        //Search By tianjin_diff
        if (isset($params['tianjin_diff']) && $params['tianjin_diff'] != '') {
            $query->where('tianjin_diff', '=', $params['tianjin_diff']);
        }
        
        //Search By hebei
        if (isset($params['hebei']) && $params['hebei'] != '') {
            $query->where('hebei', '=', $params['hebei']);
        }
        
        //Search By hebei_diff
        if (isset($params['hebei_diff']) && $params['hebei_diff'] != '') {
            $query->where('hebei_diff', '=', $params['hebei_diff']);
        }
        
        //Search By shanxi
        if (isset($params['shanxi']) && $params['shanxi'] != '') {
            $query->where('shanxi', '=', $params['shanxi']);
        }
        
        //Search By shanxi_diff
        if (isset($params['shanxi_diff']) && $params['shanxi_diff'] != '') {
            $query->where('shanxi_diff', '=', $params['shanxi_diff']);
        }
        
        //Search By henan
        if (isset($params['henan']) && $params['henan'] != '') {
            $query->where('henan', '=', $params['henan']);
        }
        
        //Search By henan_diff
        if (isset($params['henan_diff']) && $params['henan_diff'] != '') {
            $query->where('henan_diff', '=', $params['henan_diff']);
        }
        
        //Search By shandong
        if (isset($params['shandong']) && $params['shandong'] != '') {
            $query->where('shandong', '=', $params['shandong']);
        }
        
        //Search By shandong_diff
        if (isset($params['shandong_diff']) && $params['shandong_diff'] != '') {
            $query->where('shandong_diff', '=', $params['shandong_diff']);
        }
        
        //Search By shanghai
        if (isset($params['shanghai']) && $params['shanghai'] != '') {
            $query->where('shanghai', '=', $params['shanghai']);
        }
        
        //Search By shanghai_diff
        if (isset($params['shanghai_diff']) && $params['shanghai_diff'] != '') {
            $query->where('shanghai_diff', '=', $params['shanghai_diff']);
        }
        
        //Search By jiangsu
        if (isset($params['jiangsu']) && $params['jiangsu'] != '') {
            $query->where('jiangsu', '=', $params['jiangsu']);
        }
        
        //Search By jiangsu_diff
        if (isset($params['jiangsu_diff']) && $params['jiangsu_diff'] != '') {
            $query->where('jiangsu_diff', '=', $params['jiangsu_diff']);
        }
        
        //Search By zhejiang
        if (isset($params['zhejiang']) && $params['zhejiang'] != '') {
            $query->where('zhejiang', '=', $params['zhejiang']);
        }
        
        //Search By zhejiang_diff
        if (isset($params['zhejiang_diff']) && $params['zhejiang_diff'] != '') {
            $query->where('zhejiang_diff', '=', $params['zhejiang_diff']);
        }
        
        //Search By fujian
        if (isset($params['fujian']) && $params['fujian'] != '') {
            $query->where('fujian', '=', $params['fujian']);
        }
        
        //Search By fujian_diff
        if (isset($params['fujian_diff']) && $params['fujian_diff'] != '') {
            $query->where('fujian_diff', '=', $params['fujian_diff']);
        }
        
        //Search By anhui
        if (isset($params['anhui']) && $params['anhui'] != '') {
            $query->where('anhui', '=', $params['anhui']);
        }
        
        //Search By anhui_diff
        if (isset($params['anhui_diff']) && $params['anhui_diff'] != '') {
            $query->where('anhui_diff', '=', $params['anhui_diff']);
        }
        
        //Search By jiangxi
        if (isset($params['jiangxi']) && $params['jiangxi'] != '') {
            $query->where('jiangxi', '=', $params['jiangxi']);
        }
        
        //Search By jiangxi_diff
        if (isset($params['jiangxi_diff']) && $params['jiangxi_diff'] != '') {
            $query->where('jiangxi_diff', '=', $params['jiangxi_diff']);
        }
        
        //Search By hubei
        if (isset($params['hubei']) && $params['hubei'] != '') {
            $query->where('hubei', '=', $params['hubei']);
        }
        
        //Search By hubei_diff
        if (isset($params['hubei_diff']) && $params['hubei_diff'] != '') {
            $query->where('hubei_diff', '=', $params['hubei_diff']);
        }
        
        //Search By hunan
        if (isset($params['hunan']) && $params['hunan'] != '') {
            $query->where('hunan', '=', $params['hunan']);
        }
        
        //Search By hunan_diff
        if (isset($params['hunan_diff']) && $params['hunan_diff'] != '') {
            $query->where('hunan_diff', '=', $params['hunan_diff']);
        }
        
        //Search By guangdong
        if (isset($params['guangdong']) && $params['guangdong'] != '') {
            $query->where('guangdong', '=', $params['guangdong']);
        }
        
        //Search By guangdong_diff
        if (isset($params['guangdong_diff']) && $params['guangdong_diff'] != '') {
            $query->where('guangdong_diff', '=', $params['guangdong_diff']);
        }
        
        //Search By guangxi
        if (isset($params['guangxi']) && $params['guangxi'] != '') {
            $query->where('guangxi', '=', $params['guangxi']);
        }
        
        //Search By guangxi_diff
        if (isset($params['guangxi_diff']) && $params['guangxi_diff'] != '') {
            $query->where('guangxi_diff', '=', $params['guangxi_diff']);
        }
        
        //Search By guizhou
        if (isset($params['guizhou']) && $params['guizhou'] != '') {
            $query->where('guizhou', '=', $params['guizhou']);
        }
        
        //Search By guizhou_diff
        if (isset($params['guizhou_diff']) && $params['guizhou_diff'] != '') {
            $query->where('guizhou_diff', '=', $params['guizhou_diff']);
        }
        
        //Search By yunnan
        if (isset($params['yunnan']) && $params['yunnan'] != '') {
            $query->where('yunnan', '=', $params['yunnan']);
        }
        
        //Search By yunnan_diff
        if (isset($params['yunnan_diff']) && $params['yunnan_diff'] != '') {
            $query->where('yunnan_diff', '=', $params['yunnan_diff']);
        }
        
        //Search By hainan
        if (isset($params['hainan']) && $params['hainan'] != '') {
            $query->where('hainan', '=', $params['hainan']);
        }
        
        //Search By hainan_diff
        if (isset($params['hainan_diff']) && $params['hainan_diff'] != '') {
            $query->where('hainan_diff', '=', $params['hainan_diff']);
        }
        
        //Search By heilongjiang
        if (isset($params['heilongjiang']) && $params['heilongjiang'] != '') {
            $query->where('heilongjiang', '=', $params['heilongjiang']);
        }
        
        //Search By heilongjiang_diff
        if (isset($params['heilongjiang_diff']) && $params['heilongjiang_diff'] != '') {
            $query->where('heilongjiang_diff', '=', $params['heilongjiang_diff']);
        }
        
        //Search By jilin
        if (isset($params['jilin']) && $params['jilin'] != '') {
            $query->where('jilin', '=', $params['jilin']);
        }
        
        //Search By jilin_diff
        if (isset($params['jilin_diff']) && $params['jilin_diff'] != '') {
            $query->where('jilin_diff', '=', $params['jilin_diff']);
        }
        
        //Search By liaoning
        if (isset($params['liaoning']) && $params['liaoning'] != '') {
            $query->where('liaoning', '=', $params['liaoning']);
        }
        
        //Search By liaoning_diff
        if (isset($params['liaoning_diff']) && $params['liaoning_diff'] != '') {
            $query->where('liaoning_diff', '=', $params['liaoning_diff']);
        }
        
        //Search By shanxi2
        if (isset($params['shanxi2']) && $params['shanxi2'] != '') {
            $query->where('shanxi2', '=', $params['shanxi2']);
        }
        
        //Search By shanxi2_diff
        if (isset($params['shanxi2_diff']) && $params['shanxi2_diff'] != '') {
            $query->where('shanxi2_diff', '=', $params['shanxi2_diff']);
        }
        
        //Search By xinjiang
        if (isset($params['xinjiang']) && $params['xinjiang'] != '') {
            $query->where('xinjiang', '=', $params['xinjiang']);
        }
        
        //Search By xinjiang_diff
        if (isset($params['xinjiang_diff']) && $params['xinjiang_diff'] != '') {
            $query->where('xinjiang_diff', '=', $params['xinjiang_diff']);
        }
        
        //Search By neimeng
        if (isset($params['neimeng']) && $params['neimeng'] != '') {
            $query->where('neimeng', '=', $params['neimeng']);
        }
        
        //Search By neimeng_diff
        if (isset($params['neimeng_diff']) && $params['neimeng_diff'] != '') {
            $query->where('neimeng_diff', '=', $params['neimeng_diff']);
        }
        
        //Search By qinghai
        if (isset($params['qinghai']) && $params['qinghai'] != '') {
            $query->where('qinghai', '=', $params['qinghai']);
        }
        
        //Search By qinghai_diff
        if (isset($params['qinghai_diff']) && $params['qinghai_diff'] != '') {
            $query->where('qinghai_diff', '=', $params['qinghai_diff']);
        }
        
        //Search By ningxia
        if (isset($params['ningxia']) && $params['ningxia'] != '') {
            $query->where('ningxia', '=', $params['ningxia']);
        }
        
        //Search By ningxia_diff
        if (isset($params['ningxia_diff']) && $params['ningxia_diff'] != '') {
            $query->where('ningxia_diff', '=', $params['ningxia_diff']);
        }
        
        //Search By gansu
        if (isset($params['gansu']) && $params['gansu'] != '') {
            $query->where('gansu', '=', $params['gansu']);
        }
        
        //Search By gansu_diff
        if (isset($params['gansu_diff']) && $params['gansu_diff'] != '') {
            $query->where('gansu_diff', '=', $params['gansu_diff']);
        }
        
        //Search By sichuan
        if (isset($params['sichuan']) && $params['sichuan'] != '') {
            $query->where('sichuan', '=', $params['sichuan']);
        }
        
        //Search By sichuan_diff
        if (isset($params['sichuan_diff']) && $params['sichuan_diff'] != '') {
            $query->where('sichuan_diff', '=', $params['sichuan_diff']);
        }
        
        //Search By chongqing
        if (isset($params['chongqing']) && $params['chongqing'] != '') {
            $query->where('chongqing', '=', $params['chongqing']);
        }
        
        //Search By chongqing_diff
        if (isset($params['chongqing_diff']) && $params['chongqing_diff'] != '') {
            $query->where('chongqing_diff', '=', $params['chongqing_diff']);
        }
        
        //Search By xizang
        if (isset($params['xizang']) && $params['xizang'] != '') {
            $query->where('xizang', '=', $params['xizang']);
        }
        
        //Search By xizang_diff
        if (isset($params['xizang_diff']) && $params['xizang_diff'] != '') {
            $query->where('xizang_diff', '=', $params['xizang_diff']);
        }
        
        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }
        
        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }
        
        return $query;
    }
    
    /**
     * 免惠最低价 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilMinPrice::Filter($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy('sync_date', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }
        
        return $data;
    }
    
    /**
     * 免惠最低价 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilMinPrice::find($params['id']);
    }
    
    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilMinPrice::lockForUpdate()->where('id', $params['id'])->first();
    }
    
    /**
     * 免惠最低价 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilMinPrice::create($params);
    }
    
    /**
     * 免惠最低价 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilMinPrice::find($params['id'])->update($params);
    }
    
    /**
     * 免惠最低价 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);
        
        return OilMinPrice::destroy($params['ids']);
    }
    
    /**
     * @param $date
     * @return mixed
     */
    static public function getBySyncDate($date)
    {
        return self::where('sync_date', $date)->first();
    }
    
    
}