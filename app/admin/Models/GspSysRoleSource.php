<?php
/**
 * 角色资源表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:17
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class GspSysRoleSource extends \Framework\Database\Model
{
    protected $table = 'gsp_sys_role_source';

    protected $guarded = ["id"];

    protected $fillable = ['role_id','source_id','creator_id','last_operator','remark','createtime','updatetime','status','is_del'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function Sources()
    {
        return $this->hasMany('Models\GspSysSource','id','source_id');
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By role_id
        if (isset($params['role_id']) && $params['role_id'] != '') {
            $query->where('role_id', '=', $params['role_id']);
        }

        //Search By source_id
        if (isset($params['source_id']) && $params['source_id'] != '') {
            $query->where('source_id', '=', $params['source_id']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By is_del
        if (isset($params['is_del']) && $params['is_del'] != '') {
            $query->where('is_del', '=', $params['is_del']);
        }


        return $query;
    }

    /**
     * 角色资源表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = GspSysRoleSource::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 角色资源表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspSysRoleSource::find($params['id']);
    }

    /**
     * 角色资源表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return GspSysRoleSource::create($params);
    }

    /**
     * 角色资源表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspSysRoleSource::find($params['id'])->update($params);
    }

    /**
     * 角色资源表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return GspSysRoleSource::destroy($params['ids']);
    }

    /**
     * 根据角色查询资源
     * @param $roleIds
     * @return mixed
     */
    static public function getByRoleIds($roleIds)
    {
        return GspSysRoleSource::whereIn('role_id',$roleIds)
            ->where('is_del','=',0)
            ->where('status','=',1)
            ->pluck('source_id');
    }

}