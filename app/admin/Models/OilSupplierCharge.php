<?php
/**
 * oil_supplier_charge
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/07/15
 * Time: 11:13:10
 */
namespace Models;
use Fuel\Defines\SupplierAccountConf;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilSupplierCharge extends \Framework\Database\Model
{
    protected $table = 'oil_supplier_charge';

    protected $guarded = ["id"];
    protected $fillable = ['no','account_id','account_no','account_type','account_name','account_level','amount','charge_type',
        'app_no','app_id','status','is_del','remark','creator_id','creator_name','last_operator_id','last_operator','createtime',
        'updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By no
        if (isset($params['no']) && $params['no'] != '') {
            $query->where('no', '=', $params['no']);
        }

        //Search By account_id
        if (isset($params['account_id']) && $params['account_id'] != '') {
            $query->where('account_id', '=', $params['account_id']);
        }

        //Search By account_no
        if (isset($params['account_no']) && $params['account_no'] != '') {
            $query->where('account_no', '=', $params['account_no']);
        }

        //Search By account_type
        if (isset($params['account_type']) && $params['account_type'] != '') {
            $query->where('account_type', '=', $params['account_type']);
        }

        //Search By account_name
        if (isset($params['account_name']) && $params['account_name'] != '') {
            $query->where('account_name', '=', $params['account_name']);
        }

        //Search By account_level
        if (isset($params['account_level']) && $params['account_level'] != '') {
            $query->where('account_level', '=', $params['account_level']);
        }

        //Search By amount
        if (isset($params['amount']) && $params['amount'] != '') {
            $query->where('amount', '=', $params['amount']);
        }

        //Search By charge_type
        if (isset($params['charge_type']) && $params['charge_type'] != '') {
            $query->where('charge_type', '=', $params['charge_type']);
        }

        //Search By app_no
        if (isset($params['app_no']) && $params['app_no'] != '') {
            $query->where('app_no', '=', $params['app_no']);
        }

        //Search By app_id
        if (isset($params['app_id']) && $params['app_id'] != '') {
            $query->where('app_id', '=', $params['app_id']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By is_del
        if (isset($params['is_del']) && $params['is_del'] != '') {
            $query->where('is_del', '=', $params['is_del']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By creator_name
        if (isset($params['creator_name']) && $params['creator_name'] != '') {
            $query->where('creator_name', '=', $params['creator_name']);
        }

        //Search By last_operator_id
        if (isset($params['last_operator_id']) && $params['last_operator_id'] != '') {
            $query->where('last_operator_id', '=', $params['last_operator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By createtimeGe
        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('createtime', '>=', $params['createtimeGe']);
        }

        //Search By createtimeLe
        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $query->where('createtime', '<=', $params['createtimeLe']);
        }

        //Search By updatetimeGe
        if (isset($params['updatetimeGe']) && $params['updatetimeGe'] != '') {
            $query->where('updatetime', '>=', $params['updatetimeGe']);
        }

        //Search By updatetimeLe
        if (isset($params['updatetimeLe']) && $params['updatetimeLe'] != '') {
            $query->where('updatetime', '<=', $params['updatetimeLe']);
        }

        return $query;
    }

    /**
     * oil_supplier_charge 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilSupplierCharge::Filter($params);

        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        $data = self::formatData($data);
        return $data;
    }

    static public function formatData($data){
        if($data){
            foreach ($data as &$val){
                $val->charge_type_name = SupplierAccountConf::$supplier_charge_map[$val->charge_type] ?? '--';
            }
        }
        return $data;
    }

    /**
     * oil_supplier_charge 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierCharge::find($params['id']);
    }

    static public function getInfoByFilter(array $params)
    {
        return OilSupplierCharge::Filter($params)->first();
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierCharge::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_supplier_charge 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilSupplierCharge::create($params);
    }

    /**
     * oil_supplier_charge 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierCharge::find($params['id'])->update($params);
    }

    /**
     * oil_supplier_charge 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilSupplierCharge::destroy($params['ids']);
    }

    static public function createNo()
    {
        $microTime = microtime();
        $microArr = explode(" ", $microTime);
        $str = "SYCZ" . date("ymds", time());
        $str .= substr($microArr[0], 3, 5);
        $str .= sprintf('%02d', rand(0, 99));

        $exist = self::where("no", "=", $str)->lockForUpdate()->first();
        if ($exist) {
            self::createNo();
        }

        return $str;
    }




}