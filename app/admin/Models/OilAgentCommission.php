<?php
/**
 * 代理商佣金记录
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/03/03
 * Time: 11:00:01
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilAgentCommission extends \Framework\Database\Model
{
    protected $table = 'oil_agent_commission';

    protected $guarded = ["id"];
    protected $fillable = ['agent_orgcode','customer_orgcode','month','charge_fee','consume_fee','fanli_fee','commission_fee','audit_status','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By agent_orgcodeLk
        if (isset($params['agent_orgcodeLk']) && $params['agent_orgcodeLk'] != '') {
            $query->where('agent_orgcode', 'like', '%'.$params['agent_orgcodeLk'].'%');
        }

        //Search By agent_orgcode
        if (isset($params['agent_orgcode']) && $params['agent_orgcode'] != '') {
            $query->where('agent_orgcode', '=', $params['agent_orgcode']);
        }

        //Search By customer_orgcodeLk
        if (isset($params['customer_orgcodeLk']) && $params['customer_orgcodeLk'] != '') {
            $query->where('customer_orgcode', 'like', '%'.$params['customer_orgcodeLk'].'%');
        }

        //Search By customer_orgcode
        if (isset($params['customer_orgcode']) && $params['customer_orgcode'] != '') {
            $query->where('customer_orgcode', '=', $params['customer_orgcode']);
        }

        //Search By month
        if (isset($params['month']) && $params['month'] != '') {
            $query->where('month', '=', $params['month']);
        }

        //Search By monthLk
        if (isset($params['monthLk']) && $params['monthLk'] != '') {
            $query->where('month', 'like', $params['monthLk'].'%');
        }

        //Search By charge_fee
        if (isset($params['charge_fee']) && $params['charge_fee'] != '') {
            $query->where('charge_fee', '=', $params['charge_fee']);
        }

        //Search By consume_fee
        if (isset($params['consume_fee']) && $params['consume_fee'] != '') {
            $query->where('consume_fee', '=', $params['consume_fee']);
        }

        //Search By fanli_fee
        if (isset($params['fanli_fee']) && $params['fanli_fee'] != '') {
            $query->where('fanli_fee', '=', $params['fanli_fee']);
        }

        //Search By commission_fee
        if (isset($params['commission_fee']) && $params['commission_fee'] != '') {
            $query->where('commission_fee', '=', $params['commission_fee']);
        }

        //Search By audit_status
        if (isset($params['audit_status']) && $params['audit_status'] != '') {
            $query->where('audit_status', '=', $params['audit_status']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 代理商佣金记录 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilAgentCommission::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('createtime', 'desc')->get();
        }elseif(isset($params['_isAll']) && $params['_isAll'] == 1){
            $data = $sqlObj->orderBy('createtime', 'desc')->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }
        foreach ($data as &$val){
            switch ($val->audit_status) {
                case 2:
                    $val->audit_status_txt = '待审核';
                    break;
                case 3:
                    $val->audit_status_txt = '已审核';
                    break;
                default:
                    $val->fanli_fee = $val->commission_fee = '-';
                    $val->audit_status_txt = '未计算';
            }
            $condition['_count'] = 1;
            $condition['module_name'] = 'oil_agent_commission';
            $condition['third_id'] = $val->id;
            $nums = OilSysOperatorLogs::getList($condition);
            $val->log_num =  $nums > 0 ? $nums : '0';

            if($val->agent_orgcode) {
                $orgName = self::getOrgName($val->agent_orgcode);
                $val->agent_org_txt = $val->agent_orgcode." ".$orgName;
                $val->agent_org_name = $orgName;
            }else{
                $val->agent_org_txt = '无';
            }

            if($val->customer_orgcode) {
                $orgName = self::getOrgName($val->customer_orgcode);
                $val->customer_orgcode_txt = $val->customer_orgcode." ".$orgName;
                $val->customer_orgcode_name = $orgName;
            }else{
                $val->customer_orgcode_txt = '无';
            }
            $val->month = $val->month."\t";

        }
        return $data;
    }

    /**
     * 代理商佣金记录 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAgentCommission::find($params['id']);
    }

    /**
     * 代理商佣金记录 详情查询
     * @param array $params
     * @return object
     */
    static public function getInfoByParams(array $params)
    {
        return OilAgentCommission::lockForUpdate()->Filter($params)->first();
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAgentCommission::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 代理商佣金记录 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilAgentCommission::create($params);
    }

    /**
     * 代理商佣金记录 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAgentCommission::find($params['id'])->update($params);
    }

    /**
     * 代理商佣金记录 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilAgentCommission::destroy($params['ids']);
    }

    static public function getOneColum($params,$key = "id",$value="month")
    {
        return OilAgentCommission::Filter($params)->orderBy("createtime","asc")->pluck($value,$key);
    }

    static public function getOrgName($orgCode)
    {
        $cacheData = file_get_contents(APP_WWW_ROOT."/../tmp/rootOrgCache.php");
        if(count($cacheData) > 0){
            $cacheJson = json_decode($cacheData,true);
            if(array_key_exists($orgCode,$cacheJson)) {
                $orgname = $cacheJson[$orgCode];
            }
        }
        if(empty($orgname)){
            $orgInfo = OilOrg::getByOrgcode($orgCode);
            $orgname = $orgInfo->org_name;
        }
        return $orgname;
    }
}