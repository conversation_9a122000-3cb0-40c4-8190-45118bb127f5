<?php
/**
 * 机构积分账户表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:17
 */
namespace Models;
use Fuel\Defines\OilCom;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilAccountJifen extends \Framework\Database\Model
{
    protected $table = 'oil_account_jifen';

    protected $guarded = ["id"];

    protected $fillable = ['subAccountID', 'account_no','org_id','main_id','jifen','assign_total','fanli_total','createtime','updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function Org()
    {
        return $this->belongsTo('Models\OilOrg','org_id','id');
    }

    public function CardMain()
    {
        return $this->belongsTo('Models\OilCardMain','main_id','id');
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        if (isset($params['subAccountID']) && $params['subAccountID']) {
            $query->where('subAccountID', '=', $params['subAccountID']);
        }

        //Search By account_no
        if (isset($params['account_no']) && $params['account_no'] != '') {
            $query->where('account_no', '=', $params['account_no']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By org_id
        if (isset($params['orgcode']) && $params['orgcode'] != '' && isset($params['org_flag'])) {
            $query->where('oil_org.orgcode', 'like', $params['orgcode'].'%');
        }

        if (isset($params['orgcode']) && $params['orgcode'] != '' && !isset($params['org_flag'])) {
            $query->where('oil_org.orgcode', '=', $params['orgcode']);
        }

        //Search By oil_com
        if (isset($params['oil_com']) && $params['oil_com'] != '') {
            $query->where('oil_card_main.oil_com', '=', $params['oil_com']);
        }

        //Search By fanli_region
        if (isset($params['fanli_region']) && $params['fanli_region'] != '') {
            $query->where('oil_card_main.fanli_region', '=', $params['fanli_region']);
        }

        //Search By main_id
        if (isset($params['main_id']) && $params['main_id'] != '') {
            $query->where('main_id', '=', $params['main_id']);
        }

        //Search By jifen
        if (isset($params['jifen']) && $params['jifen'] != '') {
            $query->where('jifen', '=', $params['jifen']);
        }

        //Search By assign_total
        if (isset($params['assign_total']) && $params['assign_total'] != '') {
            $query->where('assign_total', '=', $params['assign_total']);
        }

        //Search By fanli_total
        if (isset($params['fanli_total']) && $params['fanli_total'] != '') {
            $query->where('fanli_total', '=', $params['fanli_total']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 机构积分账户表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        
        $sqlObj = Capsule::connection()->table('oil_account_jifen')
            ->leftJoin('oil_org', 'oil_account_jifen.org_id', '=', 'oil_org.id')
            ->leftJoin('oil_card_main', 'oil_card_main.id', '=', 'oil_account_jifen.main_id')
            ->leftJoin('oil_provinces', 'oil_provinces.id', '=', 'oil_card_main.fanli_region')
            ->select('oil_account_jifen.account_no', 'oil_account_jifen.assign_total', 'oil_account_jifen.createtime',
                'oil_account_jifen.fanli_total',
                'oil_account_jifen.id', 'oil_account_jifen.jifen', 'oil_account_jifen.main_id', 'oil_account_jifen.org_id','oil_account_jifen.updatetime',
                'oil_org.org_name','oil_org.orgcode',
                'oil_provinces.province',
                'oil_card_main.main_no','oil_card_main.oil_com');

        $oilAccountJifen = new OilAccountJifen();
        $sqlObj = $oilAccountJifen->scopeFilter($sqlObj, $params);

        if(isset($params['_export']) && $params['_export'] == 1){
            $data = self::preListData($sqlObj->get());
        }else{
            $skip = isset($params['page']) && isset($params['limit']) && intval($params['page']) > 0 ?
                ($params['page']-1) * $params['limit'] : 0;
            $limit = isset($params['limit']) && intval($params['limit']) > 0 ? intval($params['limit']) : 50;

            $total = $sqlObj->count();
            $result = $sqlObj
                ->skip($skip)
                ->take($limit)
                ->get();

            $data = [
                'total' =>  $total,
                'data'  =>  self::preListData($result)
            ];
        }

        return $data;
    }

    /**
     * 列表数据预处理
     * @param $data
     * @return mixed
     */
    static public function preListData($data)
    {
        if(count($data) > 0){
            foreach($data as &$v){
                $v->jifen = $v->jifen == '0' ? '-' : $v->jifen;
                $v->fanli_total = $v->fanli_total == '0' ? '-' : $v->fanli_total;
                $v->assign_total = $v->assign_total == '0' ? '-' : $v->assign_total;
                $v->_oil_com = $v->oil_com;
                $oilCom = \Fuel\Defines\OilCom::getById($v->oil_com);
                $v->oil_com = $oilCom['oil_com'];
                $v->orgroot = substr($v->orgcode,0,6);
            }
        }

        return $data;
    }

    /**
     * @title 根据org_id查询账户信息 并锁定
     * @desc
     * @version
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getByOrgIdForLock(array $params)
    {
        \helper::argumentCheck(['org_id'],$params);

        return OilAccountJifen::where('org_id','=',$params['org_id'])
            ->where('main_id','=',$params['main_id'])
            ->lockForUpdate()->first();
    }

    /**
     * 机构积分账户表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountJifen::find($params['id']);
    }

    /**
     * 机构积分账户表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilAccountJifen::create($params);
    }

    /**
     * 机构积分账户表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountJifen::find($params['id'])->update($params);
    }

    /**
     * 机构积分账户表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilAccountJifen::destroy($params['ids']);
    }

    /**
     * 生成机构账号
     */
    static public function createAccountNo()
    {
        $result = OilAccountJifen::where('account_no','like','106%')->max('account_no');

        if($result)
        {
            $account_no = $result + 1;
        }
        else
        {
            $account_no = '********';
        }
        return $account_no;
    }

    /**
     * 得到开通壹号卡的机构或积分账户
     */
    static public function getOrgPointAccountNo(array $condition)
    {
        //Capsule::connection()->enableQueryLog();
        $sqlObj = self::leftJoin('oil_org', 'oil_org.id', '=', 'oil_account_jifen.org_id')
            ->select("oil_org.id", "oil_org.orgcode","oil_account_jifen.account_no","oil_account_jifen.id as PointId")
            ->where("oil_org.is_del", 0)
            ->whereNotNull("oil_account_jifen.subAccountID");
        if(isset($condition['OrgIn']) && !empty($condition['OrgIn'])){
            $sqlObj->whereIn("oil_org.orgcode",$condition['OrgIn']);
        }else {
            $sqlObj->where($condition);
        }
        $data = $sqlObj->groupBy("oil_org.id")
                ->orderBy("oil_org.createtime", "desc")
                ->get();
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        return $data;
    }

    /**
     * @title 获取积分账户信息
     * <AUTHOR>
     * @param array $params
     * @return mixed
     */
    static public function getAccountInfo(array $params)
    {
        \helper::argumentCheck(['org_id','main_id'], $params);

        return OilAccountJifen::where('org_id', $params['org_id'])->where('main_id', $params['main_id'])->first();
    }

    static public function getAccountInfoForLock(array $params)
    {
        \helper::argumentCheck(['org_id','main_id'], $params);

        return OilAccountJifen::where('org_id', $params['org_id'])->where('main_id', $params['main_id'])->lockForUpdate()->first();
    }

    /**
     * @title 获取积分账户信息
     * <AUTHOR>
     * @param array $params
     * @return mixed
     */
    static public function getAccountByOrg(array $params)
    {
        \helper::argumentCheck(['org_id'], $params);

        return OilAccountJifen::where('oil_account_jifen.org_id', $params['org_id'])
                ->leftJoin('oil_card_main', 'oil_card_main.id', '=', 'oil_account_jifen.main_id')
                ->whereIn("oil_card_main.oil_com",[OilCom::GAS_FIRST_CHARGE])
                #->pluck("oil_account_jifen.account_no","oil_card_main.oil_com")->toArray();
                ->first();
    }
}