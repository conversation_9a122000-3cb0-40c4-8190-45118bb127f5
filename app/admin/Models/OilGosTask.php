<?php
/**
 * gos任务log
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2017/05/26
 * Time: 15:49:11
 */

namespace Models;

use Framework\Helper;
use Fuel\Defines\GosTaskActionType;
use Fuel\Defines\GosTaskStatus;
use Illuminate\Database\Capsule\Manager as Capsule;
use \Framework\Database\Model;

class OilGosTask extends Model
{
    protected $table = 'oil_gos_task';
    
    protected $guarded  = ["id"];
    protected $fillable = [
        'id', 'task_id', 'task_name', 'method', 'method_type', 'send_time', 'send_use_time', 'params',
        'callback_time', 'callback_use_time', 'code', 'message',
        'createuser', 'updateuser', 'status', 'createtime', 'updatetime'
    ];
    
    //disable incrementing id;
    public $incrementing = FALSE;
    
    public function getFillAble()
    {
        return $this->fillable;
    }
    
    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }
        
        //Search By task_id
        if (isset($params['task_id']) && $params['task_id'] != '') {
            $query->where('task_id', '=', $params['task_id']);
        }
        
        //Search By method
        if (isset($params['method']) && $params['method'] != '') {
            $query->where('method', '=', $params['method']);
        }
        
        //Search By task_name
        if (isset($params['task_name']) && $params['task_name'] != '') {
            $query->where('task_name', 'like', '%' . $params['task_name'] . '%');
        }
        
        //Search By params
        if (isset($params['params']) && $params['params'] != '') {
            $query->where('params', '=', $params['params']);
        }
        
        //Search By data
        if (isset($params['data']) && $params['data'] != '') {
            $query->where('data', '=', $params['data']);
        }
        
        //Search By send_use_time
        if (isset($params['send_use_time']) && $params['send_use_time'] != '') {
            $query->where('send_use_time', '=', $params['send_use_time']);
        }
        
        //Search By code
        if (isset($params['code']) && $params['code'] != '') {
            if ($params['code'] == 1) {
                $query->where('code', '!=', 0);
            } else {
                $query->where('code', '=', $params['code']);
            }
        }
        
        //Search By message
        if (isset($params['message']) && $params['message'] != '') {
            $query->where('message', '=', $params['message']);
        }
        
        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }
        
        //Search By createtime
        if (isset($params['createtime_ge']) && $params['createtime_ge'] != '') {
            $createtime_ge = preg_match('/^\d{4}(\-|\/|.)\d{1,2}\1\d{1,2}$/', $params['createtime_ge'], $matches) ? $params['createtime_ge'] . ' 00:00:00' : $params['createtime_ge'];
            $query->where('createtime', '>=', $createtime_ge);
        }
    
        if (isset($params['createtime_le']) && $params['createtime_le'] != '') {
            $createtime_le = preg_match('/^\d{4}(\-|\/|.)\d{1,2}\1\d{1,2}$/', $params['createtime_le'], $matches) ? $params['createtime_le'] . ' 23:59:59' : $params['createtime_le'];
            $query->where('createtime', '<=', $createtime_le);
        }
        
        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }
        
        return $query;
    }
    
    /**
     * gos任务log 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilGosTask::Filter($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }
        
        if ($data) {
            foreach ($data as &$v) {
                $v->params = \unserialize($v->params);
                $v->method = \unserialize($v->method);
                $v->method_type = GosTaskActionType::getById($v->method_type);
                $v->status = GosTaskStatus::getById($v->status);
            }
        }
        
        return $data;
    }
    
    /**
     * gos任务log 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilGosTask::find($params['id']);
    }
    
    static public function getByIds(array $params)
    {
        return OilGosTask::whereIn('id', $params['id'])->get();
    }
    
    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilGosTask::lockForUpdate()->where('id', $params['id'])->first();
    }
    
    /**
     * gos任务log 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        $params['id'] = isset($params['id']) && $params['id'] ? $params['id'] : Helper::uuid();
        return OilGosTask::create($params);
    }
    
    /**
     * gos任务log 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilGosTask::find($params['id'])->update($params);
    }
    
    /**
     * gos任务log 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);
        
        return OilGosTask::destroy($params['ids']);
    }
    
    static public function getByTaskId($taskId)
    {
        return self::where('task_id', $taskId)->first();
    }
    
    static public function editByTaskId(array $params)
    {
        if (!isset($params['task_id']) || !$params['task_id']) {
            throw new \RuntimeException('task_id not be null', 2);
        }
        
        return OilGosTask::where('task_id', $params['task_id'])->update($params);
    }
    
    /**
     * cron 自动删除一周前的数据
     * @return array
     */
    static public function deleteWeekBefore(array $params)
    {
        $beforeTime = isset($params['dateTime']) && $params['dateTime'] ? $params['dateTime'] : date('Y-m-d', strtotime('-1 week'));
        $monthTime = date('Y-m-d', strtotime('-1 month'));
        //先删除1个月之前所有的记录，然后删除7天的，提高删除效率18-11-08
        OilGosTask::where('createtime', '<', $monthTime)->delete();
        $data = OilGosTask::where('createtime', '<', $beforeTime)->where('status', 40)->delete();
        return $data;
    }
    
    
}