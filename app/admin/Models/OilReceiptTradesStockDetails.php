<?php
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilReceiptTradesStockDetails extends \Framework\Database\Model
{
    protected $table = 'oil_receipt_trades_stock_details';

    protected $guarded = ["id"];

    protected $fillable = ['receipt_apply_id','trades_id','receipt_money','o_num','x_num',
    'createtime','updatetime','creator','creator_id','remark'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    static public function getList(array $params)
    {
        $data = [];

        $sqlObj = OilReceiptTradesStockDetails::Filter($params);
        $data = $sqlObj->get();

        return $data;
    }

    /**
     * 中国 省、市、区,县  三级行政词典 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptTradesStockDetails::find($params['id']);
    }

    static public function getByIdList(array $params)
    {
        \helper::argumentCheck(['idList'], $params);

        return OilReceiptTradesStockDetails::whereIn('id', $params['idList'])->get();
    }

    static public function add(array $params)
    {
        return OilReceiptTradesStockDetails::create($params);
    }

    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptTradesStockDetails::find($params['id'])->update($params);
    }

    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilReceiptTradesStockDetails::destroy($params['ids']);
    }

    static public function batchAdd(array $params)
    {
        return OilReceiptTradesStockDetails::insert($params);
    }
}