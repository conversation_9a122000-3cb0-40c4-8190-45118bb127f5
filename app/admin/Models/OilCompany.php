<?php
/**
 * 企业基本信息表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/03/20
 * Time: 15:08:02
 */

namespace Models;

use Illuminate\Database\Capsule\Manager as Capsule;

class OilCompany extends \Framework\Database\Model
{
    protected $table = 'oil_company';

    protected $guarded  = ["id"];
    protected $fillable = ['company_name', 'credit_code', 'address', 'tel', 'bank_name', 'bank_no', 'open_way', 'username', 'userpassword', 'orgcode', 'product_list', 'progess_status', 'status', 'express_info', 'salesmen_id', 'salesmen_name', 'crm_id', 'contacter_name', 'contacter_role', 'contacter_mobile', 'main_saler_name', 'main_saler_org', 'assist_saler_name', 'assist_saler_org', 'createtime', 'updatetime', 'crm_id'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function OilCompanyContractImgs()
    {
        return $this->hasMany("Models\OilCompanyContractImg", "company_id", "id");
    }

    public function OilCompanyInfoImgs()
    {
        return $this->hasMany("Models\OilCompanyInfoImg", "company_id", "id");
    }


    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By company_name
        if (isset($params['company_name']) && $params['company_name'] != '') {
            $query->where('company_name', 'like', "%" . $params['company_name'] . "%");
        }

        //Search By credit_code
        if (isset($params['credit_code']) && $params['credit_code'] != '') {
            $query->where('credit_code', '=', $params['credit_code']);
        }

        //Search By address
        if (isset($params['address']) && $params['address'] != '') {
            $query->where('address', '=', $params['address']);
        }

        //Search By tel
        if (isset($params['tel']) && $params['tel'] != '') {
            $query->where('tel', '=', $params['tel']);
        }

        //Search By bank_name
        if (isset($params['bank_name']) && $params['bank_name'] != '') {
            $query->where('bank_name', '=', $params['bank_name']);
        }

        //Search By bank_no
        if (isset($params['bank_no']) && $params['bank_no'] != '') {
            $query->where('bank_no', '=', $params['bank_no']);
        }

        //Search By open_way
        if (isset($params['open_way']) && $params['open_way'] != '') {
            $query->where('open_way', '=', $params['open_way']);
        }

        //Search By username
        if (isset($params['username']) && $params['username'] != '') {
            $query->where('username', '=', $params['username']);
        }

        //Search By crm_id
        if (isset($params['crm_id']) && $params['crm_id'] != '') {
            $query->where('crm_id', '=', $params['crm_id']);
        }

        //Search By userpassword
        if (isset($params['userpassword']) && $params['userpassword'] != '') {
            $query->where('userpassword', '=', $params['userpassword']);
        }

        //Search By orgcode
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $query->where('orgcode', '=', $params['orgcode']);
        }

        //Search By product_list
        if (isset($params['product_list']) && $params['product_list'] != '') {
            $query->where('product_list', '=', $params['product_list']);
        }

        //Search By progess_status
        if (isset($params['progess_status']) && $params['progess_status'] != '') {
            $query->where('progess_status', '=', $params['progess_status']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By express_info
        if (isset($params['express_info']) && $params['express_info'] != '') {
            $query->where('express_info', '=', $params['express_info']);
        }

        //Search By salesmen_id
        if (isset($params['salesmen_id']) && $params['salesmen_id'] != '') {
            $query->where('salesmen_id', '=', $params['salesmen_id']);
        }

        //Search By salesmen_name
        if (isset($params['salesmen_name']) && $params['salesmen_name'] != '') {
            $query->where('salesmen_name', '=', $params['salesmen_name']);
        }

        //Search By crm_id
        if (isset($params['crm_id']) && $params['crm_id'] != '') {
            $query->where('crm_id', '=', $params['crm_id']);
        }

        //Search By createtimeGe
        if (!empty($params['createtimeGe'])) {
            $query->where('createtime', '>=', $params['createtimeGe']);
        }

        //Search By createtimeLe
        if (!empty($params['createtimeLe'])) {
            $query->where('createtime', '<=', $params['createtimeLe'] . '23:59:59');
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By main_saler_name
        if (isset($params['main_saler_name']) && $params['main_saler_name'] != '') {
            $query->where('main_saler_name', '=', $params['main_saler_name']);
        }

        //Search By main_saler_org
        if (isset($params['main_saler_org']) && $params['main_saler_org'] != '') {
            $query->where('main_saler_org', '=', $params['main_saler_org']);
        }

        //Search By assist_saler_name
        if (isset($params['assist_saler_name']) && $params['assist_saler_name'] != '') {
            $query->where('assist_saler_name', '=', $params['assist_saler_name']);
        }

        //Search By assist_saler_org
        if (isset($params['assist_saler_org']) && $params['assist_saler_org'] != '') {
            $query->where('assist_saler_org', '=', $params['assist_saler_org']);
        }

        return $query;
    }

    /**
     * 企业基本信息表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page']  = isset($params['page']) ? $params['page'] : 1;
        $sqlObj          = OilCompany::Filter($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->with(['OilCompanyContractImgs', 'OilCompanyInfoImgs'])
                ->orderBy('createtime', 'desc')
                ->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        if ($data) {
            foreach ($data as &$val) {
                $product_arr = explode(',', $val->product_list);
                $product_val = [];
                foreach ($product_arr as $value) {
                    if ($value == 1) {
                        $product_val[] = '中石油中石化';
                    }
                    if ($value == 2) {
                        $product_val[] = '1号电子卡';
                    }
                    if ($value == 3) {
                        $product_val[] = '撬装业务';
                    }
                }

                $val->product_list = implode(',', $product_val);
            }
        }

        return $data;
    }

    /**
     * 企业基本信息表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCompany::find($params['id']);
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCompany::lockForUpdate()->where('id', $params['id'])->first();
    }

    /**
     * 企业基本信息表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCompany::create($params);
    }

    /**
     * 企业基本信息表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCompany::find($params['id'])->update($params);
    }

    /**
     * 企业基本信息表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilCompany::destroy($params['ids']);
    }


}