<?php
/**
 * oil_assign_pond
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/18
 * Time: 17:23:46
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilAssignPond extends \Framework\Database\Model
{
    protected $table = 'oil_assign_pond';

    protected $guarded = ["id"];

    protected $fillable = ['charge_id','money','vice_id','createtime','updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * CardVice
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     * <AUTHOR>
     * @since ${DATE}
     */
    public function CardVice()
    {
        return $this->belongsTo('Models\OilCardVice','vice_id','id');
    }

    /**
     * AccountMoneyCharge
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     * <AUTHOR>
     * @since ${DATE}
     */
    public function AccountMoneyCharge()
    {
        return $this->belongsTo('Models\OilAccountMoneyCharge','charge_id','id');
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By charge_id
        if (isset($params['charge_id']) && $params['charge_id'] != '') {
            $query->where('charge_id', '=', $params['charge_id']);
        }

        //Search By total_money
        if (isset($params['money']) && $params['money'] != '') {
            $query->where('money', '=', $params['money']);
        }


        //Search By coupon_money
        if (isset($params['vice_id']) && $params['vice_id'] != '') {
            $query->where('vice_id', '=', $params['vice_id']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }


        return $query;
    }

    /**
     * oil_assign_pond 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilAssignPond::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_assign_pond 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAssignPond::find($params['id']);
    }

    /**
     * oil_assign_pond 详情查询
     * @param array $params
     * @return object
     */
    static public function getByChargeId(array $params)
    {
        \helper::argumentCheck(['charge_id'],$params);

        return OilAssignPond::where('charge_id','=',$params['charge_id'])->get();
    }

    /**
     * oil_assign_pond 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilAssignPond::create($params);
    }

    /**
     * oil_assign_pond 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAssignPond::find($params['id'])->update($params);
    }

    /**
     * oil_assign_pond 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilAssignPond::destroy($params['ids']);
    }




}