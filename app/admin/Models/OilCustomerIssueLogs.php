<?php
/**
 * 客户问题操作日志
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/06/05
 * Time: 19:24:39
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCustomerIssueLogs extends \Framework\Database\Model
{
    protected $table = 'oil_customer_issue_logs';

    protected $guarded = ["id"];
    protected $fillable = ['issue_id','case_log_id','content','status','iscallback','hangtime','exclusive_custom_name','creator_name','createtime','updatetime','third_createtime','third_createuser','last_operator'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By issue_id
        if (isset($params['issue_id']) && $params['issue_id'] != '') {
            $query->where('issue_id', '=', $params['issue_id']);
        }

        //Search By content
        if (isset($params['content']) && $params['content'] != '') {
            $query->where('content', '=', $params['content']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By iscallback
        if (isset($params['iscallback']) && $params['iscallback'] != '') {
            $query->where('iscallback', '=', $params['iscallback']);
        }

        //Search By hangtime
        if (isset($params['hangtime']) && $params['hangtime'] != '') {
            $query->where('hangtime', '=', $params['hangtime']);
        }

        //Search By exclusive_custom_name
        if (isset($params['exclusive_custom_name']) && $params['exclusive_custom_name'] != '') {
            $query->where('exclusive_custom_name', '=', $params['exclusive_custom_name']);
        }

        //Search By creator_name
        if (isset($params['creator_name']) && $params['creator_name'] != '') {
            $query->where('creator_name', '=', $params['creator_name']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 客户问题操作日志 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCustomerIssueLogs::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 客户问题操作日志 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCustomerIssueLogs::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCustomerIssueLogs::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 客户问题操作日志 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        $params['createtime'] = \helper::nowTime();
        $params['updatetime'] = \helper::nowTime();
        return OilCustomerIssueLogs::create($params);
    }

    /**
     * 客户问题操作日志 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCustomerIssueLogs::find($params['id'])->update($params);
    }

    /**
     * 客户问题操作日志 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCustomerIssueLogs::destroy($params['ids']);
    }

    /**
     * 客户问题操作日志 根据issue_id删除或批量删除
     * @param array $params
     * @return int
     */
    static public function removeData(array $params)
    {
        \helper::argumentCheck(['issue_id'],$params);

        return OilCustomerIssueLogs::where("issue_id",'=',$params['issue_id'])->delete();
    }
    /**
     * 获取某列数据
     */
    static public function getOneColume(array $params,$field="id",$isdistinct=false){
        \helper::argumentCheck(['issue_id'],$params);

        if($isdistinct) {
            return OilCustomerIssueLogs::select($field)->where($params)->distinct()->get()->toArray();
        }else{
            return OilCustomerIssueLogs::select($field)->where($params)->orderby("id","asc")->get()->toArray();
        }
    }
    /**
     * 客户问题反馈 根据id
     * @param array $params
     * @return int
     */
    static public function getIssueLogs(array $params)
    {
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 5;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        \helper::argumentCheck(['issue_id'],$params);
        $subSql = Capsule::connection()->table('oil_customer_issue as issue')
            ->leftJoin('oil_customer_issue_logs as issuelog','issue.id','=','issuelog.issue_id')
            ->where('issue.id',$params['issue_id'])
            ->orderBy("issuelog.third_createtime",'desc')
            ->select('issue.third_createtime as start_time','issue.exclusive_custom','issue.customer_id',"issuelog.*");

        return $subSql->paginate($params['limit'], ['*'], 'page', $params['page'])->toArray();
    }
    /**
     * 客户问题反馈 根据issue_id获取回电次数
     * @param array $params
     * @return int
     */
    static public function getCallBackNum(array $params)
    {
        \helper::argumentCheck(['issue_id'],$params);
        return OilCustomerIssueLogs::where($params)->count('id');
    }

    /**
     * 查找及更新
     */
    static public function updateOrAdd (array $params,$values){

//        \helper::argumentCheck(['case_log_id'],$params);

        return OilCustomerIssueLogs::updateOrCreate($params,$values);
    }

    /**
     * 查找工单日志信息
     * @param array $params
     * @return object
     */
    static public function getDetailLog(array $params)
    {
        \helper::argumentCheck(['case_log_id'],$params);

        return OilCustomerIssueLogs::where('case_log_id',$params['case_log_id'])->first();
    }


}