<?php
/**
 * Foss开票额度
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/12/18
 * Time: 10:23:38
 */

namespace Models;

use Framework\Log;
use Fuel\Defines\OilType;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilReceiptQuotaDay extends \Framework\Database\Model
{
    protected $table = 'oil_receipt_quota_day';

    protected $guarded  = ["id"];
    protected $fillable = [
        'org_id','org_code', 'subcategory', 'receipt_title_id', 'pay_company_id', 'end_date', 'a_receipt_amount_remain',
        'b_pay_total_recharge_remain', 'c_org_total_trade_remain', 'd_pay_total_recharge', 'e_pay_total_repay',
        'f_pay_total_recharge_and_repay', 'g_pay_total_receipt', 'h_pay_total_receipt_freeze', 'j_org_total_trade',
        'k_org_total_rebate_calculate', 'l_org_total_rebate_recharge', 'o_org_total_rebate', 'm_org_total_rebate_used',
        'r_org_total_receipt', 's_org_total_receipt_freeze', 'z_marked_trade', 'createtime', 'updatetime', 'deleted_at',
        'org_operator_id'
    ];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By org_code
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', $params['org_id']);
        }

        //Search By org_operator_id
        if (isset($params['org_operator_id']) && $params['org_operator_id'] != '') {
            $query->where('oil_receipt_quota_day.org_operator_id', $params['org_operator_id']);
        }

        //Search By org_code
        if (isset($params['org_code']) && $params['org_code'] != '') {
            $query->where('org_code', 'like', $params['org_code']."%");
        }

        //Search By subcategory
        if (isset($params['subcategory']) && $params['subcategory'] != '') {
            $query->where('subcategory', '=', $params['subcategory']);
        }

        //Search By receipt_title_id
        if (isset($params['receipt_title_id']) && $params['receipt_title_id'] != '') {
            $query->where('receipt_title_id', '=', $params['receipt_title_id']);
        }

        //Search By pay_company_id
        if (isset($params['pay_company_id']) && $params['pay_company_id'] != '') {
            $query->where('pay_company_id', '=', $params['pay_company_id']);
        }

        //Search By end_date
        if (isset($params['end_date']) && $params['end_date'] != '') {
            $query->where('end_date', '=', $params['end_date']);
        }

        //Search By a_receipt_amount_remain
        if (isset($params['a_receipt_amount_remain']) && $params['a_receipt_amount_remain'] != '') {
            $query->where('a_receipt_amount_remain', '=', $params['a_receipt_amount_remain'] * 100);
        }

        //Search By a_receipt_amount_remain_lt
        if (isset($params['a_receipt_amount_remain_lt']) && $params['a_receipt_amount_remain_lt'] != '') {
            $query->where('a_receipt_amount_remain', '<', $params['a_receipt_amount_remain_lt'] * 100);
        }

        //Search By a_receipt_amount_remain_ge
        if (isset($params['a_receipt_amount_remain_ge']) && $params['a_receipt_amount_remain_ge'] != '') {
            $query->where('a_receipt_amount_remain', '>=', $params['a_receipt_amount_remain_ge'] * 100);
        }

        //Search By b_pay_total_recharge_remain
        if (isset($params['b_pay_total_recharge_remain']) && $params['b_pay_total_recharge_remain'] != '') {
            $query->where('b_pay_total_recharge_remain', '=', $params['b_pay_total_recharge_remain'] * 100);
        }

        //Search By c_org_total_trade_remain
        if (isset($params['c_org_total_trade_remain']) && $params['c_org_total_trade_remain'] != '') {
            $query->where('c_org_total_trade_remain', '=', $params['c_org_total_trade_remain'] * 100);
        }

        //Search By d_pay_total_recharge
        if (isset($params['d_pay_total_recharge']) && $params['d_pay_total_recharge'] != '') {
            $query->where('d_pay_total_recharge', '=', $params['d_pay_total_recharge'] * 100);
        }

        //Search By e_pay_total_repay
        if (isset($params['e_pay_total_repay']) && $params['e_pay_total_repay'] != '') {
            $query->where('e_pay_total_repay', '=', $params['e_pay_total_repay'] * 100);
        }

        //Search By f_pay_total_recharge_and_repay
        if (isset($params['f_pay_total_recharge_and_repay']) && $params['f_pay_total_recharge_and_repay'] != '') {
            $query->where('f_pay_total_recharge_and_repay', '=', $params['f_pay_total_recharge_and_repay'] * 100);
        }

        //Search By g_pay_total_receipt
        if (isset($params['g_pay_total_receipt']) && $params['g_pay_total_receipt'] != '') {
            $query->where('g_pay_total_receipt', '=', $params['g_pay_total_receipt'] * 100);
        }

        //Search By h_pay_total_receipt_freeze
        if (isset($params['h_pay_total_receipt_freeze']) && $params['h_pay_total_receipt_freeze'] != '') {
            $query->where('h_pay_total_receipt_freeze', '=', $params['h_pay_total_receipt_freeze'] * 100);
        }

        //Search By j_org_total_trade
        if (isset($params['j_org_total_trade']) && $params['j_org_total_trade'] != '') {
            $query->where('j_org_total_trade', '=', $params['j_org_total_trade'] * 100);
        }

        //Search By k_org_total_rebate_calculate
        if (isset($params['k_org_total_rebate_calculate']) && $params['k_org_total_rebate_calculate'] != '') {
            $query->where('k_org_total_rebate_calculate', '=', $params['k_org_total_rebate_calculate'] * 100);
        }

        //Search By l_org_total_rebate_recharge
        if (isset($params['l_org_total_rebate_recharge']) && $params['l_org_total_rebate_recharge'] != '') {
            $query->where('l_org_total_rebate_recharge', '=', $params['l_org_total_rebate_recharge'] * 100);
        }

        //Search By o_org_total_rebate
        if (isset($params['o_org_total_rebate']) && $params['o_org_total_rebate'] != '') {
            $query->where('o_org_total_rebate', '=', $params['o_org_total_rebate'] * 100);
        }

        //Search By m_org_total_rebate_used
        if (isset($params['m_org_total_rebate_used']) && $params['m_org_total_rebate_used'] != '') {
            $query->where('m_org_total_rebate_used', '=', $params['m_org_total_rebate_used'] * 100);
        }

        //Search By r_org_total_receipt
        if (isset($params['r_org_total_receipt']) && $params['r_org_total_receipt'] != '') {
            $query->where('r_org_total_receipt', '=', $params['r_org_total_receipt'] * 100);
        }

        //Search By s_org_total_receipt_freeze
        if (isset($params['s_org_total_receipt_freeze']) && $params['s_org_total_receipt_freeze'] != '') {
            $query->where('s_org_total_receipt_freeze', '=', $params['s_org_total_receipt_freeze'] * 100);
        }

        //Search By z_marked_trade
        if (isset($params['z_marked_trade']) && $params['z_marked_trade'] != '') {
            $query->where('z_marked_trade', '=', $params['z_marked_trade']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By deleted_at
        if (isset($params['deleted_at']) && $params['deleted_at'] != '') {
            $query->where('deleted_at', '=', $params['deleted_at']);
        }

        return $query;
    }

    /**
     * Foss开票额度 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        //Capsule::connection()->enableQueryLog();
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page']  = isset($params['page']) ? $params['page'] : 1;
        $sqlObj          = OilReceiptQuotaDay::Filter($params)
            ->select("oil_pay_company.company_name",  'oil_receipt_quota_day.org_code', 'oil_receipt_quota_day.subcategory',
                'oil_receipt_quota_day.receipt_title_id', 'oil_receipt_quota_day.pay_company_id', 'oil_receipt_quota_day.end_date',
                'oil_receipt_quota_day.a_receipt_amount_remain', 'oil_receipt_quota_day.b_pay_total_recharge_remain',
                'oil_receipt_quota_day.c_org_total_trade_remain', 'oil_receipt_quota_day.d_pay_total_recharge',
                'oil_receipt_quota_day.e_pay_total_repay', 'oil_receipt_quota_day.f_pay_total_recharge_and_repay',
                'oil_receipt_quota_day.g_pay_total_receipt', 'oil_receipt_quota_day.h_pay_total_receipt_freeze',
                'oil_receipt_quota_day.j_org_total_trade', 'oil_receipt_quota_day.k_org_total_rebate_calculate',
                'oil_receipt_quota_day.l_org_total_rebate_recharge', 'oil_receipt_quota_day.o_org_total_rebate',
                'oil_receipt_quota_day.m_org_total_rebate_used', 'oil_receipt_quota_day.r_org_total_receipt',
                'oil_receipt_quota_day.s_org_total_receipt_freeze','oil_receipt_quota_day.org_operator_id',
                'oil_org.org_name','oil_org.is_recepit_nowtime','oil_org.receipt_mode','oil_org.operators_id'
            )
            ->leftJoin("oil_pay_company", "oil_pay_company.id", "=", "oil_receipt_quota_day.pay_company_id")
            ->leftJoin("oil_org", "oil_org.id", "=", "oil_receipt_quota_day.org_id")
            ->where("oil_org.is_del", "=", 0);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy('oil_receipt_quota_day.id', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        //$sql = Capsule::connection()->getQueryLog();
        //Log::error('sql:'.var_export($sql,true),[],'timRay');
        if (count($data) > 0) {
            $operator = OilOperators::getIdMapName("company_name");
            foreach ($data as &$v) {
                $v->is_recepit_nowtime             = $v->is_recepit_nowtime == '2' ? '是' : '否';
                $v->receipt_mode                   = $v->receipt_mode == '1' ? '集中' : '独立';
                $v->_subcategory                   = $v->subcategory;
                $v->a_receipt_amount_remain        = sprintf("%.2f", $v->a_receipt_amount_remain / 100);
                $v->b_pay_total_recharge_remain    = sprintf("%.2f", $v->b_pay_total_recharge_remain / 100);
                $v->c_org_total_trade_remain       = sprintf("%.2f", $v->c_org_total_trade_remain / 100);
                $v->d_pay_total_recharge           = sprintf("%.2f", $v->d_pay_total_recharge / 100);
                $v->e_pay_total_repay              = sprintf("%.2f", $v->e_pay_total_repay / 100);
                $v->f_pay_total_recharge_and_repay = sprintf("%.2f", $v->f_pay_total_recharge_and_repay / 100);
                $v->g_pay_total_receipt            = sprintf("%.2f", $v->g_pay_total_receipt / 100);
                $v->h_pay_total_receipt_freeze     = sprintf("%.2f", $v->h_pay_total_receipt_freeze / 100);
                $v->j_org_total_trade              = sprintf("%.2f", $v->j_org_total_trade / 100);
                $v->o_org_total_rebate             = sprintf("%.2f", $v->o_org_total_rebate / 100);
                $v->k_org_total_rebate_calculate   = sprintf("%.2f", $v->k_org_total_rebate_calculate / 100);
                $v->l_org_total_rebate_recharge    = sprintf("%.2f", $v->l_org_total_rebate_recharge / 100);
                $v->m_org_total_rebate_used        = sprintf("%.2f", $v->m_org_total_rebate_used / 100);
                $v->r_org_total_receipt            = sprintf("%.2f", $v->r_org_total_receipt / 100);
                $v->s_org_total_receipt_freeze     = sprintf("%.2f", $v->s_org_total_receipt_freeze / 100);
                $v->subcategory                    = OilType::getById($v->subcategory);
                $v->operator_txt                   = isset($operator[$v->org_operator_id]) ? $operator[$v->org_operator_id] : "";
            }
        }
        //Log::error('end',[],'timRay');

        return $data;
    }

    /**
     * Foss开票额度 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilReceiptQuotaDay::find($params['id']);
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilReceiptQuotaDay::lockForUpdate()->where('id', $params['id'])->first();
    }

    /**
     * Foss开票额度 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilReceiptQuotaDay::create($params);
    }

    /**
     * Foss开票额度 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilReceiptQuotaDay::find($params['id'])->update($params);
    }

    /**
     * Foss开票额度 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilReceiptQuotaDay::destroy($params['ids']);
    }


}