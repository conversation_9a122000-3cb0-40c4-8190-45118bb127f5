<?php
/**
 * 任务中心
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/08/23
 * Time: 12:41:05
 */

namespace Models;

use Framework\Database\Model;
use Framework\Log;
use Fuel\Defines\OilDownloadStatus;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilDownload extends Model
{
    protected $table = 'oil_download';

    protected $guarded  = ["id"];
    protected $fillable = [
        'jobs_id', 'rate', 'project','channel',
        'filename', 'filetype', 'filesize',
        'url', 'down_count', 'status',
        'message', 'downtime', 'createtime', 'createuser','task_id'
    ];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function Jobs()
    {
        return $this->belongsTo('Models\OilJobs', 'jobs_id', 'id');
    }

    /**
     * 创建人
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function Creator()
    {
        return $this->belongsTo('Models\GspSysUsers', 'createuser', 'id');
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By jobs_id
        if (isset($params['jobs_id']) && $params['jobs_id'] != '') {
            $query->where('jobs_id', '=', $params['jobs_id']);
        }

        //Search By project
        if (isset($params['project']) && $params['project'] != '') {
            $query->where('project', 'like', '%' . $params['project'] . '%');
        }

        //Search By filename
        if (isset($params['filename']) && $params['filename'] != '') {
            $query->where('filename', '=', $params['filename']);
        }

        //Search By filetype
        if (isset($params['filetype']) && $params['filetype'] != '') {
            $query->where('filetype', '=', $params['filetype']);
        }

        //Search By filesize
        if (isset($params['filesize']) && $params['filesize'] != '') {
            $query->where('filesize', '=', $params['filesize']);
        }

        //Search By url
        if (isset($params['url']) && $params['url'] != '') {
            $query->where('url', '=', $params['url']);
        }

        //Search By down_count
        if (isset($params['down_count']) && $params['down_count'] != '') {
            $query->where('down_count', '=', $params['down_count']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By downtime
        if (isset($params['downtime']) && $params['downtime'] != '') {
            $query->where('downtime', '=', $params['downtime']);
        }

        if (isset($params['message']) && $params['message'] != '') {
            $query->where('message', '=', $params['message']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('createtime', '>=', $params['createtimeGe']);
        }

        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $query->where('createtime', '<=', $params['createtimeLe'] . '23:59:59');
        }

        //Search By createuse
        if (isset($params['createuser'])) {
            global $app;
            if(in_array(67, $app->myAdmin->roles) || in_array($app->myAdmin->id, [1,1000126])) {

            }else{
                $query->where('createuser', '=', $params['createuser']);
            }

        }

        return $query;
    }

    /**
     * 任务中心 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $sqlObj = OilDownload::Filter($params)->with(['Creator'])->orderBy('id', 'desc');
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);

            if (count($data) > 0) {
                $queueNum = 1;
                foreach ($data as &$v) {
                    if ($v->status == 1) {
                        $queueNum++;
                    }
                }

                //排号处理
                if ($queueNum <= 10) {
                    foreach ($data as $k => &$v) {
                        if ($v->status == 1) {
                            $v->queue_num = OilDownload::where('channel', $v->channel)
                                ->whereIn('oil_download.status', [1, 10])
                                ->where('id','<',$v->id)
                                ->count();
                        } else {
                            $v->queue_num = '';
                        }
                    }
                }
            }
        }
        global $app;
        $status = OilDownloadStatus::getAll();
        list(,$operator_id) = authCheck();
        foreach ($data as &$v) {
            $v->_status = $v->status;
            $v->status = $status[$v->status];
            $v->createuser = $v->Creator->true_name;
            //G7WALLET-2000 屏蔽掉filename和url
            if(!$operator_id &&  stripos($v->url,"oss.aliyuncs") !== false && $app->config->exportJob->switch == 1 ){
                $v->filename = "";
                $v->url = "";
            }
        }

        return $data;
    }

    /**
     * 任务中心 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilDownload::find($params['id']);
    }

    /**
     * 任务中心 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilDownload::create($params);
    }

    /**
     * 任务中心 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        Log::debug('oilDownload:' . \var_export($params, TRUE), [], 'jobOk');

        return OilDownload::where('id', $params['id'])->update($params);
    }

    /**
     * 任务中心 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilDownload::destroy($params['ids']);
    }

    static public function updateByJobsId(array $params)
    {
        return OilDownload::where('jobs_id', '=', $params['jobs_id'])->update($params);
    }

    static public function getFilterInfo(array $params)
    {
        return OilDownload::Filter($params)->first();
    }

    /**
     * Desc:监控近2小时是否有未处理任务
     *  ---------------------------------------------
     * @return mixed
     *  ---------------------------------------------
     * <AUTHOR>
     * @DateTime: 2023-02-08 18:06
     */
    static public function getMonitorList()
    {
        $last_time = date('Y-m-d H:i:s',time()-7200);
        return OilDownload::where('status', '=', '1')->where('createtime','>' ,$last_time)->count();
    }

}
