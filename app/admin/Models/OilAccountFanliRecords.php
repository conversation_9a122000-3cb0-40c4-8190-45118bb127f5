<?php
/**
 * 机构资金账户流水表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2017/08/22
 * Time: 11:28:37
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilAccountFanliRecords extends \Framework\Database\Model
{
    protected $table = 'oil_account_fanli_records';

    protected $guarded = ["id"];
    protected $fillable = ['money_id','org_id','from_org_id','fanli_discount_remain','after_fanli_discount_remain','no_type','no','remark','remark_work','createtime','updatetime','operator_id','operator_name'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By money_id
        if (isset($params['money_id']) && $params['money_id'] != '') {
            $query->where('money_id', '=', $params['money_id']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By from_org_id
        if (isset($params['from_org_id']) && $params['from_org_id'] != '') {
            $query->where('from_org_id', '=', $params['from_org_id']);
        }

        //Search By trade_type
        if (isset($params['trade_type']) && $params['trade_type'] != '') {
            $query->where('trade_type', '=', $params['trade_type']);
        }

        //Search By fanli_discount_remain
        if (isset($params['fanli_discount_remain']) && $params['fanli_discount_remain'] != '') {
            $query->where('fanli_discount_remain', '=', $params['fanli_discount_remain']);
        }

        //Search By after_fanli_discount_remain
        if (isset($params['after_fanli_discount_remain']) && $params['after_fanli_discount_remain'] != '') {
            $query->where('after_fanli_discount_remain', '=', $params['after_fanli_discount_remain']);
        }

        //Search By no_type
        if (isset($params['no_type']) && $params['no_type'] != '') {
            $query->where('no_type', '=', $params['no_type']);
        }

        //Search By no
        if (isset($params['no']) && $params['no'] != '') {
            $query->where('no', '=', $params['no']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By remark_work
        if (isset($params['remark_work']) && $params['remark_work'] != '') {
            $query->where('remark_work', '=', $params['remark_work']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By operator_id
        if (isset($params['operator_id']) && $params['operator_id'] != '') {
            $query->where('operator_id', '=', $params['operator_id']);
        }

        //Search By operator_name
        if (isset($params['operator_name']) && $params['operator_name'] != '') {
            $query->where('operator_name', '=', $params['operator_name']);
        }

        return $query;
    }

    /**
     * 机构资金账户流水表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilAccountFanliRecords::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 机构资金账户流水表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountFanliRecords::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountFanliRecords::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 机构资金账户流水表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilAccountFanliRecords::create($params);
    }

    /**
     * 机构资金账户流水表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountFanliRecords::find($params['id'])->update($params);
    }

    /**
     * 机构资金账户流水表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilAccountFanliRecords::destroy($params['ids']);
    }




}