<?php
/**
 * 机构日充值统计
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/08/14
 * Time: 14:23:16
 */

namespace Models;

use Illuminate\Database\Capsule\Manager as Capsule;

class OilOrgDayCharges extends \Framework\Database\Model
{
    protected $table = 'oil_org_day_charges';

    protected $guarded  = ["id"];
    protected $fillable = ['day', 'orgcode', 'pay_company_id', 'pay_company_name', 'total_cash_money', 'total_fanli_money',
        'total_credit_charge', 'total_credit_borrow', 'createtime', 'updatetime','total_num','charge_fee'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     *
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By day
        if (isset($params['day']) && $params['day'] != '') {
            $query->where('day', '=', $params['day']);
        }

        //Search By orgcode
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $query->where('orgcode', '=', $params['orgcode']);
        }

        //Search By pay_company_id
        if (isset($params['pay_company_id']) && $params['pay_company_id'] != '') {
            $query->where('pay_company_id', '=', $params['pay_company_id']);
        }

        //Search By pay_company_name
        if (isset($params['pay_company_name']) && $params['pay_company_name'] != '') {
            $query->where('pay_company_name', '=', $params['pay_company_name']);
        }

        //Search By total_cash_money
        if (isset($params['total_cash_money']) && $params['total_cash_money'] != '') {
            $query->where('total_cash_money', '=', $params['total_cash_money']);
        }

        //Search By total_fanli_money
        if (isset($params['total_fanli_money']) && $params['total_fanli_money'] != '') {
            $query->where('total_fanli_money', '=', $params['total_fanli_money']);
        }

        //Search By total_credit_charge
        if (isset($params['total_credit_charge']) && $params['total_credit_charge'] != '') {
            $query->where('total_credit_charge', '=', $params['total_credit_charge']);
        }

        //Search By total_credit_borrow
        if (isset($params['total_credit_borrow']) && $params['total_credit_borrow'] != '') {
            $query->where('total_credit_borrow', '=', $params['total_credit_borrow']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 机构日充值统计 列表查询
     *
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page']  = isset($params['page']) ? $params['page'] : 1;
        $sqlObj          = OilOrgDayCharges::Filter($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * 机构日充值统计 详情查询
     *
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilOrgDayCharges::find($params['id']);
    }

    /**
     * 悲观锁查询
     *
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilOrgDayCharges::lockForUpdate()->where('id', $params['id'])->first();
    }

    /**
     * 机构日充值统计 新增
     *
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilOrgDayCharges::create($params);
    }

    /**
     * 机构日充值统计 编辑
     *
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilOrgDayCharges::find($params['id'])->update($params);
    }

    /**
     * 机构日充值统计 根据ids删除或批量删除
     *
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilOrgDayCharges::destroy($params['ids']);
    }

    /**
     * 充值日报统计
     *
     * @param $params
     * @return array
     */
    public static function statisticByDay($params)
    {
        $start_time = isset($params['start_time']) && $params['start_time'] ? $params['start_time'] : date("Y-m-d", strtotime('-1 day')) . ' 00:00:00';
        $end_time   = isset($params['end_time']) && $params['end_time'] ? $params['end_time'] : date("Y-m-d", strtotime('-1 day')) . ' 23:59:59';
        $sql = "insert into oil_org_day_charges
(day,orgcode,latest_time,pay_company_id,pay_company_name,charge_type,total_money,charge_fee,total_num,createtime)
(
SELECT
DATE_FORMAT(a.audit_time, '%Y-%m-%d') as day,b.orgcode,max(a.audit_time),a.pay_company_id,a.pay_name as pay_company_name,a.charge_type,
sum(a.arrival_money) as total_money,
sum(a.arrival_money) as charge_fee,
count(a.id) as total_num,
NOW() as createtime
FROM
oil_account_money_charge a 
left join oil_org b on a.org_id = b.id
where a.audit_time >= '" . $start_time . "' and a.audit_time <= '" . $end_time . "' and a.status = 1
group by a.org_id,DATE_FORMAT(a.audit_time, '%Y-%m-%d'),a.charge_type,a.pay_company_id
)";
        $data = Capsule::connection('default')->select($sql);


        $sqlNew        = "insert into oil_org_day_charges_new
(day,orgroot,orgcode,latest_time,pay_company_id,pay_company_name,charge_type,total_money,total_num,createtime)
(
SELECT
DATE_FORMAT(a.audit_time, '%Y-%m-%d') as day,
if(left(b.orgcode,6) = '201XW3',left(b.orgcode,10),left(b.orgcode,6)) as orgroot,
b.orgcode,max(a.audit_time),a.pay_company_id,a.pay_name as pay_company_name,a.charge_type,
sum(a.arrival_money) as total_money,
count(a.id) as total_num,
NOW() as createtime
FROM
oil_account_money_charge a 
left join oil_org b on a.org_id = b.id
where a.audit_time >= '" . $start_time . "' and a.audit_time <= '" . $end_time . "' and a.status = 1
group by a.org_id,DATE_FORMAT(a.audit_time, '%Y-%m-%d'),a.charge_type,a.pay_company_id
)";
        return Capsule::connection('default')->select($sqlNew);
    }


    public static function getChargeNum($day = "",$orgcode = "",$type = 1,$company_id = 0)
    {
        $sql = "SELECT
count(a.id) as total_num,
sum(a.arrival_money) as total_money
FROM
oil_account_money_charge a 
left join oil_org b on a.org_id = b.id
where a.audit_time like '".$day."%' and a.status = 1 and a.charge_type = ".$type." and b.orgcode = '".$orgcode."'";
        if( !empty($company_id) ){
            $sql .= " and a.pay_company_id = ".$company_id;
        }
        return Capsule::connection('default')->select($sql);
    }

}