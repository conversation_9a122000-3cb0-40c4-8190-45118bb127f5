<?php
/**
 * 欠票统计数据供应商视角数据快照表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2022/06/28
 * Time: 10:56:45
 */
namespace Models;
use Fuel\Defines\CardViceConf;
use Fuel\Defines\CooperationStatus;
use Fuel\Defines\CooperationType;
use Fuel\Defines\StationArea;
use Fuel\Defines\SupplierConf;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilOwingSupplierSnapshot extends \Framework\Database\Model
{
    protected $table = 'oil_owing_supplier_snapshot';

    protected $guarded = ["id"];
    protected $fillable = ['snapshot_data','supplier_id','supplier_name','cooperation_type','operator_name','receipt_statement',
        'receipt_claim','oil_id','oil_name','owing_ticket_total','owing_ticket_const_total','owing_ticket_dynamic_total',
        'owing_ticket_num_total','owing_ticket_price','oil_unit','oil_consumption','supplier_recharge','operator_id',
        'supplier_receipt_return_total','supplier_total_rebate','oil_consumption_total','oil_consumption_flowing_water',
        'oil_adjustment_flowing_water','receipt_money','receipt_discount','receipt_num','owing_day','first_owing_trade',
        'last_receipt_time','last_return_time','settlement_docker','network_docker','remark','createtime','updatetime','oil_arrears_flowing_water'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By snapshot_data
        if (isset($params['snapshot_data']) && $params['snapshot_data'] != '') {
            $shortTime = preg_match('/^\d{4}(\-|\/|.)\d{1,2}\1\d{1,2}$/', $params['snapshot_data'], $matches) ? $params['snapshot_data'] : $params['snapshot_data'];
            $query->where('snapshot_data', 'like', $shortTime."%");
        }

        //Search By supplier_id
        if (isset($params['supplier_id']) && $params['supplier_id'] != '') {
            $query->where('supplier_id', '=', $params['supplier_id']);
        }

        //Search By supplier_name
        if (isset($params['supplier_name']) && $params['supplier_name'] != '') {
            $query->where('supplier_name', '=', $params['supplier_name']);
        }

        //Search By cooperation_type
        if (isset($params['cooperation_type']) && $params['cooperation_type'] != '') {
            $query->where('cooperation_type', '=', $params['cooperation_type']);
        }

        //Search By operator_id
        if (isset($params['operator_id']) && $params['operator_id'] != '') {
            $query->where('operator_id', '=', $params['operator_id']);
        }

        //Search By operator_name
        if (isset($params['operator_name']) && $params['operator_name'] != '') {
            $query->where('operator_name', '=', $params['operator_name']);
        }

        //Search By receipt_statement
        if (isset($params['receipt_statement']) && $params['receipt_statement'] != '') {
            $query->where('receipt_statement', '=', $params['receipt_statement']);
        }

        //Search By receipt_claim
        if (isset($params['receipt_claim']) && $params['receipt_claim'] != '') {
            $query->where('receipt_claim', '=', $params['receipt_claim']);
        }

        //Search By oil_id
        if (isset($params['oil_id']) && $params['oil_id'] != '') {
            $query->where('oil_id', '=', $params['oil_id']);
        }

        //Search By oil_name
        if (isset($params['oil_name']) && $params['oil_name'] != '') {
            $query->where('oil_name', '=', $params['oil_name']);
        }

        //Search By owing_ticket_total
        if (isset($params['owing_ticket_total']) && $params['owing_ticket_total'] != '') {
            $query->where('owing_ticket_total', '=', $params['owing_ticket_total']);
        }

        //Search By owing_ticket_const_total
        if (isset($params['owing_ticket_const_total']) && $params['owing_ticket_const_total'] != '') {
            $query->where('owing_ticket_const_total', '=', $params['owing_ticket_const_total']);
        }

        //Search By owing_ticket_dynamic_total
        if (isset($params['owing_ticket_dynamic_total']) && $params['owing_ticket_dynamic_total'] != '') {
            $query->where('owing_ticket_dynamic_total', '=', $params['owing_ticket_dynamic_total']);
        }

        //Search By owing_ticket_num_total
        if (isset($params['owing_ticket_num_total']) && $params['owing_ticket_num_total'] != '') {
            $query->where('owing_ticket_num_total', '=', $params['owing_ticket_num_total']);
        }

        //Search By owing_ticket_price
        if (isset($params['owing_ticket_price']) && $params['owing_ticket_price'] != '') {
            $query->where('owing_ticket_price', '=', $params['owing_ticket_price']);
        }

        //Search By oil_unit
        if (isset($params['oil_unit']) && $params['oil_unit'] != '') {
            $query->where('oil_unit', '=', $params['oil_unit']);
        }

        //Search By oil_consumption
        if (isset($params['oil_consumption']) && $params['oil_consumption'] != '') {
            $query->where('oil_consumption', '=', $params['oil_consumption']);
        }

        //Search By supplier_recharge
        if (isset($params['supplier_recharge']) && $params['supplier_recharge'] != '') {
            $query->where('supplier_recharge', '=', $params['supplier_recharge']);
        }

        //Search By supplier_receipt_return_total
        if (isset($params['supplier_receipt_return_total']) && $params['supplier_receipt_return_total'] != '') {
            $query->where('supplier_receipt_return_total', '=', $params['supplier_receipt_return_total']);
        }

        //Search By supplier_total_rebate
        if (isset($params['supplier_total_rebate']) && $params['supplier_total_rebate'] != '') {
            $query->where('supplier_total_rebate', '=', $params['supplier_total_rebate']);
        }

        //Search By oil_consumption_total
        if (isset($params['oil_consumption_total']) && $params['oil_consumption_total'] != '') {
            $query->where('oil_consumption_total', '=', $params['oil_consumption_total']);
        }

        //Search By oil_consumption_flowing_water
        if (isset($params['oil_consumption_flowing_water']) && $params['oil_consumption_flowing_water'] != '') {
            $query->where('oil_consumption_flowing_water', '=', $params['oil_consumption_flowing_water']);
        }

        //Search By oil_adjustment_flowing_water
        if (isset($params['oil_adjustment_flowing_water']) && $params['oil_adjustment_flowing_water'] != '') {
            $query->where('oil_adjustment_flowing_water', '=', $params['oil_adjustment_flowing_water']);
        }

        //Search By receipt_money
        if (isset($params['receipt_money']) && $params['receipt_money'] != '') {
            $query->where('receipt_money', '=', $params['receipt_money']);
        }

        //Search By receipt_discount
        if (isset($params['receipt_discount']) && $params['receipt_discount'] != '') {
            $query->where('receipt_discount', '=', $params['receipt_discount']);
        }

        //Search By receipt_num
        if (isset($params['receipt_num']) && $params['receipt_num'] != '') {
            $query->where('receipt_num', '=', $params['receipt_num']);
        }

        //Search By owing_day
        if (isset($params['owing_day']) && $params['owing_day'] != '') {
            $query->where('owing_day', '=', $params['owing_day']);
        }

        //Search By first_owing_trade
        if (isset($params['first_owing_trade']) && $params['first_owing_trade'] != '') {
            $query->where('first_owing_trade', '=', $params['first_owing_trade']);
        }

        //Search By last_receipt_time
        if (isset($params['last_receipt_time']) && $params['last_receipt_time'] != '') {
            $query->where('last_receipt_time', '=', $params['last_receipt_time']);
        }

        //Search By last_return_time
        if (isset($params['last_return_time']) && $params['last_return_time'] != '') {
            $query->where('last_return_time', '=', $params['last_return_time']);
        }

        //Search By settlement_docker
        if (isset($params['settlement_docker']) && $params['settlement_docker'] != '') {
            $query->where('settlement_docker', '=', $params['settlement_docker']);
        }

        //Search By network_docker
        if (isset($params['network_docker']) && $params['network_docker'] != '') {
            $query->where('network_docker', '=', $params['network_docker']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 欠票统计数据供应商视角数据快照表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        //Capsule::connection()->enableQueryLog();
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilOwingSupplierSnapshot::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('owing_ticket_total', 'desc')->get();
        }else{
            $data = $sqlObj->orderBy('owing_ticket_total', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;

        if(count($data) == 0){
            return [];
        }

        $data = $data->toArray();
        $list = [];
        $pergage = false;
        if(isset($data['data'])) {
            $pergage = true;
            $list = $data['data'];
            $ids = array_column($list, "supplier_id");
        }else{
            $list = $data;
            $ids = array_column($data,"supplier_id");
        }

        $sortData = [];
        foreach ($list as &$v) {
            $v['receipt_claim_value'] = SupplierConf::$claim[$v['receipt_claim']];
            $v['receipt_statement_value'] = SupplierConf::$statement[$v['receipt_statement']];
            $v['cooperation_type_value'] = CooperationType::getById($v['cooperation_type']);
            $v['cooperation_status_value'] = CooperationStatus::getById($v['cooperation_status']);
            $v['first_owing_trade'] = isset($v['first_owing_trade']) && $v['first_owing_trade'] != '0000-00-00 00:00:00' ? substr($v['first_owing_trade'],0,10): "";
            $v['last_receipt_time'] = isset($v['last_receipt_time']) && $v['last_receipt_time'] != '0000-00-00 00:00:00' ? substr($v['last_receipt_time'],0,10): "";
            $v['last_return_time'] = isset($v['last_return_time']) && $v['last_return_time'] != '0000-00-00 00:00:00' ? substr($v['last_return_time'],0,10): "";

            $v['snapshot_data'] = substr($v['snapshot_data'],0,10);
            if(in_array($v['supplier_id'],$ids)){
                $sortData[$v['supplier_id']][] = $v;
            }
        }
        $res = [];
        foreach ($sortData as $_one){
            foreach ($_one as $_item) {
                $res[] = $_item;
            }
        }
        if($pergage) {
            $data['data'] = $res;
            return $data;
        }else{
            return $res;
        }
    }

    /**
     * 欠票统计数据供应商视角数据快照表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOwingSupplierSnapshot::find($params['id']);
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOwingSupplierSnapshot::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 欠票统计数据供应商视角数据快照表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilOwingSupplierSnapshot::create($params);
    }

    /**
     * 欠票统计数据供应商视角数据快照表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOwingSupplierSnapshot::find($params['id'])->update($params);
    }

    /**
     * 欠票统计数据供应商视角数据快照表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilOwingSupplierSnapshot::destroy($params['ids']);
    }


    static public function batchAdd($data = []){
        return OilOwingSupplierSnapshot::insert($data);
    }



}