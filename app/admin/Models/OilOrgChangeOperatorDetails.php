<?php
/**
 * 机构换签记录
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2023/02/20
 * Time: 14:22:13
 */

namespace Models;

use Framework\Log;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilOrgChangeOperatorDetails extends \Framework\Database\Model
{
    protected $table = 'oil_org_change_operator_details';
    protected $guarded  = ["id"];
    protected $fillable = ['org_change_id', 'orgcode','change_end_time','status','offline_refund_order','refund_money',
        'trades_id','recharge_status','charge_no','recharge_order', 'creator','createtime', 'updatetime', 'error_msg',
        'sign_end_time','org_name','original_money','actual_money','remark'
    ];


    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * Desc:
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 18/3/23 下午5:49
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function OilOrgChangeOperator()
    {
        return $this->hasOne('Models\OilOrgChangeOperatorLog', 'org_change_id', 'id');
    }
    /**
     * 聚集查询
     *
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('oil_org_change_operator_details.id', '=', $params['id']);
        }

        //Search By orgcode
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $query->where('oil_org_change_operator_details.orgcode', '=', $params['orgcode']);
        }

        if (isset($params['orgcodeNeq']) && $params['orgcodeNeq'] != '') {
            $query->where('oil_org_change_operator_details.orgcode', '!=', $params['orgcodeNeq']);
        }

        //Search By orgcode
        if (isset($params['orgroot']) && $params['orgroot'] != '') {
            $query->where('oil_org_change_operator.orgroot', '=', $params['orgroot']);
        }

        //Search By status
        if (isset($params['statusIn']) && $params['statusIn'] != '') {
            $query->whereIn('oil_org_change_operator_details.status', $params['statusIn']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('oil_org_change_operator_details.status','=', $params['status']);
        }

        //Search By before_operator_id
        if (isset($params['org_change_id']) && $params['org_change_id'] != '') {
            $query->where('org_change_id', '=', $params['org_change_id']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('oil_org_change_operator_details.createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('oil_org_change_operator_details.updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 机构换签记录 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page']  = isset($params['page']) ? $params['page'] : 1;
        $sqlObj          = OilOrgChangeOperatorDetails::Filter($params)
            ->selectRaw('oil_org_change_operator_details.*,oil_org.org_name as oil_org_name')
            ->leftJoin('oil_org', 'oil_org.orgcode', '=', 'oil_org_change_operator_details.orgcode');

        $sqlObj = $sqlObj->where('oil_org.is_del', '=', 0);

        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->orderBy('createtime', 'desc')->get();
        } else {
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * 机构换签记录 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return     $sqlObj= OilOrgChangeOperatorDetails::leftJoin('oil_org_change_operator','oil_org_change_operator.id','=','oil_org_change_operator_details.org_change_id')
            ->select('oil_org_change_operator_details.*','oil_org_change_operator.orgroot','oil_org_change_operator.before_operator_id',
                'oil_org_change_operator.change_time','oil_org_change_operator.remark','oil_org_change_operator.up_receipt_status',
                'oil_org_change_operator.down_receipt_status','oil_org_change_operator.after_operator_id',
                'oil_org_change_operator.before_operator_id')
            ->where('oil_org_change_operator_details.id',$params['id'])
            ->first();
    }


    /**
     * Desc:
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 18/3/23 下午6:25
     * @param array $params
     * @return mixed
     */
    static public function getDetailsByOrgChangeId(array $params)
    {
        \helper::argumentCheck(['id'],$params);
        return OilOrgChangeOperatorDetails::selectRaw('oil_org_change_operator_details.*,oil_org_change_operator.*')
            ->leftJoin('oil_org_change_operator', 'oil_org_change_operator.id', '=', 'oil_org_change_operator_details.org_change_id')
            ->where('oil_org_change_operator.org_change_id',$params['id'])
            ->get();
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgChangeOperatorDetails::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 机构换签记录 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilOrgChangeOperatorDetails::create($params);
    }

    /**
     * 机构换签记录 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgChangeOperatorDetails::find($params['id'])->update($params);
    }

    static public function updateByFilter($condition = [],$params = [])
    {
        return OilOrgChangeOperatorDetails::Filter($condition)->update($params);
    }
    /**
     * 机构换签记录 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilOrgChangeOperatorDetails::destroy($params['ids']);
    }

    static public function getChangeDataByFilter($params = [])
    {
        $field = "oil_org_change_operator_details.id as sub_id,oil_org_change_operator_details.orgcode,
        oil_org_change_operator_details.status as sub_status,oil_org_change_operator_details.change_end_time,
        oil_org_change_operator.*,oil_org_change_operator_details.org_name,oil_org_change_operator_details.original_money";
        $data = OilOrgChangeOperatorDetails::Filter($params)
            ->selectRaw($field)
            ->leftJoin('oil_org_change_operator', 'oil_org_change_operator.id', '=', 'oil_org_change_operator_details.org_change_id')
            ->orderby("oil_org_change_operator_details.orgcode",'desc')
            ->orderby("oil_org_change_operator_details.id",'asc')
            ->get();
        //$sql = Capsule::connection()->getQueryLog();
        //Log::error("getChangeDataByFilter sql:".json_encode($params),[$sql],"sign_mark_");
        return $data;
    }

}