<?php
/**
 * oil_card_vice_supplyer
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/11/26
 * Time: 10:05:31
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCardViceSupplyer extends \Framework\Database\Model
{
    protected $table = 'oil_card_vice_supplyer';

    
    protected $fillable = [];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        return $query;
    }

    /**
     * oil_card_vice_supplyer 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCardViceSupplyer::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_card_vice_supplyer 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardViceSupplyer::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardViceSupplyer::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_card_vice_supplyer 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCardViceSupplyer::create($params);
    }

    /**
     * oil_card_vice_supplyer 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardViceSupplyer::find($params['id'])->update($params);
    }

    /**
     * oil_card_vice_supplyer 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCardViceSupplyer::destroy($params['ids']);
    }

}