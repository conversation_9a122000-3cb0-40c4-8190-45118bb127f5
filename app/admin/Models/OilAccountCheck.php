<?php
/**
 * 三线平账数据
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2017/08/11
 * Time: 12:03:09
 */
namespace Models;
use Framework\Helper;
use Illuminate\Database\Capsule\Manager as Capsule;
use \Framework\Database\Model;
use Ramsey\Uuid\Uuid;

class OilAccountCheck extends Model
{
    protected $table = 'oil_account_check';

    protected $guarded = ["id"];

    protected $fillable = ['org_id','orgcode', 'orgname', 'type','balance','total_charge','total_fanli','total_assign',
        'total_transfer_in','total_transfer_out','status','createtime','updatetime'];

    //disable incrementing id;
    public $incrementing = \TRUE;

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By orgcode
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $query->where('orgcode', '=', $params['orgcode']);
        }

        //Search By type
        if (isset($params['type']) && $params['type'] != '') {
            $query->where('type', '=', $params['type']);
        }

        //Search By balance
        if (isset($params['balance']) && $params['balance'] != '') {
            $query->where('balance', '=', $params['balance']);
        }

        //Search By total_charge
        if (isset($params['total_charge']) && $params['total_charge'] != '') {
            $query->where('total_charge', '=', $params['total_charge']);
        }

        //Search By total_fanli
        if (isset($params['total_fanli']) && $params['total_fanli'] != '') {
            $query->where('total_fanli', '=', $params['total_fanli']);
        }

        //Search By total_assign
        if (isset($params['total_assign']) && $params['total_assign'] != '') {
            $query->where('total_assign', '=', $params['total_assign']);
        }

        //Search By total_transfer_in
        if (isset($params['total_transfer_in']) && $params['total_transfer_in'] != '') {
            $query->where('total_transfer_in', '=', $params['total_transfer_in']);
        }

        //Search By total_transfer_out
        if (isset($params['total_transfer_out']) && $params['total_transfer_out'] != '') {
            $query->where('total_transfer_out', '=', $params['total_transfer_out']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 三线平账数据 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilAccountCheck::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 三线平账数据 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountCheck::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountCheck::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 三线平账数据 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        $params['createtime'] = date("Y-m-d H:i:s");
        return OilAccountCheck::create($params);
    }

    /**
     * 三线平账数据 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountCheck::find($params['id'])->update($params);
    }

    /**
     * 三线平账数据 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilAccountCheck::destroy($params['ids']);
    }

    static public function getByOrgAndType(array $params)
    {
        \helper::argumentCheck(['type', 'org_id'],$params);

        return OilAccountCheck::Filter($params)->first();
    }


}