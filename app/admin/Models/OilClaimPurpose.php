<?php
/**
 * oil_claim_purpose
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/04/20
 * Time: 09:57:46
 */
namespace Models;
use Fuel\Defines\ClaimMoneyStatus;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilClaimPurpose extends \Framework\Database\Model
{
    protected $table = 'oil_claim_purpose';

    protected $guarded = ["id"];
    protected $fillable = ['name','code','ascription','status','creator_name','last_operator','createtime','updatetime','claim_object'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By name
        if (isset($params['name']) && $params['name'] != '') {
            $query->where('name', '=', $params['name']);
        }

        if (isset($params['keyword']) && $params['keyword'] != '') {
            $query->where('name', 'like', '%'.$params['keyword']."%");
        }

        //Search By code
        if (isset($params['code']) && $params['code'] != '') {
            $query->where('code', '=', $params['code']);
        }

        //Search By ascription
        if (isset($params['ascription']) && $params['ascription'] != '') {
            $query->where('ascription', '=', $params['ascription']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        if (isset($params['claim_object']) && $params['claim_object'] != '') {
            $query->where('claim_object', '=', $params['claim_object']);
        }

        //Search By creator_name
        if (isset($params['creator_name']) && $params['creator_name'] != '') {
            $query->where('creator_name', '=', $params['creator_name']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * oil_claim_purpose 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilClaimPurpose::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        if($data){
            foreach ($data as &$v){
                $v->_status = $v->status == 10 ? "正常" : "停用";
                /*switch ($v->claim_object){
                    case 10:
                        $obj_txt = "下游";
                        break;
                    case 20:
                        $obj_txt = "上游";
                        break;
                    case 30:
                        $obj_txt = "其它";
                        break;
                    default:
                        $obj_txt = "-";
                }*/
                $v->claim_object_txt = ClaimMoneyStatus::objectById($v->claim_object);
            }
        }

        return $data;
    }

    /**
     * oil_claim_purpose 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilClaimPurpose::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilClaimPurpose::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_claim_purpose 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilClaimPurpose::create($params);
    }

    /**
     * oil_claim_purpose 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilClaimPurpose::find($params['id'])->update($params);
    }

    /**
     * oil_claim_purpose 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilClaimPurpose::destroy($params['ids']);
    }

    static public function getPurposeMap(array $params)
    {
        $obj = OilClaimPurpose::select('name','id')->where('status',10)->where('id','!=',1);
        if(isset($params['claim_object']) && !empty($params['claim_object'])){
            $obj = $obj->where("claim_object","=",$params['claim_object']);
        }
        if(isset($params['is_org_config']) && $params['is_org_config'] == 1){
            $obj = $obj->whereIn("name",['油品充值常规认领','油品自授信还款']);
        }
        return $obj->get()->toArray();
    }

    static public function getPurposeMapForSerach(array $params)
    {
        return OilClaimPurpose::Filter($params)->select('name','id','claim_object')->get()->toArray();
    }

    static public function getMap()
    {
        return OilClaimPurpose::pluck('name','id')->toArray();
    }

}