<?php
/**
 * 用户角色表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:17
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class GspSysUserRoles extends \Framework\Database\Model
{
    protected $table = 'gsp_sys_user_roles';

    protected $guarded = ["id"];

    protected $fillable = ['user_id','role_id','creator_id','createtime','updatetime','status','is_del'];

    public function getFillAble()
    {
        return $this->fillable;
    }
    
    public function RoleSources()
    {
        return $this->hasMany('Models\GspSysRoleSource','role_id','role_id');
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By user_id
        if (isset($params['user_id']) && $params['user_id'] != '') {
            $query->where('user_id', '=', $params['user_id']);
        }

        //Search By role_id
        if (isset($params['role_id']) && $params['role_id'] != '') {
            $query->where('role_id', '=', $params['role_id']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By is_del
        if (isset($params['is_del']) && $params['is_del'] != '') {
            $query->where('is_del', '=', $params['is_del']);
        }


        return $query;
    }

    /**
     * 用户角色表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = GspSysUserRoles::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 用户角色表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspSysUserRoles::find($params['id']);
    }

    /**
     * 用户角色表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return GspSysUserRoles::create($params);
    }

    /**
     * 用户角色表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspSysUserRoles::find($params['id'])->update($params);
    }

    /**
     * 用户角色表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return GspSysUserRoles::destroy($params['ids']);
    }

    /**
     * 查询指定uid所有role_id
     * @param $uid
     * @return mixed
     */
    static public function getRoleIdsByUid($uid)
    {
        return GspSysUserRoles::where('user_id','=',$uid)
            ->where('is_del','=',0)
            ->where('status','=',1)
            ->pluck('role_id');
    }

    /**
     * 根据用户id查询其对应的资源
     * @return \Illuminate\Database\Eloquent\Collection|static[]
     */
    static public function getSourcesIdByUserId($userId)
    {
        $data = GspSysUserRoles::with(
            [
                'RoleSources' =>  function($query){
                    $query->select('id','role_id','source_id');
                }
            ]
        )
            ->where('user_id','=',$userId)
            ->where('is_del','=','0')
            ->where('status','=',1)
            ->get();

        return $data;
    }




}