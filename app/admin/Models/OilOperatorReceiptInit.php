<?php
/**
 * oil_operator_day_trades
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2022/11/14
 * Time: 10:01:30
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilOperatorReceiptInit extends \Framework\Database\Model
{
    protected $table = 'oil_operator_receipt_init';

    protected $guarded = ["id"];
    protected $fillable = ['flag','classify','up_operator_id','down_operator_id','origin_init_time',
        'oil_super_id','init_stock','card_balance','init_time','trade_id','trade_money','discount_money',
        'operator_num','init_total_fee','day_trade_money','day_discount_money','day_operator_num',
        'day_left_trade_money','day_left_discount_money','day_left_operator_num','remark','createtime',
        'updatetime','is_use','share_trade'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By flag
        if (isset($params['flag']) && $params['flag'] != '') {
            $query->where('flag', '=', $params['flag']);
        }

        //Search By up_operator_id
        if (isset($params['up_operator_id']) && $params['up_operator_id'] != '') {
            $query->where('up_operator_id', '=', $params['up_operator_id']);
        }

        //Search By down_operator_id
        if (isset($params['down_operator_id']) && $params['down_operator_id'] != '') {
            $query->where('down_operator_id', '=', $params['down_operator_id']);
        }

        //Search By oil_base_id
        if (isset($params['oil_supplier_id']) && $params['oil_supplier_id'] != '') {
            $query->where('oil_supplier_id', '=', $params['oil_supplier_id']);
        }

        if (isset($params['init_stock_gt']) && $params['init_stock_gt'] != '') {
            $query->where('init_stock', '>', 0);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By oil_base_id
        if (isset($params['classify']) && $params['classify'] != '') {
            $query->where('classify', '=', $params['classify']);
        }

        //Search By oil_base_id
        if (isset($params['oil_super_id']) && $params['oil_super_id'] != '') {
            $query->where('oil_super_id', '=', $params['oil_super_id']);
        }

        //Search By id
        if (isset($params['id_elt']) && $params['id_elt'] != '') {
            $query->where('id', '!=', $params['id_elt']);
        }


        if (isset($params['is_use']) && $params['is_use'] != '') {
            $query->where('is_use', '=', $params['is_use']);
        }

        if(isset($params['trade_idNull']) && $params['trade_idNull'] == 1){
            $query->where('trade_id', '=', 0);
        }

        if (isset($params['init_time']) && $params['init_time'] != '') {
            $query->where('init_time', '=', $params['init_time']);
        }


        return $query;
    }

    /**
     * oil_operator_day_trades 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        Capsule::connection()->enableQueryLog();
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilOperatorReceiptInit::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('id', 'asc')->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        $sql = Capsule::connection()->getQueryLog();
        // print_r($sql);exit;
        return $data;
    }

    /**
     * oil_operator_day_trades 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOperatorReceiptInit::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOperatorReceiptInit::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_operator_day_trades 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilOperatorReceiptInit::create($params);
    }

    static public function batchAdd(array $params)
    {
        return OilOperatorReceiptInit::insert($params);
    }

    /**
     * oil_operator_day_trades 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOperatorReceiptInit::find($params['id'])->update($params);
    }

    /**
     * oil_operator_day_trades 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilOperatorReceiptInit::destroy($params['ids']);
    }

    static public function oneByFilter($params = [])
    {
        //Capsule::connection()->enableQueryLog();
        $sqlObj = OilOperatorReceiptInit::Filter($params);
        $list = $sqlObj->orderBy("id","desc")->first();
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);
        return $list;
    }
}