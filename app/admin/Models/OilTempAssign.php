<?php
/**
 * 微信分配临时草稿表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/06/29
 * Time: 18:02:22
 */
namespace Models;
use Fuel\Defines\CardFrom;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilTempAssign extends \Framework\Database\Model
{
    protected $table = 'oil_temp_assign';

    protected $guarded = ["id"];
    protected $fillable = ['org_id','vice_id','money','createtime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('oil_temp_assign.id', '=', $params['id']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('oil_temp_assign.org_id', '=', $params['org_id']);
        }

        //Search By vice_id
        if (isset($params['vice_id']) && $params['vice_id'] != '') {
            $query->where('oil_temp_assign.vice_id', '=', $params['vice_id']);
        }

        //Search By money
        if (isset($params['money']) && $params['money'] != '') {
            $query->where('oil_temp_assign.money', '=', $params['money']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('oil_temp_assign.createtime', '=', $params['createtime']);
        }

        return $query;
    }

    /**
     * 微信分配临时草稿表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilTempAssign::Filter($params)->leftJoin('oil_card_vice','oil_card_vice.id','=','oil_temp_assign.vice_id')
            ->select('oil_temp_assign.id','oil_temp_assign.money','oil_temp_assign.vice_id','oil_card_vice.vice_no','oil_card_vice.vice_no',
                'oil_card_vice.oil_com','oil_card_vice.card_from','oil_card_vice.card_main_id','oil_card_vice.truck_no','oil_card_vice.driver_tel',
                'oil_card_vice.card_owner','oil_card_vice.reserve_remain','oil_card_vice.card_remain')
            ->orderBy('oil_temp_assign.createtime','desc');
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }
        foreach($data as $k=>&$v)
        {
            $v->vice_no_short = substr($v->vice_no,-5);
            $v->oil_com_name = \Fuel\Defines\OilCom::getOilCom($v->oil_com);
            $cardFrom = CardFrom::getById($v->card_from);
            $v->_card_from = $cardFrom ? $cardFrom['sub_name'] : '';
        }
        $data = $data->toArray();
        $data['totalMoney'] = $sqlObj->sum('money');
        return $data;
    }

    /**
     * 微信分配临时草稿表 列表查询
     * @param array $params
     * @return array
     */
    static public function getAll(array $params)
    {
        $res = [];

        $sqlObj = OilTempAssign::Filter($params)->leftJoin('oil_card_vice','oil_card_vice.id','=','oil_temp_assign.vice_id')
            ->select('oil_temp_assign.id','oil_temp_assign.money','oil_temp_assign.vice_id','oil_card_vice.vice_no','oil_card_vice.vice_no',
                'oil_card_vice.oil_com','oil_card_vice.card_main_id','oil_card_vice.truck_no','oil_card_vice.driver_tel',
                'oil_card_vice.card_owner','oil_card_vice.reserve_remain','oil_card_vice.card_remain')
            ->orderBy('oil_temp_assign.createtime','desc');

        $data = $sqlObj->get();

        foreach($data as $k=>&$v)
        {
            $v->vice_no_short = substr($v->vice_no,-5);
            $v->oil_com_name = \Fuel\Defines\OilCom::getOilCom($v->oil_com);
        }
        $res['totalMoney'] = $sqlObj->sum('money');
        $res['total'] = $sqlObj->count();
        $res['data'] = $data;
        return $res;
    }

    /**
     * 微信分配临时草稿表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilTempAssign::find($params['id']);
    }

    /**
     * 微信分配临时草稿表 详情查询
     * @param array $params
     * @return object
     */
    static public function getByOrgId(array $params)
    {
        \helper::argumentCheck(['org_id'],$params);

        return OilTempAssign::where('org_id','=',$params['org_id'])->get();
    }

    /**
     * 微信分配临时草稿表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilTempAssign::create($params);
    }

    /**
     * 微信分配临时草稿表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilTempAssign::find($params['id'])->update($params);
    }

    /**
     * 微信分配临时草稿表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilTempAssign::destroy($params['ids']);
    }

    /**
     * 微信分配临时草稿表 根据org_id删除或批量删除
     * @param array $params
     * @return int
     */
    static public function removeByOrgId(array $params)
    {
        \helper::argumentCheck(['org_id'],$params);

        return OilTempAssign::where('org_id', '=', $params['org_id'])->delete();
    }




}