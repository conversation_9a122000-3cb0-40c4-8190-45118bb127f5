<?php
/**
 * 峰松授信账单
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/10/23
 * Time: 15:29:16
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCreditCarrierBill extends \Framework\Database\Model
{
    protected $table = 'oil_credit_carrier_bill';

    protected $guarded = ["id"];
    public  $timestamps = false;
    protected $fillable = ['operators_id','order_no','assign_num','assign_id','order_day','order_fee','adjust_fee','status','remark','createtime','updatetime','settletime','operator_id','operator_name'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By operators_id
        if (isset($params['operators_id']) && $params['operators_id'] != '') {
            $query->where('operators_id', '=', $params['operators_id']);
        }

        //Search By operators_id
        if (isset($params['order_no']) && $params['order_no'] != '') {
            $query->where('order_no', '=', $params['order_no']);
        }

        //Search By order_day
        if (isset($params['day_start_name']) && $params['day_start_name'] != '') {
            $query->where('order_day', '>=', $params['day_start_name']);
        }
        if (isset($params['day_end_name']) && $params['day_end_name'] != '') {
            $query->where('order_day', '<=', $params['day_end_name']);
        }

        //Search By order_fee
        if (isset($params['order_fee']) && $params['order_fee'] != '') {
            $query->where('order_fee', '=', $params['order_fee']);
        }

        //Search By adjust_fee
        if (isset($params['adjust_fee']) && $params['adjust_fee'] != '') {
            $query->where('adjust_fee', '=', $params['adjust_fee']);
        }

        //Search By status
        if (isset($params['settle']) && $params['settle'] != '') {
            $query->where('status', '=', $params['settle']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By operator_id
        if (isset($params['operator_id']) && $params['operator_id'] != '') {
            $query->where('operator_id', '=', $params['operator_id']);
        }

        //Search By operator_name
        if (isset($params['operator_name']) && $params['operator_name'] != '') {
            $query->where('operator_name', '=', $params['operator_name']);
        }

        return $query;
    }

    /**
     * 峰松授信账单 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCreditCarrierBill::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            if($params['is_only'] == 1) {
                $data = $sqlObj->orderBy('id', 'asc')->get();
            }else{
                $data = $sqlObj->orderBy('id', 'desc')->get();
            }
        }else{
            $data = $sqlObj->orderBy('id', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }


        return self::formData($data);
    }

    static public function formData($data)
    {
        if(count($data) == 0){
            return array();
        }
        foreach($data as $_tmp){
            $operators_id[$_tmp->operators_id] = $_tmp->operators_id;
        }
        $operators = OilOperators::getByIdMap(['idList'=>array_keys($operators_id)]);
        foreach ($data as &$_item){
            $_item->operator = $operators[$_item->operators_id];
            $_item->settle_txt = $_item->status == 1 ? '已结算' : '待结算';
            $_item->order_fee = number_format($_item->order_fee,2,'.','');
            $_item->adjust_fee = number_format($_item->adjust_fee,2,'.','');
            if($_item->status == 1) {
                $_item->nopay_fee = "0.00";
                $_item->settle_fee = number_format(($_item->order_fee + $_item->adjust_fee),2,'.',''); //已结金额
            }else{
                $_item->nopay_fee = number_format(($_item->order_fee + $_item->adjust_fee),2,".",'');
                $_item->settle_fee = "0.00";
            }
            $_item->pay_user = '峰松电子';

            $log = OilCreditCarrierBillLog::getList(['bill_id'=>$_item->id]);

            $_item->log_num = count($log);
            /*$statusLog = '<div id="main"><div class="panel">';
            foreach ($log as $val){
                $statusLog .= "<ul>";
                if($val->status == 1){
                    $statusLog .= ' <li><div class="left">已结算</div><div class="right">￥'.number_format($val->money,2,'.','.').'</div></li>';
                    //$statusLog .= "已结算&nbsp;&nbsp;&nbsp;￥".."<br/>";
                }elseif($val->status == 3){
                    $statusLog .= ' <li><div class="left">调整</div><div class="right">￥'.number_format($val->money,2,'.','.').'</div></li>';
                    //$statusLog .= "调整&nbsp;&nbsp;&nbsp;￥".number_format($val->money,2,'.','.')."<br/> ";
                }else{
                    $statusLog .= ' <li><div class="left">待结算</div><div class="right">￥'.number_format($val->money,2,'.','.').'</div></li>';
                    //$statusLog .= "待结算&nbsp;&nbsp;&nbsp;￥".number_format($val->money,2,'.','.')."<br/>";
                }
                $statusLog .= '<li><div class="left">'.$val->operator_name.'</div><div class="right">'.$val->createtime.'</div></li>';
                // $statusLog .= $val->operator_name.'&nbsp;&nbsp;&nbsp;'.;
                $statusLog .= '</ul>';
            }

            $_item->statusTxt = $statusLog;
            $_item->logData = $log;*/
            if($_item->remark == NULL){
                $_item->remark = "";
            }
        }
        return $data;
    }

    /**
     * 峰松授信账单 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCreditCarrierBill::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCreditCarrierBill::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 峰松授信账单 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        global $app;
        $params['createtime'] = \helper::nowTime();
        $params['updatetime'] = \helper::nowTime();
        $params['operator_id'] = $app->myAdmin->id;
        $params['operator_name'] = $app->myAdmin->true_name;
        return OilCreditCarrierBill::create($params);
    }

    /**
     * 峰松授信账单 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        global $app;
        $params['updatetime'] = \helper::nowTime();
        $params['operator_id'] = $app->myAdmin->id;
        $params['operator_name'] = $app->myAdmin->true_name;
        return OilCreditCarrierBill::find($params['id'])->update($params);
    }

    /**
     * 峰松授信账单 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCreditCarrierBill::destroy($params['ids']);
    }

    /**
     * 获取峰松授信运营商
     * @param array $params
     * @return int
     */
    static public function getOperatorIds()
    {
        return OilCreditCarrierBill::groupBy("operators_id")->pluck("operators_id")->toArray();
    }

    /**
     * 生成订单编号
     * @param array $params
     * @return int
     */
    static public function createOrderNo($maxId)
    {
        $no = "JS".date("Ymd",time());
        if(strlen($maxId) < 6){
            $zero = str_repeat("0",(6-strlen($maxId)));
            return $no.$zero.$maxId;
        }else{
            return $no.$maxId;
        }

    }
    /**
     * 获取订单最大id
     * @param array $params
     * @return int
     */
    static public function getMaxId()
    {
        return OilCreditCarrierBill::max("id");
    }

    /**
     * 获取汇总数据总金额
     * @param array $params
     * @return int
     */
    static public function getMainData()
    {
        return self::where("oil_credit_carrier_bill.status",2)
            ->leftJoin("oil_operators","oil_operators.id",'=',"oil_credit_carrier_bill.operators_id")
            ->groupBy("operators_id")
            ->orderBy("oil_operators.id","asc")
            ->selectRaw("sum(order_fee + IFNULL(adjust_fee,0)) as total_nopay,operators_id,oil_operators.`name`")
            ->get()->toArray();
    }

    /**
     * 获取汇总数据总金额
     * @param array $params
     * @return int
     */
    static public function getMainCount()
    {
        return self::leftJoin("oil_operators","oil_operators.id",'=',"oil_credit_carrier_bill.operators_id")
            ->groupBy("operators_id")
            ->orderBy("oil_operators.id","asc")
            ->selectRaw("sum(assign_num) as total_assign_num,operators_id")
            ->pluck("total_assign_num","operators_id")->toArray();
    }

    /**
     * 获取结算状态金额
     * @param array $params
     * @return int
     */
    static public function getSettleData()
    {
        return OilCreditCarrierBill::groupBy("status")->selectRaw("sum(order_fee + IFNULL(adjust_fee,0)) as money,`status`")->pluck("money","status")->toArray();
    }

}