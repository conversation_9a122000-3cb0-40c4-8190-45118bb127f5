<?php
/**
 * oil_card_main
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 07:54:04
 */

namespace Models;

use Framework\Cache;
use Framework\Log;
use Fuel\Defines\AutoAssignTaskType;
use Fuel\Defines\CardFrom;
use Fuel\Defines\CardMain;
use Fuel\Defines\CooperationStatus;
use Fuel\Defines\CooperationType;
use Fuel\Defines\OilCom;
use Fuel\Service\AutoAssign;
use Fuel\Service\CardViceToGos;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCardMain extends \Framework\Database\Model
{
    protected $table = 'oil_card_main';

    protected $guarded = ["id"];

    protected $fillable = [
        'id', 'main_no', 'main_jifen_id', 'main_jifen_no', 'card_from', 'org_id', 'oil_com', 'active_region', 'fanli_region', 'card_owner',
        'operators_id', 'active_time', 'account_remain', 'point_remain', 'remain_syn_time', 'account_name', 'account_password',
        'account_status', 'account_status_desc', 'account_status_time', 'remark', 'is_jifen', 'creator_id', 'last_operator',
        'createtime', 'updatetime', 'main_addr_id', 'is_close', 'close_begin', 'close_end', 'supplyer_id', 'is_encrypt', 'customer_name',
        'origin_supplier_id','property','province_fanli_out','province_trade_out','province_receipt_out','fanli_desc','main_status',
        'card_owner_ID','company_tax','reserve_phone','query_code','goods_limit','register_phone','origin_main_no',
        'main_no_pwd','reserve_name','cw_register_phone','cw_register_name','main_keep_desc'
    ];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 副卡
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function CardVice()
    {
        return $this->hasMany('Models\OilCardVice', 'card_main_id', 'id');
    }

    /**
     * 发卡省份
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function ActiveProvince()
    {
        return $this->belongsTo('Models\OilProvinces', 'active_region', 'id');
    }

    /**
     * 积分可用地区
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function FanLiRegion()
    {
        return $this->belongsTo('Models\OilProvinces', 'fanli_region', 'id');
    }

    /**
     * 创建人
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function Creator()
    {
        return $this->belongsTo('Models\GspSysUsers', 'creator_id', 'id');
    }

    /**
     * 运营商
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function Operators()
    {
        return $this->belongsTo('Models\OilOperators', 'operators_id', 'id');
    }

    /**
     * 收卡地址
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function Mainaddr()
    {
        return $this->belongsTo('Models\OilCardMainAddr', 'main_addr_id', 'id');
    }


    /**
     * 聚集查询
     *
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        if (isset($params['idList']) && $params['idList']) {
            $query->whereIn('id', $params['idList']);
        }

        //Search By id
        if (isset($params['operators_id']) && $params['operators_id'] != '') {
            $query->where('operators_id', '=', $params['operators_id']);
        }

        //Search By main_no
        if (isset($params['main_no']) && $params['main_no'] != '') {
            if (is_array($params['main_no'])) {
                $query->whereIn('main_no', array_unique($params['main_no']));
            } else {
                $query->where('main_no', '=', $params['main_no']);
            }
        }

        //Search By main_no
        if (isset($params['main_no_lk']) && $params['main_no_lk'] != '') {
            $query->where('main_no', 'like', '%' . $params['main_no_lk'] . '%');
        }

        //Search By card_from
        if (isset($params['card_from']) && $params['card_from'] != '') {
            $query->where('card_from', '=', $params['card_from']);
        }

        //Search By card_fromIn
        if (isset($params['card_fromIn']) && $params['card_fromIn'] != '') {
            $query->whereIn('card_from', $params['card_fromIn']);
        }

        //Search By oil_comIn
        if (isset($params['oil_comIn']) && $params['oil_comIn'] != '') {
            $query->whereIn('oil_com', $params['oil_comIn']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By main_jifen_id
        if (isset($params['main_jifen_id']) && $params['main_jifen_id'] != '') {
            $query->where('main_jifen_id', '=', $params['main_jifen_id']);
        }

        //Search By main_jifen_no
        if (isset($params['main_jifen_no']) && $params['main_jifen_no'] != '') {
            $query->where('main_jifen_no', '=', $params['main_jifen_no']);
        }

        //Search By oil_com
        if (isset($params['oil_com']) && $params['oil_com'] != '') {
            $query->where('oil_com', '=', $params['oil_com']);
        }

        //Search By active_region
        if (isset($params['active_region']) && $params['active_region'] != '') {
            $query->where('active_region', '=', $params['active_region']);
        }

        //Search By fanli_region
        if (isset($params['fanli_region']) && $params['fanli_region'] != '') {
            $query->where('fanli_region', '=', $params['fanli_region']);
        }

        //Search By card_owner
        if (isset($params['card_owner']) && $params['card_owner'] != '') {
            $query->where('card_owner', '=', $params['card_owner']);
        }

        //Search By card_owner
        if (isset($params['card_owner_lk']) && $params['card_owner_lk'] != '') {
            $query->where('card_owner', 'like', '%' . $params['card_owner_lk'] . '%');
        }

        //Search By active_time
        if (isset($params['active_time']) && $params['active_time'] != '') {
            $query->where('active_time', '=', $params['active_time']);
        }

        //Search By account_remain
        if (isset($params['account_remain']) && $params['account_remain'] != '') {
            $query->where('account_remain', '=', $params['account_remain']);
        }

        //Search By point_remain
        if (isset($params['point_remain']) && $params['point_remain'] != '') {
            $query->where('point_remain', '=', $params['point_remain']);
        }

        //Search By remain_syn_time
        if (isset($params['remain_syn_time']) && $params['remain_syn_time'] != '') {
            $query->where('remain_syn_time', '=', $params['remain_syn_time']);
        }

        //Search By account_name
        if (isset($params['account_name']) && $params['account_name'] != '') {
            $query->where('account_name', 'like', '%' . $params['account_name'] . '%');
        }

        if (isset($params['account_name_in']) && $params['account_name_in'] != '') {
            $query->whereIn('account_name', $params['account_name_in']);
        }

        //Search By account_nameIn
        if (isset($params['account_nameIn']) && $params['account_nameIn'] != '') {
            $query->whereIn('account_name', $params['account_nameIn']);
        }

        //Search By account_password
        if (isset($params['account_password']) && $params['account_password'] != '') {
            $query->where('account_password', '=', $params['account_password']);
        }

        //Search By account_status
        if (isset($params['account_status']) && $params['account_status'] != '') {
            $query->where('account_status', '=', $params['account_status']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', 'like', $params['remark'] . '%');
        }

        //Search By is_jifen
        if (isset($params['is_jifen']) && $params['is_jifen'] != '') {
            $query->where('is_jifen', '=', $params['is_jifen']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By is_open_invoice
        if (isset($params['is_open_invoice']) && $params['is_open_invoice'] != '') {
            $query->where('is_open_invoice', '=', $params['is_open_invoice']);
        }


        if (isset($params['mainNoList']) && $params['mainNoList'] != '') {
            $query->whereIn('main_no', $params['mainNoList']);
        }

        //Search By is_close
        if (isset($params['is_close']) && $params['is_close'] != '') {
            $query->where('is_close', '=', $params['is_close']);
        }

        //获取要封禁的数据
        if (isset($params['willClose']) && $params['willClose'] != '') {
            $query->where('is_close', '=', 1)->orWhereNotNull("close_begin");
        }

        //获取要封禁的数据
        if (isset($params['supplyer_id']) && $params['supplyer_id'] != '') {
            $query->where('supplyer_id', '=', $params['supplyer_id']);
        }

        //获取要封禁的数据
        if (isset($params['customer_name']) && $params['customer_name'] != '') {
            $query->where('customer_name', '=', $params['customer_name']);
        }

        //获取要封禁的数据
        if (isset($params['customer_name_lk']) && $params['customer_name_lk'] != '') {
            $query->where('customer_name', 'like', '%' . $params['customer_name_lk'] . '%');
        }

        //是否是自营卡
        if (isset($params['property']) && $params['property'] != '') {
            if (strpos($params['property'], ',') === false) {
                $query->where('property', '=', $params['property']);
            } else {
                $query->whereIn('property', explode(',', $params['property']));
            }
        }

        //原始供应商
        if (isset($params['origin_supplier_id']) && $params['origin_supplier_id'] != '') {
            $query->where('origin_supplier_id', '=', $params['origin_supplier_id']);
        }

        if (isset($params['province_fanli_out']) && $params['province_fanli_out'] != '') {
            $query->where('province_fanli_out', '=', $params['province_fanli_out']);
        }

        if (isset($params['province_trade_out']) && $params['province_trade_out'] != '') {
            $query->where('province_trade_out', '=', $params['province_trade_out']);
        }

        if (isset($params['main_status']) && $params['main_status'] != '') {
            $query->where('main_status', '=', $params['main_status']);
        }

        return $query;
    }

    /**
     * oil_card_main 列表查询
     *
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        //Capsule::connection()->enableQueryLog();
        $data            = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page']  = isset($params['page']) ? $params['page'] : 1;
        //Log::error('sql'.var_export($params,true),[],'tim423');
        $sqlObj = OilCardMain::Filter($params)
                 ->with(
                     [
//                                     'CardVice' => function ($query) {
//                                         $query->select("id", "card_main_id");
//                                     },
                         'ActiveProvince',
                         'FanLiRegion',
                         'Creator',
//                                     'Operators',
//                                     'Mainaddr'
                     ]
                 ); //排除托管卡
        if (isset($params['card_from']) && $params['card_from']) {
            $sqlObj->where('card_from', $params['card_from']);
        } else {
            $sqlObj->where('card_from', '!=', 41);
        }

        if (isset($params['count']) && $params['count'] == 1) {
            return $sqlObj->count();
        } elseif (isset($params['take']) && isset($params['skip'])) {
            $data = $sqlObj->orderby("id","desc")->skip(intval($params['skip']))->take(intval($params['take']))->get();
        } elseif (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->orderby("id","desc")->get();
        } else {
            $data = $sqlObj->orderby("id","desc")->paginate($params['limit'], ['*'], 'page', $params['page']);
        }
        //$sql = Capsule::connection()->getQueryLog();
        //Log::error('sql'.var_export($sql,true),[],'tim423');
        $supplyerMap = OilCardSupplyer::getSupplyerMap([]);
        $originSupplierMap=OilStationSupplier::getSupplierMap([]);
        $operator = OilOperators::getIdMapName("company_name");
        $mainAddr = OilCardMainAddr::getIdMapName();

        //数据整理
        if ($data) {
            foreach ($data as &$v) {
                $oil_com         = \Fuel\Defines\OilCom::getById($v->oil_com);
                $v->oil_com_name = $oil_com ? $oil_com['oil_com'] : '';
                $cardFrom        = CardFrom::getById($v->card_from);
                $v->_card_from   = $cardFrom ? $cardFrom['name'] : '';
                if ($v->org_id) {
                    $orgInfo     = OilOrg::getById(['id' => $v->org_id]);
                    $v->_org_id  = $orgInfo ? $orgInfo->orgcode." ".$orgInfo->org_name : '';
                    $v->orgcode  = $orgInfo ? $orgInfo->orgcode : '';
                    $v->org_name = $orgInfo ? $orgInfo->org_name : '';
                }
                $v->province       = $v->ActiveProvince->province;
                $v->active_region  = $v->ActiveProvince->id;
                $v->fanli_province = $v->FanLiRegion->province;
                $v->fanli_region   = $v->FanLiRegion->id;
                $v->true_name      = $v->Creator->true_name;
//                $v->vice_card_num  = count($v->CardVice);
                // 副卡数量优化
                $v->vice_card_num  = OilCardVice::getTotal(['card_main_id'=>$v->id]);
                $v->operators_name = isset($operator[$v->operators_id]) ? $operator[$v->operators_id] : ""; //$v->operators->name;
                //$v->main_addr_name = $v->mainaddr->addr_flag;
                $v->main_addr_name = isset($mainAddr[$v->main_addr_id]) ? $mainAddr[$v->main_addr_id] : "";// $v->mainaddr->addr_flag;
                $v->supplyer_name  = $supplyerMap[$v->supplyer_id] ? $supplyerMap[$v->supplyer_id] : '常规';
                if ($v->is_close == 1) {
                    $v->close_txt = "已封禁";
                } else if ($v->is_close == 2) {
                    $v->close_txt = "未封禁";
                } else {
                    $v->close_txt = "待封禁";
                }
                $v->card_property_val=CardMain::getCardPropertyById($v->property);
                $v->origin_supplier_name=$originSupplierMap[$v->origin_supplier_id] ? $originSupplierMap[$v->origin_supplier_id] : '';
                $v->province_fanli_txt = CardMain::$main_fanli[$v->province_fanli_out] ? CardMain::$main_fanli[$v->province_fanli_out] : "";
                $v->province_trade_txt = CardMain::$main_trade[$v->province_trade_out] ? CardMain::$main_trade[$v->province_trade_out] : "";
                $v->main_status_txt = CardMain::$main_status[$v->main_status] ? CardMain::$main_status[$v->main_status] : "";
                $v->is_open_invoice_txt = $v->is_open_invoice == 10 ? "是" : "否";
                //unset($v->CardVice, $v->account_password);
                unset($v->CardVice);
                //unset($v->CardVice);
            }
        }

        return $data;
    }

    /**
     * 油品主卡表 详情查询
     *
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardMain::find($params['id']);
    }

    static public function getInfoById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        $sqlObj = OilCardMain::where('id', $params['id'])->with(
            [
                'FanLiRegion'
            ]
        );

        return $sqlObj->first();
    }

    /**
     * @title  根据机构获取主卡信息
     * @param array $params
     * @return mixed
     * <AUTHOR>
     */
    static public function getByOrgId(array $params)
    {
        \helper::argumentCheck(['org_id'], $params);

        return OilCardMain::where('org_id', $params['org_id'])->get()->toArray();
    }

    /**
     * oil_card_main 新增
     *
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCardMain::create($params);
    }

    /**
     * oil_card_main 编辑
     *
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardMain::find($params['id'])->update($params);
    }

    /**
     * oil_card_main 根据ids删除或批量删除
     *
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilCardMain::destroy($params['ids']);
    }

    /**
     * 获取主卡运营商
     *
     * @param $params
     * @return mixed
     * author Jaime Du
     */
    static public function getMainOperator($params)
    {
        $data = OilCardMain::where('oil_card_main.main_no', '=', $params)
                           ->leftJoin('oil_operators', 'oil_card_main.operators_id', '=', 'oil_operators.id')
                           ->select('oil_card_main.operators_id', 'oil_operators.name as operators_name', 'oil_operators.merchantID as merchantID')
                           ->first();
        return $data;
    }

    /**
     * 根据卡号获取信息(主卡同步余额专用)
     *
     * @param $mainNosArr
     * @return mixed
     * <AUTHOR>
     */
    static private function getByMainNos($mainNosArr)
    {
        return OilCardMain::whereIn('main_no', $mainNosArr)->whereIn('oil_com', [1, 2, 52, 26])->select('main_no', 'oil_com', 'account_name', 'account_password')->get();
    }


    static public function getPluckFields(array $params, $pluckField)
    {
        return OilCardMain::Filter($params)->pluck($pluckField)->toArray();
    }

    /**
     * 预处理查询任务数据
     *
     * @param $mainNosArr
     * @return array
     * <AUTHOR>
     */
    static public function preTaskData($mainNosArr)
    {
        $data = self::getByMainNos($mainNosArr);
        if (count($data) <= 0) {
            throw new \RuntimeException("主卡信息不存在或油卡类型不合法");
        }
        $task = [];
        foreach ($data as $v) {
            $oilCom                                    = OilCom::getById($v->oil_com);
            $task[$v->main_no]['cardtype']             = $oilCom ? $oilCom['inter_name'] : '';
            $task[$v->main_no]['account']              = ($v->oil_com == OilCom::ZSY or $v->oil_com == OilCom::ZSY_DZ) ?
                $v->main_no : $v->account_name;
            $task[$v->main_no]['level']                = 1;
            $task[$v->main_no]['callback']             = '';
            $task[$v->main_no]['params']['taskType']   = AutoAssignTaskType::getQueryTypeMainNo($v->oil_com);
            $task[$v->main_no]['params']['cardType']   = $oilCom ? $oilCom['inter_name'] : '';
            $task[$v->main_no]['params']['account']    = $v->account_name;
            $task[$v->main_no]['params']['password']   = $v->account_password;
            $task[$v->main_no]['params']['parentcard'] = $v->main_no;
        }

        foreach ($task as &$v) {
            $v['params'] = json_encode($v['params']);
        }

        return $task;
    }

    /**
     * 余额实时刷新
     *
     * @param $mainNos
     * @return bool
     */
    static public function balanceRefresh($mainNos)
    {
        $mainNosArr = explode(',', $mainNos);
        //修改主卡余额刷新状态
        OilCardMain::whereIn('main_no', $mainNosArr)->whereIn('oil_com', [1, 2, 52])->update(['refresh_status' => 5]);
        $taskData = self::preTaskData($mainNosArr);


        global $app;
        $creator_id = $app->myAdmin->id;
        //开启事务
        Capsule::connection()->beginTransaction();
        try {
            $autoAssign = new AutoAssign();
            $autoAssign
                ->setData($taskData)
                ->sendQueryTask(function ($result) use ($creator_id, $taskData) {
                    //得到taskId写入分配任务表
                    if ($result['taskId']) {
                        $data = OilAccountAssignTask::add([
                            'main_no'    => $result['main_no'],
                            'taskId'     => $result['taskId'],
                            'creator_id' => $creator_id,
                            'status'     => 0,
                            'type'       => 2,
                            'createtime' => \helper::nowTime()
                        ]);
                        \Framework\Log::notice('task--', $data, 'balanceRefresh');
                    }

                    return true;
                });
            //事务提交
            Capsule::connection()->commit();
        } catch (\Exception $e) {
            //事务回滚
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        return TRUE;
    }

    static public function getByIdArr(array $cardMainIds, $hasProvince = \TRUE)
    {
        $sqlObj = OilCardMain::whereIn('id', $cardMainIds);
        if ($hasProvince) {
            $sqlObj->with(
                [
                    'FanLiRegion'
                ]
            );
        }

        return $sqlObj->get();
    }

    /**
     * @title  根据主卡号获取主卡ID
     * @param $mainNo
     * @return mixed
     * <AUTHOR>
     */
    static public function getByMainNo($mainNo)
    {
        return self::where('main_no', $mainNo)->first();
    }

    /**
     * 获取账号
     *
     * @param $account_no
     * @return mixed
     */
    static public function getByAccount($account_no)
    {
        return self::where('card_from', CardFrom::CUSTOMER_CARD_SIMPLE)
                   ->where('account_name', $account_no)->get();
    }

    static public function getByAccountArr($account_nos)
    {
        return self::where('card_from', CardFrom::CUSTOMER_CARD_SIMPLE)
                   ->whereIn('account_name', $account_nos)->get();
    }

    static public function getByAccountInfo($account_no)
    {
        return self::where('card_from', CardFrom::CUSTOMER_CARD_SIMPLE)
                   ->where('account_name', $account_no)->first();
    }

    /**
     * @title
     * @desc
     * @param $account_no
     * @param $org_id
     * @return bool
     * @returns
     * []
     * @returns
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
     * @package Models
     */
    static public function updateOrgWithMainCardAndCardVice($account_no, $org_id)
    {
        //修改帐号下所有副卡的所属机构
        $mainIds = OilCardMain::where('account_name', $account_no)->pluck('id')->toArray();
        Log::error('mainIds' . var_export($mainIds, true), [], 'editAccount');
        //修改帐号下所有住卡的所属机构
        OilCardMain::where('account_name', $account_no)->update(['org_id' => $org_id]);

        //得到要删除的卡id
        $cardIds = OilCardVice::whereIn('card_main_id', $mainIds)
                              ->where('card_from', CardFrom::CUSTOMER_CARD_SIMPLE)
                              ->pluck('id')->toArray();

        if ($mainIds) {
            OilCardVice::whereIn('card_main_id', $mainIds)->update(['org_id' => $org_id]);
        }

        if ($cardIds) {
            //删除卡到gos
            CardViceToGos::batchUpdateToGosByViceIds($cardIds);
        }

        return TRUE;
    }

    static public function getCardMainAccountStatus(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        $ids = explode(',', $params['ids']);

        $data = self::whereIn('id', $ids)->select('id', 'account_name', 'account_status', 'account_status_desc', 'account_status_time', 'is_close')->get();

        return $data;
    }

    //根据主卡号，获取ids
    static public function getMainIdsByNo(array $params)
    {
        $data = self::whereIn('main_no', $params)->pluck("id");

        return $data;
    }

    //txb 根据条件获取主卡信息
    static public function getMainInfo(array $condition)
    {
        $condition['account_status'] = 10;
        $condition['is_jifen']       = 0;
        $_mainKey                    = var_export(__METHOD__, true) . json_encode($condition);
        $info                        = Cache::get($_mainKey);
        if (!$info) {
            $info = self::where($condition)->select("id", "main_no", "org_id", "main_jifen_id", "main_jifen_no", "fanli_region")->first();
            Cache::put($_mainKey, $info, 60 * 60 * 6);
        }
        return $info;
    }

    //获取壹号卡的主卡信息
    static public function getFirstCard()
    {
        $condition['account_status'] = 10;
        $condition['is_jifen']       = 0;
        $condition['card_from']      = CardFrom::GAS_CARD;
        return self::where($condition)
                   ->whereIn("oil_com", array(OilCom::GAS_FIRST_CHARGE, OilCom::GAS_FIRST_TALLY))
                   ->pluck("id", "oil_com")
                   ->toArray();
    }

    //获取主卡号
    static public function getMainNo($condition, $column = "main_no")
    {
        $oil_com_in = $condition['oil_com'];
        unset($condition['oil_com']);
        return self::where($condition)
                   ->whereIn("oil_com", $oil_com_in)
                   ->pluck($column)
                   ->toArray();
    }

    //获取已封禁或定时封禁的主卡
    static public function getMainData($params)
    {
        //Capsule::connection()->enableQueryLog();
        $sqlObj = OilCardMain::Filter($params);
        return $sqlObj->get();
        //print_r(Capsule::connection()->getQueryLog());
    }

    //获取所有主卡
    static public function getAllMainNo($column = "main_no", $key = "id")
    {
        return self::pluck($column, $key)
                   ->toArray();
    }

    static public function getOneField($params, $field = "main_no")
    {
        return OilCardMain::Filter($params)->pluck($field);
    }

    /**
     * 取字段值
     *
     * @param array $params
     * @param       $pluckField
     * @return mixed
     */
    static public function getResField(array $params, $pluckField)
    {
        $res = OilCardMain::Filter($params)->pluck($pluckField)->toArray();

        return !empty($res[0]) ? $res[0] : '';
    }

    static public function getCardList(array $params)
    {
        $_beginTime  = $params['start_time'];
        $_endTime    = $params['end_time'];
        $accountList = [];
        if (isset($params['orgcode']) && $params['orgcode']) {
            $accountList = OilCustomerCard::getAccountList(['is_parent' => 1, "orgcode" => $params['orgcode']]);
        }
        if (count($accountList) > 0) {
            $params['account_nameIn'] = $accountList->toArray();
        } else {
            return [];
        }
        $clientEnv = OilConfigure::getBySysKey('client_env');
        $data      = OilCardMain::Filter($params)
                                ->select('id', "main_no", "account_name", "account_password", "oil_com")
                                ->with(
                                    [
                                        'CardVice' => function ($query) {
                                            $query->select("id", "card_main_id", "vice_no");
                                        },
                                    ])
                                ->get();
        $checkData = [];
        if ($data) {
            foreach ($data as $value) {
                $checkArr = [];
                if (!isset($checkData[$value->id])) {
                    $checkArr['cardList'][] = $value->CardVice->vice_no;
                    $checkArr['parentcard'] = $value->main_no;
                    $checkArr['account']    = ($clientEnv && $value->oil_com == OilCom::ZSY) ? $value->main_no : $value->account_name;
                    $checkArr['password']   = $value->account_password;

                    //判断任务类型
                    if (in_array($value->oil_com, [OilCom::ZSH, OilCom::ZCW_ZSH_CYZYK])) {
                        $checkArr['taskType']  = 'zshAssignSummaryCrawler';
                        $checkArr['cardType']  = 'zsh';
                        $checkArr['beginDate'] = $_beginTime;
                        $checkArr['endDate']   = $_endTime;
                    } elseif (in_array($value->oil_com, [OilCom::ZSY])) {
                        $checkArr['cardType']  = 'zsy';
                        $checkArr['taskType']  = 'zsyAssignSummaryCrawler';
                        $checkArr['beginDate'] = $_beginTime;
                        $checkArr['endDate']   = $_endTime;
                    } else {
                        continue;
                    }
                    $checkData[$value->id] = $checkArr;
                } else {
                    $checkData[$value->id]['cardList'][] = $value->CardVice->vice_no;
                }
            }
            return $checkData;
        }
        return [];
    }

    public static function updateByMainNo($params)
    {
        \helper::argumentCheck(['main_no'], $params);

        return OilCardMain::where('main_no', $params['main_no'])->update($params);
    }
    
    /**
     * 取列表
     * @param array $params
     * @param string $fields
     * @param int $limit
     * @return mixed
     */
    static public function getFilterList(array $params, $fields='*', $limit=0)
    {
        $sqlObj = self::Filter($params);

        if (! empty($params['with_supplier'])) {
            $sqlObj->join('oil_supplier_relation', function ($join) {
                $join->on('oil_card_main.main_no', '=', 'oil_supplier_relation.code')
                ->where('cooperation_type', '=', CooperationType::COOPERATION_TYPE_ZK)
                ->where('oil_supplier_relation.status', '=', 1);
            })
            ->join('oil_station_supplier', function ($join) {
                $join->on('oil_supplier_relation.supplier_id', '=', 'oil_station_supplier.id')
                    ->where('cooperation_status', '=', CooperationStatus::COOPERATION_STATUS_IN)
                    ->where('oil_station_supplier.status', '=', 1);
            });
        }
        if (! empty($params['with_region'])) {
            $sqlObj->join('oil_provinces', 'oil_provinces.id', '=', 'oil_card_main.active_region');
        }
        $sqlObj = $sqlObj->select($fields)->orderBy('id', 'desc');
        if (is_numeric($limit) && $limit > 0) {
            $sqlObj->limit($limit);
        }
        return $sqlObj->get()->toArray();
    }

    /**
     * 查询油卡类型 新增主卡使用
     * @param string $oilCom
     * @return array|mixed|null
     */
    static  public function getOilComV2($oilCom='')
    {
        return \Fuel\Defines\OilCom::getAllV2();
    }
}