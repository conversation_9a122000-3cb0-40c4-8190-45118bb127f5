<?php
/**
 * oil_wx_qrcode
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/06/14
 * Time: 16:27:11
 */
namespace Models;

use Illuminate\Database\Capsule\Manager as Capsule;

class OilWxQrcode extends \Framework\Database\Model
{
    /**
     * g7s微信订阅二维码前缀,用于客户订阅油品消息，生成临时二维码之用
     * @var string
     */
    static public $wxQrCodePreFix = '201';

    protected $table = 'oil_wx_qrcode';

    protected $guarded = ["id"];

    protected $fillable = ['org_id', 'endtime', 'ticket', 'url', 'img_url', 'qrcode_content', 'createtime', 'updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By endtime
        if (isset($params['endtime']) && $params['endtime'] != '') {
            $query->where('endtime', '=', $params['endtime']);
        }

        //Search By ticket
        if (isset($params['ticket']) && $params['ticket'] != '') {
            $query->where('ticket', '=', $params['ticket']);
        }

        //Search By url
        if (isset($params['img_url']) && $params['img_url'] != '') {
            $query->where('img_url', '=', $params['img_url']);
        }

        //Search By qrcode_content
        if (isset($params['qrcode_content']) && $params['qrcode_content'] != '') {
            $query->where('qrcode_content', '=', $params['qrcode_content']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }


        return $query;
    }

    /**
     * oil_wx_qrcode 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilWxQrcode::Filter($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * oil_wx_qrcode 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilWxQrcode::find($params['id']);
    }

    /**
     * oil_wx_qrcode 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilWxQrcode::create($params);
    }

    /**
     * oil_wx_qrcode 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilWxQrcode::find($params['id'])->update($params);
    }

    /**
     * oil_wx_qrcode 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilWxQrcode::destroy($params['ids']);
    }

    /**
     * 获取指定的可用的机构二维码信息
     * @param $orgId
     */
    static public function getQrCodeByOrgId($orgId)
    {
        return OilWxQrcode::where('endtime', '>', date("Y-m-d H:i:s"))->where('org_id', '=', $orgId)->first();
    }

    /**
     * 预处理二维码生成字符串
     * @param $id
     * @return string
     */
    static public function preQrCodeContent($id)
    {
        if (!$id) {
            throw new \RuntimeException('id不能为空', 2);
        }
        $padLength = 10 - strlen(self::$wxQrCodePreFix);

        return self::$wxQrCodePreFix . str_pad($id, $padLength, 0, STR_PAD_LEFT);
    }

    /**
     * 生成订阅二维码
     * @param $orgId
     * @return mixed|null
     */
    static public function makeQrCode($orgId)
    {
        if(!$orgId){
            throw new \RuntimeException('机构信息不能为空',2);
        }

        //删除已过期的
        OilWxQrcode::where('org_id','=',$orgId)->delete();
        $data = OilWxQrcode::add(
            [
                'org_id'     => $orgId,
                'createtime' => date("Y-m-d H:i:s")
            ]
        );

        if ($data) {
            $qrCodeContent = OilWxQrcode::preQrCodeContent($data->id);
            try{
                $qrCodeInfo = \Fuel\Request\GasClient::post(
                    [
                        'data'   => ['content'=>$qrCodeContent],
                        'method' => 'gas.api.makeTempQrCode'
                    ]
                );
                OilWxQrcode::edit(
                    [
                        'id'             => $data->id,
                        'endtime'        => $qrCodeInfo->endtime,
                        'ticket'         => $qrCodeInfo->ticket,
                        'url'            => $qrCodeInfo->url,
                        'img_url'        => $qrCodeInfo->imgUrl,
                        'qrcode_content' => $qrCodeContent
                    ]
                );

                $data['img_url'] = $qrCodeInfo->imgUrl;
            }catch (\Exception $e){
                OilWxQrcode::remove(['ids'=>$data->id]);
                throw  new \RuntimeException($e->getMessage(),$e->getCode());
            }
        }

        return $data;
    }

    /**
     *
     * @param $code
     * @return mixed
     */
    static public function getByQrCode($code)
    {
        return OilWxQrcode::where('qrcode_content','=',$code)->first();
    }

    /**
     * 根据orgcode获取订阅二维码
     * @param $code
     * @return mixed
     */
    static public function getQrCodeByOrgCode(array $params)
    {
        \helper::argumentCheck(['orgcode'], $params);

        $orgInfo = OilOrg::getByOrgcode($params['orgcode']);
        if (!$orgInfo) {
            throw new \RuntimeException('该机构还未开通油品业务', 2);
        }
        $qrCodeInfo = OilWxQrcode::getQrCodeByOrgId($orgInfo->id);

        if (!$qrCodeInfo) {
            if(isset($params['wxQrCodePreFix']) && $params['wxQrCodePreFix']){
                self::$wxQrCodePreFix = $params['wxQrCodePreFix'];
            }
            $qrCodeInfo = OilWxQrcode::makeQrCode($orgInfo->id);
        }

        return $qrCodeInfo;
    }

}