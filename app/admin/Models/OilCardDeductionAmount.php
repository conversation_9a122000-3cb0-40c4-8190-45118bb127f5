<?php
/**
 * 卡额度限流表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/10/30
 * Time: 16:17:47
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCardDeductionAmount extends \Framework\Database\Model
{
    protected $table = 'oil_card_deduction_amount';

    protected $guarded = ["id"];
    protected $fillable = ['vice_no','deduction_amount','remark','status','creator','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By vice_no
        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $query->where('vice_no', '=', $params['vice_no']);
        }

        //Search By deduction_amount
        if (isset($params['deduction_amount']) && $params['deduction_amount'] != '') {
            $query->where('deduction_amount', '=', $params['deduction_amount']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By creator
        if (isset($params['creator']) && $params['creator'] != '') {
            $query->where('creator', '=', $params['creator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 卡额度限流表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCardDeductionAmount::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 卡额度限流表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardDeductionAmount::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardDeductionAmount::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 卡额度限流表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCardDeductionAmount::create($params);
    }

    /**
     * 卡额度限流表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardDeductionAmount::find($params['id'])->update($params);
    }

    /**
     * 卡额度限流表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCardDeductionAmount::destroy($params['ids']);
    }

    /*
     * 获取限流卡列表
     */
    static public function getCardAmountMap()
    {
        $res = [];
        $data = OilCardDeductionAmount::where('status',10)->where('deduction_amount','>',0)->get();
        if($data){
            foreach ($data as $val){
                $res[$val->vice_no] = ['id'=>$val->id,'deduction_amount'=>$val->deduction_amount];
            }
        }

        return $res;
    }

    /*
     * 获取限流卡列表
     */
    static public function getCardAmountMapForEdit()
    {
        $res = [];
        $data = OilCardDeductionAmount::where('status',10)->get();
        if($data){
            foreach ($data as $val){
                $res[$val->vice_no] = ['id'=>$val->id,'deduction_amount'=>$val->deduction_amount,'remark'=>$val->remark];
            }
        }

        return $res;
    }




}