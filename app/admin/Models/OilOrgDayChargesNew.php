<?php
/**
 * 站点机构维度日交易数据
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/10/28
 * Time: 15:03:13
 */
namespace Models;

use Carbon\Carbon;

class OilOrgDayChargesNew extends \Framework\Database\Model
{
    protected $table = 'oil_org_day_charges_new';

    protected $guarded = ["id"];
    protected $fillable = [];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        //Search By date
        if (isset($params['date']) && $params['date'] != '') {
            if (strpos($params['date'], ',') === false) {
                $dateObj = Carbon::parse($params['date']);
                // 開始時間
                $beginDay = $dateObj->toDateString();
                // 結束時間
                $endDay = $dateObj->endOfMonth()->toDateString();

                $query->where('oil_org_day_charges_new.day', '>=', $beginDay);
                $query->where('oil_org_day_charges_new.day', '<=', $endDay);
            } else {
                list($s, $e) = explode(',', $params['date']);
                $query->where('oil_org_day_charges_new.day', '>=', Carbon::parse($s)->toDateString());
                $query->where('oil_org_day_charges_new.day', '<=', Carbon::parse($e)->endOfMonth()->toDateString());
            }
        }

        if (isset($params['createtime']) && $params['createtime'] != '') {
            list($startTime, $endTime) = explode(',', $params['createtime']);
            if (!is_null($endTime)) {
                $query->where('oil_org.createtime', '>=', $startTime);
                $query->where('oil_org.createtime', '<=', $endTime);
            }
        }

        //Search By orgroot
        if (isset($params['orgroot']) && $params['orgroot'] != '') {
            $query->where('oil_org_day_charges_new.orgroot', '=', $params['orgroot']);
        }

        if (! empty($params['orgroot_not_in'])) {
            $query->whereNotIn('oil_org_day_charges_new.orgroot', $params['orgroot_not_in']);
        }

        if (! empty($params['charge_type_neq'])) {
            $query->where('charge_type', '!=', $params['charge_type_neq']);
        }

        return $query;
    }
}