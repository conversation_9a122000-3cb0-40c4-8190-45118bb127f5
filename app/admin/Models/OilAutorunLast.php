<?php
/**
 * 计划任务执行情况表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:17
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilAutorunLast extends \Framework\Database\Model
{
    protected $table = 'oil_autorun_last';

    protected $guarded = ["id"];

    protected $fillable = ['subject','status','starttime','endtime','progress','runtype','crontab','runtime','remark','obcount','deleted','createtime','updatetime','createuser','updateuser'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By subject
        if (isset($params['subject']) && $params['subject'] != '') {
            $query->where('subject', '=', $params['subject']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By starttime
        if (isset($params['starttime']) && $params['starttime'] != '') {
            $query->where('starttime', '=', $params['starttime']);
        }

        //Search By endtime
        if (isset($params['endtime']) && $params['endtime'] != '') {
            $query->where('endtime', '=', $params['endtime']);
        }

        //Search By progress
        if (isset($params['progress']) && $params['progress'] != '') {
            $query->where('progress', '=', $params['progress']);
        }

        //Search By runtype
        if (isset($params['runtype']) && $params['runtype'] != '') {
            $query->where('runtype', '=', $params['runtype']);
        }

        //Search By crontab
        if (isset($params['crontab']) && $params['crontab'] != '') {
            $query->where('crontab', '=', $params['crontab']);
        }

        //Search By runtime
        if (isset($params['runtime']) && $params['runtime'] != '') {
            $query->where('runtime', '=', $params['runtime']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By obcount
        if (isset($params['obcount']) && $params['obcount'] != '') {
            $query->where('obcount', '=', $params['obcount']);
        }

        //Search By deleted
        if (isset($params['deleted']) && $params['deleted'] != '') {
            $query->where('deleted', '=', $params['deleted']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By createuser
        if (isset($params['createuser']) && $params['createuser'] != '') {
            $query->where('createuser', '=', $params['createuser']);
        }

        //Search By updateuser
        if (isset($params['updateuser']) && $params['updateuser'] != '') {
            $query->where('updateuser', '=', $params['updateuser']);
        }


        return $query;
    }

    /**
     * 计划任务执行情况表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilAutorunLast::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 计划任务执行情况表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAutorunLast::find($params['id']);
    }

    /**
     * 计划任务执行情况表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilAutorunLast::create($params);
    }

    /**
     * 计划任务执行情况表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAutorunLast::find($params['id'])->update($params);
    }

    /**
     * 计划任务执行情况表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilAutorunLast::destroy($params['ids']);
    }




}