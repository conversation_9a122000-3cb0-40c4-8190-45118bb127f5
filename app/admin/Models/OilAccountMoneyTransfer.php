<?php
/**
 * 充值资金申请表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:17
 */
namespace Models;
use Framework\Helper;
use Framework\Log;
use Fuel\Defines\AccountMoneyTransferStatus;
use Fuel\Defines\DataFrom;
use Fuel\Defines\OilCom;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilAccountMoneyTransfer extends \Framework\Database\Model
{
    protected $table = 'oil_account_money_transfer';

    protected $guarded = ["id"];

    protected $fillable = ['billID','no','sn', 'no_type','org_id','into_org_id','from_orgcode','from_orgname','into_orgcode',
        'into_orgname','app_time','audit_time','money','status','data_from','is_test','creator_id','other_creator_id','other_creator',
        'last_operator_id','last_operator','remark','remark_work','createtime','updatetime','use_fanli','from_phone','from_account_no',
        'into_account_no','into_phone'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function Creator()
    {
        return $this->belongsTo('Models\GspSysUsers','creator_id','id');
    }

    public function Org()
    {
        return $this->belongsTo('Models\OilOrg','org_id','id');
    }


    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By no
        if (isset($params['no']) && $params['no'] != '') {
            $query->where('no', 'like', '%'.$params['no'].'%');
        }

        //Search By billID
        if (isset($params['billID']) && $params['billID'] != '') {
            $query->where('billID', $params['billID']);
        }

        //Search By sn
        if (isset($params['sn']) && $params['sn']) {
            $query->where('sn', '=', $params['sn']);
        }

        //Search By no_type
        if (isset($params['no_type']) && $params['no_type'] != '') {
            $query->where('no_type', '=', $params['no_type']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By into_org_id
        if (isset($params['into_org_id']) && $params['into_org_id'] != '') {
            $query->where('into_org_id', '=', $params['into_org_id']);
        }

        if(isset($params['from_orgcode_flag']) && ($params['from_orgcode_flag'] == 'true' || $params['from_orgcode_flag'])){
            //Search By from_orgcode
            if (isset($params['from_orgcode']) && $params['from_orgcode'] != '') {
                $query->where('from_orgcode', 'like', $params['from_orgcode'].'%');
            }
        }

        //Search By from_orgcode
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $query->where(function($query)use($params){
                $query->where('from_orgcode','like',$params['orgcode'].'%');
                $query->orWhere('into_orgcode','like',$params['orgcode'].'%');
            });
        }

        //Search By from_orgname
        if (isset($params['from_orgname']) && $params['from_orgname'] != '') {
            $query->where('from_orgname', '=', $params['from_orgname']);
        }

        //Search By
        if (isset($params['app_nameLk']) && $params['app_nameLk'] != '') {
            $query->where(function ($query) use($params){
                $query->where('from_orgname', 'like', $params['app_nameLk'] . "%")->where('no_type', 'HB');
                $query->orWhere('other_creator','like',$params['app_nameLk'] . "%");
            });
        }

        //Search By into_orgcode
        if (isset($params['into_orgcode']) && $params['into_orgcode'] != '') {
            $into_orgcode_arr = explode(',',$params['into_orgcode']);
            $query->whereIn('into_orgcode', $into_orgcode_arr);
        }

        //Search By into_orgcode
        if (isset($params['from_orgcode']) && $params['from_orgcode'] != '') {
            $from_orgcode_arr = explode(',',$params['from_orgcode']);
            $query->whereIn('from_orgcode', $from_orgcode_arr);
        }

        //Search By into_orgname
        if (isset($params['into_orgname']) && $params['into_orgname'] != '') {
            $query->where('into_orgname', '=', $params['into_orgname']);
        }

        if (isset($params['money_ge']) && $params['money_ge'] !== ''){
            $query->where('money', '>=', $params['money_ge']);
        }

        if (isset($params['money_le']) && $params['money_le'] !== ''){
            $query->where('money', '<=', $params['money_le']);
        }
        //Search By start_time
        if (isset($params['start_time']) && $params['start_time'] != '') {
            $query->where('app_time', '>=', date("Y-m-d",strtotime($params['start_time'])).' 00:00:00');
        }

        //Search By end_time
        if (isset($params['end_time']) && $params['end_time'] != '') {
            $query->where('app_time', '<=', date("Y-m-d",strtotime($params['end_time'])).' 23:59:59');
        }

        //Search By money
        if (isset($params['money']) && $params['money'] != '') {
            $query->where('money', '=', $params['money']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By data_from
        if (isset($params['data_from']) && $params['data_from'] != '') {
            $query->where('data_from', '=', $params['data_from']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By other_creator_id
        if (isset($params['other_creator_id']) && $params['other_creator_id'] != '') {
            $query->where('other_creator_id', '=', $params['other_creator_id']);
        }

        //Search By other_creator
        if (isset($params['other_creator']) && $params['other_creator'] == '客服') {
            $query->where(function($query){
                $query->whereNull('other_creator');
                $query->orwhere('other_creator','');
            });
        }elseif (isset($params['other_creator']) && $params['other_creator'] != '') {
            $query->where('other_creator', 'like', '%'.$params['other_creator'].'%');
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        if (isset($params['createtimeLne']) && $params['createtimeLne'] != '') {
            $query->where('createtime', '<', $params['createtimeLne']);
        }

        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $query->where('createtime', '<=', $params['createtimeLe']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By updatetime
        if (isset($params['from_account_no']) && $params['from_account_no'] != '') {
            $query->where('from_account_no', 'like', '%'.$params['from_account_no'].'%');
        }

        //Search By updatetime
        if (isset($params['into_account_no']) && $params['into_account_no'] != '') {
            $query->where('into_account_no', 'like', '%'.$params['into_account_no'].'%');
        }

        //Search By updatetime
        if (isset($params['from_phone']) && $params['from_phone'] != '') {
            $query->where('from_phone', 'like', '%'.$params['from_phone'].'%');
        }

        //Search By updatetime
        if (isset($params['into_phone']) && $params['into_phone'] != '') {
            $query->where('into_phone', 'like', '%'.$params['into_phone'].'%');
        }

        //Search By is_test
        if (isset($params['is_test']) && $params['is_test'] != '') {
            $orgId = OilOrg::getByIsTestLike($params['is_test']);
            $query->whereIn('org_id', $orgId);
        }

        if (isset($params['use_fanliGt0']) && $params['use_fanliGt0'] != '') {
            $query->where('use_fanli','>', 0);
        }

        if(isset($params['transfer_no']) && $params['transfer_no']!= '') {
            $query->where('no','=', $params['transfer_no']);
        }


        return $query;
    }

    /**
     * 充值资金申请表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilAccountMoneyTransfer::Filter($params)->with(
            [
                'Creator'   =>  function($query){
                    $query->select('id','true_name');
                },
                'Org'   =>  function($query){
                    $query->select('id','is_test');
                }
            ]
        )
            ->orderBy('app_time','desc');

        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else if  (isset($params['skip']) && isset($params['take'])) {
            $data = $sqlObj->skip(intval($params['skip']))->take(intval($params['take']))->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }
        $sql = Capsule::connection()->getQueryLog();
        if(count($data) > 0){
            $dataFrom = DataFrom::getAll();
            foreach($data as &$v){
                $v->_no_type = $v->no_type == 'HB' ? '油费划拨' : '机构转账';
                $v->true_name = $v->Creator ? $v->Creator->true_name : $v->other_creator;
                $v->is_test = $v->Org ? $v->Org->is_test : '';
                unset($v->Creator);
                unset($v->Org);
                $v->_status = $v->status;
                if($v->is_test == '1'){
                    $v->is_test = "否";
                }else if($v->is_test == '2'){
                    $v->is_test = "是";
                }else{
                    $v->is_test = '';
                }
                $status = AccountMoneyTransferStatus::getById($v->status);
                $v->status = $status && isset($status['status']) ? $status['status'] : '未知';
                $v->data_from = isset($dataFrom[$v->data_from]) ? $dataFrom[$v->data_from] : '未知';

                $v->app_orgCode = $v->Org ? $v->Org->orgcode : "";
                $v->true_name = $v->other_creator ? $v->other_creator : $v->true_name;

                $v->orgroot = $v->app_orgCode ? substr($v->app_orgCode,0,6) : "";
                $v->rootName = "";
                $v->pay_name = $v->from_orgname;
                $v->receive_name = $v->into_orgname;
                $v->app_person = $v->true_name;

                if( $v->no_type == 'HB' ){
                    $one = OilCardVice::getPluckFields(['vice_no'=>$v->from_account_no,"oil_comIn"=>OilCom::getAllFirstList()],"oil_com");
                    $v->pay_name = $one[0] == OilCom::FORTUNE_CARD ? '发财账户' : "充值账户";
                    $v->app_person = $v->from_orgname;
                    $v->receive_person = $v->into_orgname;
                    $v->receive_name = "发财账户";
                }
            }

            $orgRoots    = OilOrg::preOrgRoot($data, ['orgroot']);
            $orgRootInfo = OilOrg::getByOrgCodesMap($orgRoots);
            $data        = OilOrg::convertOrgRoot2OrgName($data, $orgRootInfo, ['orgroot']);
        }

        return $data;
    }

    static public function getTotal(array $params)
    {
        return OilAccountMoneyTransfer::Filter($params)->count();
    }

    /**
     * 充值资金申请表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountMoneyTransfer::find($params['id']);
    }

    /**
     * @title 获取单条记录
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getSingleRecord(array $params)
    {
        return self::Filter($params)->first();
    }

    static public function getByIdList(array $ids, $returnType='array')
    {
        $data = OilAccountMoneyTransfer::whereIn('id', $ids)->get();
        if($returnType == 'array'){
            $data = $data->toArray();
        }

        return $data;
    }

    /**
     * 充值资金申请表 详情查询 for update
     * @param array $params
     * @return object
     */
    static public function getByIdForUpdate(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountMoneyTransfer::where('id','=',$params['id'])->lockForUpdate()->first();
    }

    /**
     * 充值资金申请表 详情查询 for update
     * @param array $params
     * @return object
     */
    static public function getBySnWithOrgCodeForUpdate(array $params)
    {
        \helper::argumentCheck(['sn','from_orgcode'],$params);

        return OilAccountMoneyTransfer::where('sn','=',$params['sn'])->where('from_orgcode','=',$params['from_orgcode'])->lockForUpdate()->first();
    }

    /**
     * @title 计算转账单冻结的返利金额
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param $orgId
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getFreezeFanliTotal($orgId)
    {
        //return self::where('org_id',$orgId)->where('status','!=',1)->sum('use_fanli');
        return self::where('org_id',$orgId)->where('status','!=',1)->where("no_type","!=","HB")->sum('use_fanli');
    }

    static public function listByFilter($params = [])
    {
        return self::Filter($params)->get();
    }

    /**
     * 充值资金申请表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        //$params['sn'] = Helper::uuid();

        return OilAccountMoneyTransfer::create($params);
    }

    /**
     * 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountMoneyTransfer::find($params['id'])->update($params);
    }

    /**
     * 充值资金申请表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilAccountMoneyTransfer::destroy($params['ids']);
    }

    static public function updateBySn(array $params)
    {
        \helper::argumentCheck(['sn'],$params);

        return self::where('sn',$params['sn'])->update($params);
    }

    /**
     * @title   生成单号
     * 编号 + 年月日 + 当日序号最大值+1
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param string head 编号首字母
     * @return string
     * @returns
     * string
     * @returns
     */
    static public function createNo($head = '')
    {
        $no = "";
        if(!empty($head))
        {
            $no = $head.date('ymd');
            $result = Capsule::connection()->select("SELECT MAX(no) as no FROM oil_account_money_transfer WHERE no LIKE '$no%'");
            if($result[0]->no)
            {
                $no .= sprintf("%05d",(substr($result[0]->no,-5) + 1));
            } else
            {
                $no .= '00001';
            }
        }
        return $no;
    }

    /**
     * @title   根据不同参数获得机构信息
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param $params
     * @return array|bool
     * @returns
     * array|bool
     * @returns
     */
    static public function getOrgInfo($params){
        //校验机构号是否有效
        if(!$params['from_org_id'] || !$params['into_org_id']){
            $from_org_info = OilOrg::where('orgcode','=',$params['from_orgcode'])->where('is_del','=',0)->first();
            $into_org_info = OilOrg::where('orgcode','=',$params['into_orgcode'])->where('is_del','=',0)->first();
        }else{
            $from_org_info=OilOrg::where('id','=',$params['from_org_id'])->where('is_del','=',0)->first();
            $into_org_info=OilOrg::where('id','=',$params['into_org_id'])->where('is_del','=',0)->first();
        }

        if (!$from_org_info || !$into_org_info) {
            return false;
        }
        if(substr($from_org_info->orgcode,0,6) != substr($into_org_info->orgcode,0,6)){
            return false;
        }
        return ['from_org_info'=>$from_org_info,'into_org_info'=>$into_org_info];
    }

    /**
     * @title   按机构统计转出金额
     * @desc
     * @version 1.0.0
     * <AUTHOR>
     * @package Models
     * @since
     * @params
     * @return array
     */
    static public function getOrgTransferOut()
    {
        $transferOutSql = 'SELECT
oo.id as org_id,oamt.from_orgcode, SUM(oamt.money) as money
FROM
oil_account_money_transfer oamt
LEFT JOIN oil_org oo on oo.orgcode = oamt.from_orgcode
where oamt.`status` = 1 AND oo.orgcode not like \'200I1A%\' AND  oo.is_del = 0 AND oamt.no_type != \'HB\'
GROUP BY oamt.from_orgcode';

        return Capsule::connection()->select($transferOutSql);
    }

    /**
     * @title   按机构统计转入金额
     * @desc
     * @version 1.0.0
     * <AUTHOR>
     * @package Models
     * @since
     * @params
     * @return array
     */
    static public function getOrgTransferIn()
    {
        $transferInSql = 'SELECT
oo.id as org_id,oamt.into_orgcode, SUM(oamt.money) as money
FROM
oil_account_money_transfer oamt
LEFT JOIN oil_org oo on oo.orgcode = oamt.into_orgcode
where oamt.`status` = 1 AND oo.orgcode not like \'200I1A%\' AND  oo.is_del = 0 AND oamt.no_type != \'HB\'
GROUP BY oamt.into_orgcode';

        return Capsule::connection()->select($transferInSql);
    }

    /**
     *  获取未审核通过的现金转账之和
     * @param array $params
     * @return mixed
     */
    static public function getByTranferUse(array $params)
    {
        \helper::argumentCheck(['fromOrgcode'],$params);

        return OilAccountMoneyTransfer::where('from_orgcode','=',$params['fromOrgcode'])
            ->where('status','=',0)
            ->where('no_type','!=','HB')
            ->where('money','>',0)
            ->sum('money');
    }

    static public function accountCheckForTransferIn()
    {
        $sqlObj = self::select(Capsule::connection()->raw("oil_org.orgcode,oil_org.org_name,sum(oil_account_money_transfer.money) as total_transfer_in,oil_account_money_transfer.into_org_id as org_id"))
            ->where('oil_account_money_transfer.status',1)
            ->where('oil_account_money_transfer.no_type','ZZ')
            ->leftJoin('oil_org', 'oil_account_money_transfer.into_org_id','=','oil_org.id')
            ->groupBy('oil_account_money_transfer.into_org_id');
        $result = $sqlObj->get()->toArray();

        return $result;
    }

    static public function accountCheckForTransferOut()
    {
        $sqlObj = self::select(Capsule::connection()->raw("oil_org.orgcode,oil_org.org_name,sum(oil_account_money_transfer.money) as total_transfer_out,oil_account_money_transfer.org_id"))
            ->where('oil_account_money_transfer.status',1)
            ->where('oil_account_money_transfer.no_type', '!=', 'HB')
            ->leftJoin('oil_org', 'oil_account_money_transfer.org_id','=','oil_org.id')
            ->groupBy('oil_account_money_transfer.org_id');
        $result = $sqlObj->get()->toArray();

        return $result;
    }

    /**
     * @title   按billID获取转账单
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Models
     * @since
     * @params  type filedName required?
     * @param $billID
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getByBillId($billID)
    {
        return self::where('billID',$billID)->first();
    }

    static public function getBySnLock(array $params)
    {
        \helper::argumentCheck(['sn'], $params);

        return self::where('sn', $params['sn'])->lockForUpdate()->first();
    }
}