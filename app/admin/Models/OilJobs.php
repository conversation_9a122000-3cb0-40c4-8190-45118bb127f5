<?php
/**
 * oil_jobs
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/08/05
 * Time: 03:46:46
 */

namespace Models;

use Illuminate\Database\Capsule\Manager as Capsule;
use Framework\Log;

class OilJobs extends \Framework\Database\Model
{
    protected $table = 'oil_jobs';
    
    protected $guarded  = ["id"];
    protected $fillable = [
        'id', 'job_id', 'channel', 'task_name', 'tries', 'tried', 'type',
        'after_time', 'data', 'message', 'status', 'service_name', 'userInfo',
        'createtime', 'updatetime'
    ];
    
    public function getFillAble()
    {
        return $this->fillable;
    }
    
    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }
        
        if (isset($params['type']) && $params['type'] != '') {
            $query->where('type', '=', $params['type']);
        }
        
        //Search By channel
        if (isset($params['channel']) && $params['channel'] != '') {
            $query->where('channel', '=', $params['channel']);
        }
        
        //Search By task_name
        if (isset($params['task_name']) && $params['task_name'] != '') {
            $query->where('task_name', '=', $params['task_name']);
        }
        
        //Search By module_name
        if (isset($params['tries']) && $params['tries'] != '') {
            $query->where('tries', '=', $params['tries']);
        }
        
        //Search By method_name
        if (isset($params['tried']) && $params['tried'] != '') {
            $query->where('tried', '=', $params['tried']);
        }
        
        //Search By data
        if (isset($params['data']) && $params['data'] != '') {
            $query->where('data', '=', $params['data']);
        }
        
        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }
        
        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }
        
        return $query;
    }
    
    /**
     * oil_jobs 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilJobs::Filter($params)->select('id', 'channel', 'task_name', 'tries', 'tried', 'after_time', 'message', 'status', 'createtime', 'updatetime')->orderBy('id', 'desc');
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }
        
        return $data;
    }
    
    static public function getSingleRecord(array $params)
    {
        return OilJobs::Filter($params)->first();
    }
    
    /**
     * oil_jobs 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilJobs::find($params['id']);
    }
    
    /**
     * oil_jobs 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilJobs::create($params);
    }
    
    /**
     * oil_jobs 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilJobs::find($params['id'])->update($params);
    }
    
    /**
     * oil_jobs 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);
        
        return OilJobs::destroy($params['ids']);
    }
    
    
    /**
     * oil_jobs 删除失败的任务
     * @param array $params
     * @return int
     */
    static public function deleteJob(array $params)
    {
        \helper::argumentCheck(['channel'], $params);
        
        return OilJobs::where("channel", $params['channel'])->delete();
    }
    
    static public function deleteByJobId(array $params)
    {
        \helper::argumentCheck(['job_id'], $params);
        
        return OilJobs::where("job_id", $params['job_id'])->delete();
    }
    
    
    /**
     * 获取任务
     * @param string $channel
     */
    static public function getTask($channel = 'default')
    {
        $data = OilJobs::where('channel', '=', $channel)
            ->where('status', '=', 0)
            ->where(function ($query) {
                $query->where('after_time', '<', time())->orWhere('after_time', '=', 0);
            })
            ->where('type', 10)
            ->orderBy('id', 'asc')
            ->first();
        
        return $data;
    }
    
    /**
     * 预处理任务
     * @param null $task
     */
    static public function preTask($task)
    {
        if (!$task || !isset($task->id)) {
            return;
        }
        
        $serializer = new Serializer();
        $taskInfo = json_decode($task->data);
        $closure = $serializer->unserialize($taskInfo->data->closure);
        
        self::fire($closure, $task);
    }
    
    /**
     * 执行任务
     * @param $closure
     * @param $taskId
     */
    static public function fire($closure, $taskId)
    {
        try {
            $closure();
            self::remove(['ids' => $taskId]);
            Log::notice($taskId . ' -- taskFinished', [], 'jobOk');
        } catch (\Exception $e) {
            Log::error($taskId . ' - exception' . $e->getMessage(), ['exception' => strval($e)], 'jobOk');
        }
    }
    
    public static function getByJobId($jobId)
    {
        return self::where('job_id', $jobId)->first();
    }
}