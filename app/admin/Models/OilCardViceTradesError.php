<?php
/**
 * 加油记录同步时无法入库记录
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/07/20
 * Time: 11:49:21
 */

namespace Models;

use Fuel\Defines\CardFrom;
use Illuminate\Database\Capsule\Manager as Capsule;
use Illuminate\Database\Eloquent\SoftDeletes;

class OilCardViceTradesError extends \Framework\Database\Model
{
    use SoftDeletes;

    protected $table = 'oil_card_vice_trades_error';

    protected $fillable = [
        'id', 'dateCreated', 'lastUpdated', 'operator', 'dataUpdated', 'parentCard', 'cardNo', 'tradeId',
        'reward', 'holders', 'amount', 'balance', 'extype', 'address', 'addressPadding', 'fueldate',
        'model', 'count', 'price', 'cardType', 'tradeStatus', 'accountType', 'status', 'reason',
        'createtime', 'updatetime', 'deleted_at'
    ];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     *
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By dateCreated
        if (isset($params['dateCreated']) && $params['dateCreated'] != '') {
            $query->where('dateCreated', '=', $params['dateCreated']);
        }

        //Search By lastUpdated
        if (isset($params['lastUpdated']) && $params['lastUpdated'] != '') {
            $query->where('lastUpdated', '=', $params['lastUpdated']);
        }

        //Search By operator
        if (isset($params['operator']) && $params['operator'] != '') {
            $query->where('operator', '=', $params['operator']);
        }

        //Search By dataUpdated
        if (isset($params['dataUpdated']) && $params['dataUpdated'] != '') {
            $query->where('dataUpdated', '=', $params['dataUpdated']);
        }

        //Search By parentCard
        if (isset($params['parentCard']) && $params['parentCard'] != '') {
            $query->where('parentCard', '=', $params['parentCard']);
        }

        //Search By cardNo
        if (isset($params['cardNo']) && $params['cardNo'] != '') {
            $query->where('cardNo', '=', $params['cardNo']);
        }

        //Search By tradeId
        if (isset($params['tradeId']) && $params['tradeId'] != '') {
            $query->where('tradeId', '=', $params['tradeId']);
        }

        //Search By reward
        if (isset($params['reward']) && $params['reward'] != '') {
            $query->where('reward', '=', $params['reward']);
        }

        //Search By holders
        if (isset($params['holders']) && $params['holders'] != '') {
            $query->where('holders', '=', $params['holders']);
        }

        //Search By amount
        if (isset($params['amount']) && $params['amount'] != '') {
            $query->where('amount', '=', $params['amount']);
        }

        //Search By balance
        if (isset($params['balance']) && $params['balance'] != '') {
            $query->where('balance', '=', $params['balance']);
        }

        //Search By extype
        if (isset($params['extype']) && $params['extype'] != '') {
            $query->where('extype', '=', $params['extype']);
        }

        //Search By address
        if (isset($params['address']) && $params['address'] != '') {
            $query->where('address', '=', $params['address']);
        }

        //Search By addressPadding
        if (isset($params['addressPadding']) && $params['addressPadding'] != '') {
            $query->where('addressPadding', '=', $params['addressPadding']);
        }

        //Search By fueldate
        if (isset($params['fueldate']) && $params['fueldate'] != '') {
            $query->where('fueldate', '=', $params['fueldate']);
        }

        //Search By fueldate
        if (isset($params['fueldateGe']) && $params['fueldateGe'] != '') {
            $query->where('fueldate', '>=', strtotime($params['fueldateGe']));
        }

        //Search By fueldate
        if (isset($params['fueldateLt']) && $params['fueldateLt'] != '') {
            $query->where('fueldate', '<', strtotime($params['fueldateLt']));
        }
        //Search By model
        if (isset($params['model']) && $params['model'] != '') {
            $query->where('model', '=', $params['model']);
        }

        //Search By count
        if (isset($params['count']) && $params['count'] != '') {
            $query->where('count', '=', $params['count']);
        }

        //Search By price
        if (isset($params['price']) && $params['price'] != '') {
            $query->where('price', '=', $params['price']);
        }

        //Search By cardType
        if (isset($params['cardType']) && $params['cardType'] != '') {
            $query->where('cardType', '=', $params['cardType']);
        }

        //Search By tradeStatus
        if (isset($params['tradeStatus']) && $params['tradeStatus'] != '') {
            $query->where('tradeStatus', '=', $params['tradeStatus']);
        }

        //Search By accountType
        if (isset($params['accountType']) && $params['accountType'] != '') {
            $query->where('accountType', '=', $params['accountType']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By createtime
        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('createtime', '>=', $params['createtimeGe']);
        }

        //Search By createtime
        if (isset($params['createtimeLt']) && $params['createtimeLt'] != '') {
            $query->where('createtime', '<=>', $params['createtimeLt']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By deleted_at
        if (isset($params['deleted_at']) && $params['deleted_at'] != '') {
            $query->where('deleted_at', '=', $params['deleted_at']);
        }

        return $query;
    }

    public function CardMain()
    {
        return $this->belongsTo('Models\OilCardMain', 'parentCard', 'main_no');
    }

    /**
     * 加油记录同步时无法入库记录 列表查询
     *
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page']  = isset($params['page']) ? $params['page'] : 1;
        $sqlObj          = OilCardViceTradesError::Filter($params);
        $sqlObj->with([
            'CardMain' => function ($query) {
                return $query->select('id', 'main_no', 'card_from', 'oil_com');
            }
        ]);

        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy('id', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        if (count($data) > 0) {
            foreach ($data as &$v) {
                $v->fueldate    = date("Y-m-d H:i:s", $v->fueldate);
                $v->dateCreated = date("Y-m-d H:i:s", $v->dateCreated / 1000);
                $v->lastUpdated = date("Y-m-d H:i:s", $v->lastUpdated / 1000);
                $v->dataUpdated = date("Y-m-d H:i:s", $v->dataUpdated / 1000);
                $v->card_from   = isset($v->CardMain) && $v->CardMain ? $v->CardMain->card_from : '未知';
                $_card_from = CardFrom::getById($v->card_from);
                if($_card_from){
                    $v->card_from_text = $_card_from['name'];
                }else{
                    $v->card_from_text = '未知';
                }
            }
        }

        return $data;
    }

    /**
     * 加油记录同步时无法入库记录 详情查询
     *
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardViceTradesError::find($params['id']);
    }

    /**
     * 悲观锁查询
     *
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardViceTradesError::lockForUpdate()->where('id', $params['id'])->first();
    }

    /**
     * 加油记录同步时无法入库记录 新增
     *
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        $result = NULL;
        $exist  = self::find($params['id']);
        if (!$exist) {
            $result = OilCardViceTradesError::create($params);
        } else {
            $result = self::edit($params);
        }

        return $result;
    }

    /**
     * 加油记录同步时无法入库记录 编辑
     *
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardViceTradesError::find($params['id'])->update($params);
    }

    /**
     * 加油记录同步时无法入库记录 根据ids删除或批量删除
     *
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilCardViceTradesError::destroy($params['ids']);
    }


}