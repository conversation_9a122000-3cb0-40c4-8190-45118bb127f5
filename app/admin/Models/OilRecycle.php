<?php
/**
 * 回收站
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2017/02/16
 * Time: 15:47:34
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilRecycle extends \Framework\Database\Model
{
    protected $table = 'oil_recycle';

    protected $guarded = ["id"];
    protected $fillable = ['table_name','pk','org_id','no','sn','data','status','operator_id','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By table_name
        if (isset($params['table_name']) && $params['table_name'] != '') {
            $query->where('table_name', '=', $params['table_name']);
        }

        //Search By pk
        if (isset($params['pk']) && $params['pk'] != '') {
            $query->where('pk', '=', $params['pk']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By no
        if (isset($params['no']) && $params['no'] != '') {
            $query->where('no', '=', $params['no']);
        }

        //Search By data
        if (isset($params['data']) && $params['data'] != '') {
            $query->where('data', '=', $params['data']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By operator_id
        if (isset($params['operator_id']) && $params['operator_id'] != '') {
            $query->where('operator_id', '=', $params['operator_id']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 回收站 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilRecycle::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 回收站 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilRecycle::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilRecycle::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 回收站 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilRecycle::create($params);
    }

    /**
     * 回收站 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilRecycle::find($params['id'])->update($params);
    }

    /**
     * 回收站 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilRecycle::destroy($params['ids']);
    }




}