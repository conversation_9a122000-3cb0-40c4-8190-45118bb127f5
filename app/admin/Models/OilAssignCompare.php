<?php
/**
 * 副卡分配对账数据表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/09/29
 * Time: 15:14:30
 */

namespace Models;

use Fuel\Request\dspClient;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilAssignCompare extends \Framework\Database\Model
{
    protected $table = 'oil_assign_compare';

    protected $guarded = ["id"];
    protected $fillable = ['main_no','vice_no', 'task_id','assign_money','assign_jifen', 'trade_place', 'trade_status', 'trade_type', 'assign_time', 'status', 'createtime', 'updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By vice_no
        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $viceNoArr = explode('|',$params['vice_no']);
            if(count($viceNoArr) == 1){
                $query->where('oil_vice_assign.vice_no', 'like', '%'.$params['vice_no'].'%');
            }else{
                $query->whereIn('oil_vice_assign.vice_no', $viceNoArr);
            }
        }

        //Search By main_no
        if (isset($params['main_no']) && $params['main_no'] != '') {
            $query->where('oil_card_main.main_no', 'like', '%'.$params['main_no'].'%');
        }

        //Search By assign_money
        if (isset($params['assign_money']) && $params['assign_money'] != '') {
            $query->where('assign_money', '=', $params['assign_money']);
        }


        //Search By assign_jifen
        if (isset($params['assign_jifen']) && $params['assign_jifen'] != '') {
            $query->where('assign_jifen', '=', $params['assign_jifen']);
        }

        //Search By trade_place
        if (isset($params['trade_place']) && $params['trade_place'] != '') {
            $query->where('oil_vice_assign.trade_place', 'like', '%'.$params['trade_place'].'%');
        }

        //Search By trade_status
        if (isset($params['trade_status']) && $params['trade_status'] != '') {
            $query->where('oil_vice_assign.trade_status', '=', $params['trade_status']);
        }

        //Search By trade_type
        if (isset($params['trade_type']) && $params['trade_type'] != '') {
            $query->where('oil_vice_assign.trade_type', 'like', '%'.$params['trade_type'].'%');
        }

        //Search By assign_time
        if (isset($params['assign_time']) && $params['assign_time'] != '') {
            $query->where('oil_vice_assign.assign_time', '=', $params['assign_time']);
        }

        //Search By assign_timeGe
        if (isset($params['assign_timeGe']) && $params['assign_timeGe'] != '') {
            $query->where('oil_vice_assign.assign_time', '>=', $params['assign_timeGe']);
        }
        //Search By assign_timeLe
        if (isset($params['assign_timeLe']) && $params['assign_timeLe'] != '') {
            $query->where('oil_vice_assign.assign_time', '<=', $params['assign_timeLe']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }


    /**
     * 副卡分配记录表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilAssignCompare::find($params['id']);
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilAssignCompare::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByConditionIdLock(array $params)
    {
        return OilAssignCompare::lockForUpdate()->where($params)->whereIn('status',array(0,2))->first();
    }

    static public function getByTaskIdLock(array $params)
    {
        \helper::argumentCheck(['task_id'], $params);

        return OilAssignCompare::lockForUpdate()->where('task_id',$params['task_id'])->first();
    }

    /**
     * 副卡分配记录表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilAssignCompare::create($params);
    }

    /**
     * PDO批量添加
     * @param array $apiData
     * @return bool|int
     */
    static public function batchInsertByPdo($apiData = [])
    {
        return OilAssignCompare::insert($apiData);
    }


    /**
     * 副卡分配记录表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilAssignCompare::find($params['id'])->update($params);
    }

    /**
     * 副卡分配记录表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilAssignCompare::destroy($params['ids']);
    }

    /**
     * 获取卡分配的对账数据
     * @param array $params
     * @return int
     */
    static public function getAssignData($start_time,$end_time)
    {
        return OilAssignCompare::whereBetween("assign_time",array($start_time,$end_time))
            ->select(Capsule::connection()->raw("sum(if(trade_type = '单位汇总(出)',assign_money * -1,assign_money)) as assign_money,vice_no"))
            ->where("assign_money","!=",0)
            ->groupby("vice_no")
            ->orderby("createtime",'asc')
            ->pluck("assign_money","vice_no")->toArray();
    }

    /**
     * search data from oilAgent
     * @return mixed
     */
    static public function syncAssignData()
    {
        $info = Capsule::connection()->select("select max(api_id) max_api_id from oil_vice_assign");
        $data = dspClient::post(
            [
                'method' => 'huoyunren.gascard.syncAssignData',
                'data' => [
                    'data' => json_encode([
                        'limit' => '1000',
                        'startId' => intval($info[0]->max_api_id),
                    ]),
                    'format' => 'json'
                ]
            ]
        );

        return $data;
    }

    static public function test()
    {
        $data = dspClient::post(
            [
                'method' => 'huoyunren.gascard.listAssignData',
                'data' => [
                    'data' => json_encode([
                        'parentCard' => '1000111100014502445',
                        'page' => 1,
                        'limit' => '100',
                        'beginTime' => '2016-01-01',
                        'endTime' => date('Y-m-d H:i:s'),
                    ]),
                    'format' => 'json'
                ]
            ]
        );

        var_dump($data);
    }


}