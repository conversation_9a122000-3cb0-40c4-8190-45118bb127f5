<?php
/**
 * 中国 省、市、区,县  三级行政词典
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:18
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCity extends \Framework\Database\Model
{
    protected $table = 'oil_city';

    protected $guarded = ["id"];

    protected $fillable = ['code','name','address','level','parent','createtime','updatetime','deletetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By code
        if (isset($params['code']) && $params['code'] != '') {
            $query->where('code', '=', $params['code']);
        }

        //Search By name
        if (isset($params['name']) && $params['name'] != '') {
            $query->where('name', '=', $params['name']);
        }

        //Search By address
        if (isset($params['address']) && $params['address'] != '') {
            $query->where('address', '=', $params['address']);
        }

        //Search By level
        if (isset($params['level']) && $params['level'] != '') {
            $query->where('level', '=', $params['level']);
        }

        //Search By parent
        if (isset($params['parent']) && $params['parent'] != '') {
            $query->where('parent', '=', $params['parent']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By deletetime
        if (isset($params['deletetime']) && $params['deletetime'] != '') {
            $query->where('deletetime', '=', $params['deletetime']);
        }

        return $query;
    }

    /**
     * 中国 省、市、区,县  三级行政词典 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $sqlObj = OilCity::Filter($params);
        $data = $sqlObj->where('level','<=','4')->get();

        return $data;
    }

    /**
     * 中国 省、市、区,县  三级行政词典 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCity::find($params['id']);
    }

    static public function getByIdList(array $params)
    {
        \helper::argumentCheck(['idList'], $params);

        return OilCity::whereIn('id', $params['idList'])->get();
    }

    /**
     * 中国 省、市、区,县  三级行政词典 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCity::create($params);
    }

    /**
     * 中国 省、市、区,县  三级行政词典 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCity::find($params['id'])->update($params);
    }

    /**
     * 中国 省、市、区,县  三级行政词典 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCity::destroy($params['ids']);
    }

    /**
     * getProvinceData
     * @return mixed
     * <AUTHOR>
     * @since ${DATE}
     */
    static public function getProvinceData()
    {
        return OilCity::where('level','=',2)->get();
    }

    /**
     * getCityData
     * @return mixed
     * <AUTHOR>
     * @since ${DATE}
     */
    static public function getCityData()
    {
        return OilCity::where('level','=',3)->get();
    }

    /**
     * getDistrictData
     * @return mixed
     * <AUTHOR>
     * @since ${DATE}
     */
    static public function getDistrictData()
    {
        return OilCity::where('level','=',4)->get();
    }

    /**
     * getSubCityByCode
     * @param array $params
     * @return mixed
     * <AUTHOR>
     * @since ${DATE}
     */
    static public function getSubCityByCode(array $params)
    {
        return OilCity::where('parent','=',$params['code'])->get();
    }

    /**
     * 统一获取省市，如果不传parent则返回省
     * @param array $params
     * @return mixed
     */
    static public function getProvince(array $params)
    {
        $sqlObj = OilCity::orderBy('code','asc');
        if(!isset($params['parent']) || !$params['parent']){
            $sqlObj->where('level','=',2);
        }else{
            $sqlObj->where('parent','=',$params['parent']);
        }

        return $sqlObj->get();
    }

    /**
     * 根据code查询单条记录
     * @param null $code
     * @return mixed
     */
    static public function getByCode($code = NULL)
    {
        return OilCity::where('code','=',$code)->first();
    }

}