<?php
/**
 * oil_trade_day_report
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/01/06
 * Time: 15:26:38
 */

namespace Models;

use Framework\Helper;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilTradeDayReport extends \Framework\Database\Model
{
	protected $table    = 'oil_trade_day_report';
	protected $guarded  = ["id"];
	protected $fillable = [
		'id', 'org_id', 'orgcode', 'orgname', 'cdcid', 'total_amount', 'total_count', 'total_num',
		'ecard_total_amount', 'ecard_total_count', 'ecard_total_num', 'pcard_total_amount',
		'pcard_total_count', 'pcard_total_num', 'day', 'createtime', 'updatetime'
	];
	//disable incrementing id;
	public $incrementing = FALSE;
	
	public function getFillAble()
	{
		return $this->fillable;
	}
	
	/**
	 * 聚集查询
	 * @param $query
	 * @param $params
	 * @return $query
	 */
	public function scopeFilter($query, $params)
	{
		
		//Search By id
		if (isset($params['id']) && $params['id'] != '') {
			$query->where('id', '=', $params['id']);
		}
		
		//Search By org_id
		if (isset($params['org_id']) && $params['org_id'] != '') {
			$query->where('org_id', '=', $params['org_id']);
		}
		
		//Search By orgcode
		if (isset($params['orgcode']) && $params['orgcode'] != '') {
			$query->where('orgcode', '=', $params['orgcode']);
		}
		
		//Search By orgcodeLk
		if (isset($params['orgcodeLk']) && $params['orgcodeLk'] != '') {
			$query->where('orgcode', 'like', $params['orgcodeLk'].'%');
		}
		
		//Search By orgname
		if (isset($params['orgname']) && $params['orgname'] != '') {
			$query->where('orgname', '=', $params['orgname']);
		}
		
		//Search By cdcid
		if (isset($params['cdcid']) && $params['cdcid'] != '') {
			$query->where('cdcid', '=', $params['cdcid']);
		}
		
		//Search By total_amount
		if (isset($params['total_amount']) && $params['total_amount'] != '') {
			$query->where('total_amount', '=', $params['total_amount']);
		}
		
		if (isset($params['total_amount_ge']) && $params['total_amount_ge'] != '') {
			$query->where('total_amount', '>=', $params['total_amount_ge']);
		}
		
		if (isset($params['total_amount_le']) && $params['total_amount_le'] != '') {
			$query->where('total_amount', '<=', $params['total_amount_le']);
		}
		
		//Search By total_count
		if (isset($params['total_count']) && $params['total_count'] != '') {
			$query->where('total_count', '=', $params['total_count']);
		}
		
		if (isset($params['total_count_ge']) && $params['total_count_ge'] != '') {
			$query->where('total_count', '>=', $params['total_count_ge']);
		}
		
		if (isset($params['total_count_le']) && $params['total_count_le'] != '') {
			$query->where('total_count', '<=', $params['total_count_le']);
		}
		
		if (isset($params['total_num_ge']) && $params['total_num_ge'] != '') {
			$query->where('total_num', '>=', $params['total_num_ge']);
		}
		
		if (isset($params['total_count_le']) && $params['total_num_le'] != '') {
			$query->where('total_num', '<=', $params['total_num_le']);
		}
		
		//Search By total_num
		if (isset($params['total_num']) && $params['total_num'] != '') {
			$query->where('total_num', '=', $params['total_num']);
		}
		
		//Search By ecard_total_amount
		if (isset($params['ecard_total_amount']) && $params['ecard_total_amount'] != '') {
			$query->where('ecard_total_amount', '=', $params['ecard_total_amount']);
		}
		
		//Search By ecard_total_count
		if (isset($params['ecard_total_count']) && $params['ecard_total_count'] != '') {
			$query->where('ecard_total_count', '=', $params['ecard_total_count']);
		}
		
		//Search By ecard_total_num
		if (isset($params['ecard_total_num']) && $params['ecard_total_num'] != '') {
			$query->where('ecard_total_num', '=', $params['ecard_total_num']);
		}
		
		//Search By pcard_total_amount
		if (isset($params['pcard_total_amount']) && $params['pcard_total_amount'] != '') {
			$query->where('pcard_total_amount', '=', $params['pcard_total_amount']);
		}
		
		//Search By pcard_total_count
		if (isset($params['pcard_total_count']) && $params['pcard_total_count'] != '') {
			$query->where('pcard_total_count', '=', $params['pcard_total_count']);
		}
		
		//Search By pcard_total_num
		if (isset($params['pcard_total_num']) && $params['pcard_total_num'] != '') {
			$query->where('pcard_total_num', '=', $params['pcard_total_num']);
		}
		
		//Search By day
		if (isset($params['day']) && $params['day'] != '') {
			$query->where('day', '=', $params['day']);
		}
		
		//Search By dayGe
		if (isset($params['dayGe']) && $params['dayGe'] != '') {
			$query->where('day', '>=', $params['dayGe']);
		}
		
		//Search By dayLe
		if (isset($params['dayLe']) && $params['dayLe'] != '') {
			$query->where('day', '<=', $params['dayLe']);
		}
		
		//Search By createtime
		if (isset($params['createtime']) && $params['createtime'] != '') {
			$query->where('createtime', '=', $params['createtime']);
		}
		
		//Search By updatetime
		if (isset($params['updatetime']) && $params['updatetime'] != '') {
			$query->where('updatetime', '=', $params['updatetime']);
		}
		
		return $query;
	}
	
	/**
	 * oil_trade_day_report 列表查询
	 * @param array $params
	 * @return array
	 */
	static public function getList(array $params)
	{
		$data = [];
		
		$params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
		$params['page'] = isset($params['page']) ? $params['page'] : 1;
		$sqlObj = Capsule::connection('online_only_read')->table('oil_trade_day_report');
		$sqlObj = (new self())->scopeFilter($sqlObj,$params);
		if (isset($params['_export']) && $params['_export'] == 1) {
			$data = $sqlObj->get();
		} else {
			$data = $sqlObj->orderBy('day', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
		}
		
		return $data;
	}
	
	/**
	 * oil_trade_day_report 详情查询
	 * @param array $params
	 * @return object
	 */
	static public function getById(array $params)
	{
		\helper::argumentCheck(['id'], $params);
		
		return OilTradeDayReport::find($params['id']);
	}
	
	/**
	 * 悲观锁查询
	 * @param array $params
	 * @return object
	 */
	static public function getByIdLock(array $params)
	{
		\helper::argumentCheck(['id'], $params);
		
		return OilTradeDayReport::lockForUpdate()->where('id', $params['id'])->first();
	}
	
	/**
	 * oil_trade_day_report 新增
	 * @param array $params
	 * @return mixed
	 */
	static public function add(array $params)
	{
		$params['id'] = Helper::uuid();
		return OilTradeDayReport::create($params);
	}
	
	/**
	 * oil_trade_day_report 编辑
	 * @param array $params
	 * @return mixed
	 */
	static public function edit(array $params)
	{
		\helper::argumentCheck(['id'], $params);
		
		return OilTradeDayReport::find($params['id'])->update($params);
	}
	
	/**
	 * oil_trade_day_report 根据ids删除或批量删除
	 * @param array $params
	 * @return int
	 */
	static public function remove(array $params)
	{
		\helper::argumentCheck(['ids'], $params);
		
		return OilTradeDayReport::destroy($params['ids']);
	}
	
	public static function getDayAndOrgId($params)
	{
		return self::where('org_id', $params['org_id'])->where('day', $params['day'])->first();
	}
	
	
}