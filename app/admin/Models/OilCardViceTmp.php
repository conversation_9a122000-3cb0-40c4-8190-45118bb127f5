<?php
/**
 * 油品副卡表临时表（开卡申请子表）
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:18
 */

namespace Models;

use Framework\Database\Model;
use Framework\Log;
use Fuel\Defines\BindStatus;
use Fuel\Defines\CardFrom;
use Fuel\Defines\CardViceAppStatus;
use Fuel\Defines\OilCom;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCardViceTmp extends Model
{
    protected $table = 'oil_card_vice_tmp';

    protected $guarded = ["id"];

    protected $fillable = ['vice_app_id', 'oil_top', 'day_top', 'unit', 'vice_no', 'card_main_id', 'org_id', 'org_id_big',
        'truck_no', 'driver_tel', 'card_owner', 'bind_status', 'remark', 'remark_work', 'creator_id', 'last_operator',
        'createtime', 'updatetime','vice_password','truck_type','id_card'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function CardViceApp()
    {
        return $this->belongsTo('Models\OilCardViceApp', 'vice_app_id', 'id');
    }

    /**
     * @title    创建机构
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     * @returns
     * \Illuminate\Database\Eloquent\Relations\BelongsTo
     * @returns
     */
    public function CreateOrg()
    {
        return $this->belongsTo('Models\OilOrg', 'org_id_big', 'id');
    }



    /**
     * @title    主卡
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     * @returns
     * \Illuminate\Database\Eloquent\Relations\BelongsTo
     * @returns
     */
    public function CardMain()
    {
        return $this->belongsTo('Models\OilCardMain', 'card_main_id', 'id');
    }

    /**
     * @title    创建人
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     * @returns
     * \Illuminate\Database\Eloquent\Relations\BelongsTo
     * @returns
     */
    public function CreateUser()
    {
        return $this->belongsTo('Models\GspSysUsers', 'creator_id', 'id');
    }

    /**
     * @title
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     * @returns
     * \Illuminate\Database\Eloquent\Relations\BelongsTo
     * @returns
     */
    public function PointsUseArea()
    {
        return $this->belongsTo('Models\OilProvinces','fanli_region','id');
    }

    /**
     * @title   返利机构
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     * @returns
     * \Illuminate\Database\Eloquent\Relations\BelongsTo
     * @returns
     */
    public function FanLiOrg()
    {
        return $this->belongsTo('Models\OilOrg','org_id_fanli','id');
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By id
        if (isset($params['unit']) && $params['unit'] != '') {
            $query->where('unit', '=', $params['unit']);
        }

        //Search By vice_app_id
        if (isset($params['vice_app_id']) && $params['vice_app_id'] != '') {
            $query->where('vice_app_id', '=', $params['vice_app_id']);
        }

        //Search By vice_no
        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $query->where('vice_no', '=', $params['vice_no']);
        }

        //Search By card_main_id
        if (isset($params['card_main_id']) && $params['card_main_id'] != '') {
            $query->where('card_main_id', '=', $params['card_main_id']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By org_id_big
        if (isset($params['org_id_big']) && $params['org_id_big'] != '') {
            $query->where('org_id_big', '=', $params['org_id_big']);
        }

        //Search By truck_no
        if (isset($params['truck_no']) && $params['truck_no'] != '') {
            $query->where('truck_no', '=', $params['truck_no']);
        }

        //Search By driver_tel
        if (isset($params['driver_tel']) && $params['driver_tel'] != '') {
            $query->where('driver_tel', '=', $params['driver_tel']);
        }

        //Search By card_owner
        if (isset($params['card_owner']) && $params['card_owner'] != '') {
            $query->where('card_owner', '=', $params['card_owner']);
        }

        //Search By bind_status
        if (isset($params['bind_status']) && $params['bind_status'] != '') {
            $query->where('bind_status', '=', $params['bind_status']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By remark_work
        if (isset($params['remark_work']) && $params['remark_work'] != '') {
            $query->where('remark_work', '=', $params['remark_work']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }


        return $query;
    }

    static public function getDataForExport(array $params)
    {
        Capsule::connection()->enableQueryLog();
        $sqlObj = OilCardViceTmp::select(
            'oil_card_vice_app.no','oil_card_vice_app.supplyer_id','oil_card_vice_app.org_id_big','oil_card_vice_app.org_id_fanli','oil_card_vice_app.fanli_region',
            'oil_card_vice_app.apply_time',
            'oil_card_vice_app.status','oil_card_vice_app.complete_person','oil_card_vice_app.complete_time','oil_card_vice_app.card_from',
            'oil_card_vice_tmp.id','oil_card_vice_tmp.vice_app_id','oil_card_vice_tmp.vice_no','oil_card_vice_tmp.vice_password',
            'oil_card_vice_tmp.card_main_id','oil_card_vice_tmp.truck_no','oil_card_vice_tmp.driver_tel','oil_card_vice_tmp.card_owner',
            'oil_card_vice_tmp.bind_status','oil_card_vice_tmp.remark','oil_card_vice_tmp.remark_work','oil_card_vice_tmp.createtime'
        )
            ->leftJoin('oil_card_vice_app', 'oil_card_vice_app.id', '=', 'oil_card_vice_tmp.vice_app_id')
            ->with([
                "CreateOrg"  => function ($query) {
                    $query->select('id', 'orgcode', 'org_name', 'operators_id');
                },
                "PointsUseArea"    =>  function($query){
                    $query->select('id','province');
                },
                "FanLiOrg"  =>  function($query){
                    $query->select('id','orgcode','org_name','operators_id');
                },
                "CreateUser" => function ($query) {
                    $query->select('id', 'org_id', 'true_name');
                },
                "CardMain"   => function ($query) {
                    $query->select('id', 'main_no');
                }
            ]);

        $cardViceAppModel = new OilCardViceApp();
        $sqlObj = $cardViceAppModel->scopeFilter($sqlObj, $params);
        if(isset($params['count']) && $params['count'] == 1){
            return $sqlObj->count();
        }

        $sqlObj->orderBy('oil_card_vice_app.apply_time','desc');
        if(isset($params['skip']) && isset($params['take'])){
            $data = $sqlObj->skip(intval($params['skip']))->take(intval($params['take']))->get();
        }elseif (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }
        $sql = Capsule::connection()->getQueryLog();
        //Log::error('cardApp-Sql:'.var_export($sql,TRUE),[],'cardAppExportSql');
        if($data && count($data) > 0){
            $supplyerMap = OilCardSupplyer::getSupplyerMap([]);
            foreach($data as $k=>&$v){
                $v->create_org_name = $v->CreateOrg->orgcode .' '. $v->CreateOrg->org_name;
                $v->fanli_org_name = $v->FanLiOrg->orgcode .' '. $v->FanLiOrg->org_name;
                $v->true_name = $v->CreateUser->true_name;
                $v->province = $v->PointsUseArea->province;
                $v->main_no = $v->CardMain->main_no;
                $v->supplyer_name   = $supplyerMap[$v->supplyer_id] ? $supplyerMap[$v->supplyer_id] : '常规';

                $oilCom = OilCom::getById($v->oil_com);
                $v->oil_com_name = $oilCom['name'];
                $cardFrom = CardFrom::getById($v->card_from);
                $v->card_from_name = isset($cardFrom) ? $cardFrom['name'] : '';

                $v->status_name = CardViceAppStatus::getById($v->status);
                $v->bind_status_name = BindStatus::getById($v->bind_status);

                unset($v->CreateOrg,$v->FanLiOrg,$v->CreateUser,$v->PointsUseArea,$v->CardMain);
            }
        }

        return $data;
    }

    /**
     * 油品副卡表临时表（开卡申请子表） 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page']  = isset($params['page']) ? $params['page'] : 1;
        $sqlObj          = OilCardViceTmp::Filter($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * 油品副卡表临时表（开卡申请子表） 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardViceTmp::find($params['id']);
    }

    /**
     * @param $vice_no
     * @return mixed
     */
    public static function getByViceNo($vice_no)
    {
        return self::where('vice_no',$vice_no)->first();
    }

    /**
     * 油品副卡表临时表（开卡申请子表） 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCardViceTmp::create($params);
    }

    /**
     * 油品副卡表临时表（开卡申请子表） 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardViceTmp::find($params['id'])->update($params);
    }

    /**
     * 根据工单id，获取明细id数组
     * @param array $params
     * @return mixed
     */
    static public function getIdListByAppId(array $params)
    {
        return OilCardViceTmp::where('vice_app_id', '=', $params['vice_app_id'])->pluck('id')->toArray();
    }

    /**
     * 油品副卡表临时表（开卡申请子表） 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilCardViceTmp::destroy($params['ids']);
    }

    /**
     * @title   根据$viceAppIds删除
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Models
     * @since
     * @params  type filedName required?
     * @param array $viceAppIds
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function removeByViceAppIds(array $viceAppIds)
    {
        return self::whereIn('vice_app_id', $viceAppIds)->delete();
    }

    static public function countByViceAppId($id)
    {
        return OilCardViceTmp::where('vice_app_id', '=', $id)->count();
    }

    static public function deleteTmp($id)
    {
        $res1 = OilCardViceTmp::where('vice_app_id', '=', $id)->where('truck_no', '=', '')->delete();
        $res2 = OilCardViceTmp::where('vice_app_id', '=', $id)->where('truck_no', '=', NULL)->delete();

        return $res1 + $res2;
    }

    static public function editTmp($id)
    {
        return OilCardViceTmp::where('vice_app_id', '=', $id)->where('truck_no', '!=', '')->update(['vice_no' => '']);
    }

    static public function getTmpByAppId($id)
    {
        return OilCardViceTmp::leftJoin('oil_org', 'oil_card_vice_tmp.org_id', '=', 'oil_org.id')
            ->select('oil_card_vice_tmp.id', 'oil_card_vice_tmp.vice_no', 'oil_card_vice_tmp.truck_no', 'oil_card_vice_tmp.driver_tel',
                'oil_card_vice_tmp.bind_status', 'oil_card_vice_tmp.remark_work', 'oil_org.org_name', 'oil_org.orgcode as org_id',
                'oil_card_vice_tmp.card_owner', 'oil_card_vice_tmp.id_card')
            ->where('oil_card_vice_tmp.vice_app_id', $id)
            ->get()
            ->toArray();
    }
    
    /**
     * 取字段值
     * @param array $params
     * @param $pluckField
     * @return array
     */
    static public function getPluckFields(array $params, $pluckField)
    {
        return self::Filter($params)->pluck($pluckField)->toArray();
    }
    
    /**
     * 取字段值 返回第一个值
     * @param array $params
     * @param $pluckField
     * @return mixed
     */
    static public function getResField(array $params, $pluckField)
    {
        $res = self::Filter($params)->pluck($pluckField)->toArray();
        
        return !empty($res[0]) ? $res[0] : '';
    }
    
    /**
     * 模糊查询1号卡卡号
     * @param $params
     * @return mixed
     */
    public static function searchCardViceNos($params)
    {
        $sqlObj = self::whereNotNull('vice_no')->where('vice_no', '!=', '');
        
        $needSearch = true;
        if (!empty($params['vice_noLike'])) {
            if (strlen($params['vice_noLike']) >= 4) {
                $sqlObj->where('vice_no', 'like', '%'.$params['vice_noLike'])
                    ->orWhere('vice_no', 'like', $params['vice_noLike'].'%');
            } else {
                $needSearch = false;
            }
        }
        
        $data = [];
        if ($needSearch) {
            $res = $sqlObj->limit(20)->pluck('vice_no');
            $tmpData = !$res ? [] : $res->toArray();
            foreach ($tmpData as $key => $val) {
                $data[$key]['vice_no'] = $val;
            }
        }
        
        return $data;
    }
}