<?php
/**
 * oil_org_day_stat
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/04/09
 * Time: 18:41:48
 */
namespace Models;
use Fuel\Defines\TradesType;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilOrgDayStat extends \Framework\Database\Model
{
    protected $table = 'oil_org_day_stat';

    protected $guarded = ["id"];
    protected $fillable = ['report_id','report_date','orgroot','org_name','cdc_id','crm_name','day_total_charge','day_total_money','day_card_num','sign_time',
        'open_org_time','first_open_time','first_charge_time','first_trade_time','new_charge_time','new_trade_time','sign_saler','aplowneridname','new_department',
        'new_area','oil_cross_centername','new_fossilsupporter','accountnumber','saleEmail','oil_centername','oil_cross_sale','month_total_charge','month_total_money',
        'last_month_total_charge','last_month_total_money','total_card_num','trade_card_num','total_charge','total_money','createtime','updatetime','day_electron_total_money',
        'day_entity_total_money','day_electron_card_num','day_entity_card_num','month_electron_total_money','month_entity_total_money','last_month_electron_total_money',
        'last_month_entity_total_money','total_electron_card_num','total_entity_card_num','trade_electron_card_num','trade_entity_card_num','total_electron_money','total_entity_money'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By report_id
        if (isset($params['report_id']) && $params['report_id'] != '') {
            $query->where('report_id', '=', $params['report_id']);
        }

        //Search By report_date
        if (isset($params['report_date']) && $params['report_date'] != '') {
            $query->where('report_date', '=', $params['report_date']);
        }

        //Search By report_date
        if (isset($params['report_dateGe']) && $params['report_dateGe'] != '') {
            $query->where('report_date', '>=', $params['report_dateGe']);
        }

        //Search By report_date
        if (isset($params['report_dateLe']) && $params['report_dateLe'] != '') {
            $query->where('report_date', '<=', $params['report_dateLe']);
        }

        //Search By orgroot
        if (isset($params['orgroot']) && $params['orgroot'] != '') {
            $query->where('orgroot', '=', $params['orgroot']);
        }

        //Search By org_name
        if (isset($params['org_name']) && $params['org_name'] != '') {
            $query->where('org_name', '=', $params['org_name']);
        }

        //Search By cdc_id
        if (isset($params['cdc_id']) && $params['cdc_id'] != '') {
            $query->where('cdc_id', '=', $params['cdc_id']);
        }

        //Search By crm_name
        if (isset($params['crm_name']) && $params['crm_name'] != '') {
            $query->where('crm_name', '=', $params['crm_name']);
        }

        //Search By day_total_charge
        if (isset($params['day_total_charge']) && $params['day_total_charge'] != '') {
            $query->where('day_total_charge', '=', $params['day_total_charge']);
        }

        //Search By day_total_money
        if (isset($params['day_total_money']) && $params['day_total_money'] != '') {
            $query->where('day_total_money', '=', $params['day_total_money']);
        }

        //Search By day_card_num
        if (isset($params['day_card_num']) && $params['day_card_num'] != '') {
            $query->where('day_card_num', '=', $params['day_card_num']);
        }

        //Search By sign_time
        if (isset($params['sign_time']) && $params['sign_time'] != '') {
            $query->where('sign_time', '=', $params['sign_time']);
        }

        //Search By open_org_time
        if (isset($params['open_org_time']) && $params['open_org_time'] != '') {
            $query->where('open_org_time', '=', $params['open_org_time']);
        }

        //Search By first_open_time
        if (isset($params['first_open_time']) && $params['first_open_time'] != '') {
            $query->where('first_open_time', '=', $params['first_open_time']);
        }

        //Search By first_charge_time
        if (isset($params['first_charge_time']) && $params['first_charge_time'] != '') {
            $query->where('first_charge_time', '=', $params['first_charge_time']);
        }

        //Search By first_trade_time
        if (isset($params['first_trade_time']) && $params['first_trade_time'] != '') {
            $query->where('first_trade_time', '=', $params['first_trade_time']);
        }

        //Search By new_charge_time
        if (isset($params['new_charge_time']) && $params['new_charge_time'] != '') {
            $query->where('new_charge_time', '=', $params['new_charge_time']);
        }

        //Search By new_trade_time
        if (isset($params['new_trade_time']) && $params['new_trade_time'] != '') {
            $query->where('new_trade_time', '=', $params['new_trade_time']);
        }

        //Search By sign_saler
        if (isset($params['sign_saler']) && $params['sign_saler'] != '') {
            $query->where('sign_saler', '=', $params['sign_saler']);
        }

        //Search By aplowneridname
        if (isset($params['aplowneridname']) && $params['aplowneridname'] != '') {
            $query->where('aplowneridname', '=', $params['aplowneridname']);
        }

        //Search By aplowneridname
        if (isset($params['aplowneridname_lk']) && $params['aplowneridname_lk'] != '') {
            $query->where('aplowneridname', 'like', '%'.$params['aplowneridname'].'%');
        }

        //Search By new_department
        if (isset($params['new_department']) && $params['new_department'] != '') {
            $query->where('new_department', '=', $params['new_department']);
        }

        //Search By new_department
        if (isset($params['new_department_lk']) && $params['new_department_lk'] != '') {
            $query->where('new_department', 'like', '%'.$params['new_department_lk'].'%');
        }

        //Search By new_area
        if (isset($params['new_area']) && $params['new_area'] != '') {
            $query->where('new_area', '=', $params['new_area']);
        }

        //Search By new_area
        if (isset($params['new_area_lk']) && $params['new_area_lk'] != '') {
            $query->where('new_area', 'like', '%'.$params['new_area_lk'].'%');
        }

        //Search By oil_cross_centername
        if (isset($params['oil_cross_centername']) && $params['oil_cross_centername'] != '') {
            $query->where('oil_cross_centername', '=', $params['oil_cross_centername']);
        }

        //Search By new_fossilsupporter
        if (isset($params['new_fossilsupporter']) && $params['new_fossilsupporter'] != '') {
            $query->where('new_fossilsupporter', '=', $params['new_fossilsupporter']);
        }

        //Search By new_fossilsupporter
        if (isset($params['new_fossilsupporter_lk']) && $params['new_fossilsupporter_lk'] != '') {
            $query->where('new_fossilsupporter', 'like', '%'.$params['new_fossilsupporter_lk'].'%');
        }

        //Search By accountnumber
        if (isset($params['accountnumber']) && $params['accountnumber'] != '') {
            $query->where('accountnumber', '=', $params['accountnumber']);
        }

        //Search By saleEmail
        if (isset($params['saleEmail']) && $params['saleEmail'] != '') {
            $query->where('saleEmail', '=', $params['saleEmail']);
        }

        //Search By oil_centername
        if (isset($params['oil_centername']) && $params['oil_centername'] != '') {
            $query->where('oil_centername', '=', $params['oil_centername']);
        }

        //Search By oil_cross_sale
        if (isset($params['oil_cross_sale']) && $params['oil_cross_sale'] != '') {
            $query->where('oil_cross_sale', '=', $params['oil_cross_sale']);
        }

        //Search By month_total_charge
        if (isset($params['month_total_charge']) && $params['month_total_charge'] != '') {
            $query->where('month_total_charge', '=', $params['month_total_charge']);
        }

        //Search By month_total_money
        if (isset($params['month_total_money']) && $params['month_total_money'] != '') {
            $query->where('month_total_money', '=', $params['month_total_money']);
        }

        //Search By last_month_total_charge
        if (isset($params['last_month_total_charge']) && $params['last_month_total_charge'] != '') {
            $query->where('last_month_total_charge', '=', $params['last_month_total_charge']);
        }

        //Search By last_month_total_money
        if (isset($params['last_month_total_money']) && $params['last_month_total_money'] != '') {
            $query->where('last_month_total_money', '=', $params['last_month_total_money']);
        }

        //Search By total_card_num
        if (isset($params['total_card_num']) && $params['total_card_num'] != '') {
            $query->where('total_card_num', '=', $params['total_card_num']);
        }

        //Search By trade_card_num
        if (isset($params['trade_card_num']) && $params['trade_card_num'] != '') {
            $query->where('trade_card_num', '=', $params['trade_card_num']);
        }

        //Search By total_charge
        if (isset($params['total_charge']) && $params['total_charge'] != '') {
            $query->where('total_charge', '=', $params['total_charge']);
        }

        //Search By total_money
        if (isset($params['total_money']) && $params['total_money'] != '') {
            $query->where('total_money', '=', $params['total_money']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * oil_org_day_stat 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilOrgDayStat::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('report_date', 'desc')->get();
        }else{
            $data = $sqlObj->orderBy('report_date', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_org_day_stat 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgDayStat::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgDayStat::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 查询单挑数据
     * @param array $params
     * @return object
     */
    static public function getOneInfo(array $params)
    {
        return OilOrgDayStat::Filter($params)->first();
    }

    /**
     * oil_org_day_stat 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilOrgDayStat::create($params);
    }

    /**
     * oil_org_day_stat 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgDayStat::find($params['id'])->update($params);
    }

    /**
     * oil_org_day_stat 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilOrgDayStat::destroy($params['ids']);
    }

    static public function cronOrgDayAction(array $params)
    {
//        $actionDate = date_format( DATE_SUB( curdate( ), INTERVAL 1 DAY ), '%Y-%m-%d' ); //mysql
        $actionDateDay = isset($params['day']) && $params['day'] ? $params['day'] : date('Y-m-d',strtotime('-1 day'));
        $actionDateMonth = date('Y-m',strtotime($actionDateDay));
        $actionDateLastMonth = date('Y-m',strtotime('-1 month',strtotime($actionDateDay)));

        //非无车机构
        $sql = "SELECT
	* 
FROM
	(
	SELECT
		CONCAT( date_format( DATE_SUB( curdate( ), INTERVAL 1 DAY ), '%Y%m%d' ), a.orgcode ) report_id,
		'".$actionDateDay."' report_date,#'报告日期',
		a.orgcode orgroot,#'机构编码',
		a.org_name,#'机构名称',
		a.crm_id cdc_id,
		b.NAME 'crm_name',#'crm客户名称',
		c.day_total_money,#'当日累计消费',
		d.day_total_charge,#'当日累计充值',
		e.day_card_num,#'当日消费卡数',
		p.archive_time sign_time,#签约时间
		a.createtime 'open_org_time',#'油卡开户时间',
		f.first_open_time,#'首次开卡时间',
		g.new_charge_time,#'最近充值时间',
		g.first_charge_time,#'首次充值时间',
		o.new_trade_time,#'最近消费时间',
		o.first_trade_time,#'首次消费时间',
		h.month_total_money,#'本月消费金额',
		i.last_month_total_money,#'上月消费金额',
		j.month_total_charge,#'本月充值金额',
		k.last_month_total_charge,#'上月充值金额',
		l.total_card_num,#'累计开卡数量',
		m.total_money,#'累计消费金额',
		m.trade_card_num,#'累计消费卡数',
		n.total_charge,#'累计充值金额',
		b.aplowneridname,#'产品线销售人员',
		b.saleEmail,#'产品线销售邮箱',
		b.oil_centername,#'费控系统预算部门名称',
		b.oil_cross_sale,#'油品协销人员',
		b.accountnumber,#'crm客户编码',
		b.sign_aplowneridname 'sign_saler',#签约销售
		b.new_department,#当日归属省区
		b.new_area,#当日归属大区
		b.new_fossilsupporter,#油品支持人员
		b.oil_cross_centername #当日归属事业部
	FROM
		(
		SELECT
			orgcode,
			org_name,
			crm_id,
			createtime 
		FROM
			oil_org 
		WHERE
			LENGTH( orgcode ) = 6 
			AND LEFT ( orgcode, 6 ) != '201XW3' 
			AND crm_id != '' 
			AND is_del = 0 
			AND is_test = 1 
		) a
		LEFT JOIN oil_crm_org b ON a.crm_id = b.cdc_id
		LEFT JOIN oil_contract p ON p.customer_no = b.accountnumber
		LEFT JOIN (
		#机构当日消费
		SELECT
			sum( a.trade_money ) day_total_money,
			LEFT ( b.orgcode, 6 ) orgcode 
		FROM
			oil_card_vice_trades a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			a.trade_type IN ( ".TradesType::getCashAndSelfOilTradeTypeArr(true).") 
			AND a.oil_com IN ( 20, 21 ) 
			AND date_format( a.createtime, '%Y-%m-%d' ) = '".$actionDateDay."' 
		GROUP BY
			LEFT ( b.orgcode, 6 ) 
		) c ON a.orgcode = c.orgcode
		LEFT JOIN (
		#当日充值
		SELECT
			sum( a.arrival_money ) day_total_charge,
			LEFT ( b.orgcode, 6 ) orgcode 
		FROM
			oil_account_money_charge a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			date_format( a.createtime, '%Y-%m-%d' ) = '".$actionDateDay."' 
			AND a.`status` = 1 
			AND a.charge_type != 2 
		GROUP BY
			LEFT ( b.orgcode, 6 ) 
		) d ON a.orgcode = d.orgcode
		LEFT JOIN (
		#当日消费卡数
		SELECT
			count( DISTINCT a.vice_no ) day_card_num,
			LEFT ( b.orgcode, 6 ) orgcode 
		FROM
			oil_card_vice_trades a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			a.trade_type IN ( ".TradesType::getCashAndSelfOilTradeTypeArr(true)." ) 
			AND a.oil_com IN ( 20, 21 ) 
			AND date_format( a.createtime, '%Y-%m-%d' ) = '".$actionDateDay."' 
		GROUP BY
			LEFT ( b.orgcode, 6 ) 
		) e ON a.orgcode = e.orgcode
		LEFT JOIN (
		#首次消费时间最近消费时间
		SELECT
			max( a.createtime ) new_trade_time,
			min( a.createtime ) first_trade_time,
			LEFT ( b.orgcode, 6 ) orgcode 
		FROM
			oil_card_vice_trades a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			a.trade_type IN ( ".TradesType::getCashAndSelfOilTradeTypeArr(true).") 
			AND a.oil_com IN ( 20, 21 ) 
		GROUP BY
			LEFT ( b.orgcode, 6 ) 
		) o ON a.orgcode = o.orgcode
		LEFT JOIN (
		#客户首次开卡时间
		SELECT
			min( a.createtime ) first_open_time,
			LEFT ( b.orgcode, 6 ) orgcode 
		FROM
			oil_card_vice a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		GROUP BY
			LEFT ( b.orgcode, 6 ) 
		) f ON a.orgcode = f.orgcode
		LEFT JOIN (
		#客户首次充值时间和最新充值时间
		SELECT
			max( a.createtime ) new_charge_time,
			min( a.createtime ) first_charge_time,
			LEFT ( b.orgcode, 6 ) orgcode 
		FROM
			oil_account_money_charge a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			a.STATUS = 1 
			AND a.charge_type != 2 
		GROUP BY
			LEFT ( b.orgcode, 6 ) 
		) g ON a.orgcode = g.orgcode
		LEFT JOIN (
		#客户本月总消费
		SELECT
			sum( a.trade_money ) month_total_money,
			LEFT ( b.orgcode, 6 ) orgcode 
		FROM
			oil_card_vice_trades a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			a.trade_type IN ( ".TradesType::getCashAndSelfOilTradeTypeArr(true).") 
			AND a.oil_com IN ( 20, 21 ) 
			AND date_format( a.createtime, '%Y-%m' ) = '".$actionDateMonth."' 
		GROUP BY
			LEFT ( b.orgcode, 6 ) 
		) h ON a.orgcode = h.orgcode
		LEFT JOIN (
		#客户上月总消费
		SELECT
			sum( a.trade_money ) last_month_total_money,
			LEFT ( b.orgcode, 6 ) orgcode 
		FROM
			oil_card_vice_trades a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			a.trade_type IN ( ".TradesType::getCashAndSelfOilTradeTypeArr(true).") 
			AND a.oil_com IN ( 20, 21 ) 
			AND date_format( a.createtime, '%Y-%m' ) = '".$actionDateLastMonth."' 
		GROUP BY
			LEFT ( b.orgcode, 6 ) 
		) i ON a.orgcode = i.orgcode
		LEFT JOIN (
		#客户本月充值
		SELECT
			sum( a.arrival_money ) month_total_charge,
			LEFT ( b.orgcode, 6 ) orgcode 
		FROM
			oil_account_money_charge a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			date_format( a.createtime, '%Y-%m' ) = '".$actionDateMonth."'  
			AND a.`status` = 1 
			AND a.charge_type != 2 
		GROUP BY
			LEFT ( b.orgcode, 6 ) 
		) j ON a.orgcode = j.orgcode
		LEFT JOIN (
		#客户上月充值
		SELECT
			sum( a.arrival_money ) last_month_total_charge,
			LEFT ( b.orgcode, 6 ) orgcode 
		FROM
			oil_account_money_charge a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			date_format( a.createtime, '%Y-%m' ) = '".$actionDateLastMonth."' 
			AND a.`status` = 1 
			AND a.charge_type != 2 
		GROUP BY
			LEFT ( b.orgcode, 6 ) 
		) k ON a.orgcode = k.orgcode
		LEFT JOIN (
		#累计开卡数量
		SELECT
			count( DISTINCT a.vice_no ) total_card_num,
			LEFT ( b.orgcode, 6 ) orgcode 
		FROM
			oil_card_vice a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			a.oil_com IN ( 20, 21 ) 
		GROUP BY
			LEFT ( b.orgcode, 6 ) 
		) l ON a.orgcode = l.orgcode
		LEFT JOIN (
		#客户累计消费金额和累计消费卡数
		SELECT
			sum( a.trade_money ) total_money,
			count( DISTINCT vice_no ) trade_card_num,
			LEFT ( b.orgcode, 6 ) orgcode 
		FROM
			oil_card_vice_trades a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			a.trade_type IN ( ".TradesType::getCashAndSelfOilTradeTypeArr(true)." ) 
			AND a.oil_com IN ( 20, 21 ) 
		GROUP BY
			LEFT ( b.orgcode, 6 ) 
		) m ON a.orgcode = m.orgcode
		LEFT JOIN (
		#客户累计充值
		SELECT
			sum( a.arrival_money ) total_charge,
			LEFT ( b.orgcode, 6 ) orgcode 
		FROM
			oil_account_money_charge a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			a.`status` = 1 
			AND a.charge_type != 2 
		GROUP BY
			LEFT ( b.orgcode, 6 ) 
		) n ON a.orgcode = n.orgcode 
	) a 
WHERE
	( day_total_money > 0 OR day_total_charge > 0 OR date_format( open_org_time, '%Y-%m-%d' ) = '".$actionDateDay."')";
        //'加油', 'IC卡消费', '油品脱机消费', '油品联机消费', '现金加油', '撬装加油'
        //echo $sql;exit;
        $data = Capsule::connection('online_only_read')->select($sql);

        if($data){
            foreach ($data as $val){
                $val = (array)$val;
                try{
                    self::add($val);
                }catch (\Exception $e)
                {

                }
            }
        }

        echo '非无车机构日报统计完毕'.count($data).'条|';

        //无车机构
        $sql2 = "SELECT
	* 
FROM
	(
	SELECT
		CONCAT( date_format( DATE_SUB( curdate( ), INTERVAL 1 DAY ), '%Y%m%d' ), a.orgcode ) report_id,
		'".$actionDateDay."' report_date,#'报告日期',
		a.orgcode orgroot,#'机构编码',
		a.org_name,#'机构名称',
		a.crm_id cdc_id,
		b.NAME 'crm_name',#'crm客户名称',
		c.day_total_money,#'当日累计消费',
		d.day_total_charge,#'当日累计充值',
		e.day_card_num,#'当日消费卡数',
		p.archive_time sign_time,#签约时间
		a.createtime 'open_org_time',#'油卡开户时间',
		f.first_open_time,#'首次开卡时间',
		g.new_charge_time,#'最近充值时间',
		g.first_charge_time,#'首次充值时间',
		o.new_trade_time,#'最近消费时间',
		o.first_trade_time,#'首次消费时间',
		h.month_total_money,#'本月消费金额',
		i.last_month_total_money,#'上月消费金额',
		j.month_total_charge,#'本月充值金额',
		k.last_month_total_charge,#'上月充值金额',
		l.total_card_num,#'累计开卡数量',
		m.total_money,#'累计消费金额',
		m.trade_card_num,#'累计消费卡数',
		n.total_charge,#'累计充值金额',
		b.aplowneridname,#'产品线销售人员',
		b.saleEmail,#'产品线销售邮箱',
		b.oil_centername,#'费控系统预算部门名称',
		b.oil_cross_sale,#'油品协销人员',
		b.accountnumber,#'crm客户编码',
		b.sign_aplowneridname 'sign_saler',#签约销售
		b.new_department,#当日归属省区
		b.new_area,#当日归属大区
		b.new_fossilsupporter,#油品支持人员
		b.oil_cross_centername #当日归属事业部
	FROM
		(
		SELECT
			orgcode,
			org_name,
			crm_id,
			createtime 
		FROM
			oil_org 
		WHERE
			LEFT ( orgcode, 6 ) = '201XW3' 
			AND crm_id != '' 
			AND is_del = 0 
			AND is_test = 1 
		) a
		LEFT JOIN oil_crm_org b ON a.crm_id = b.cdc_id
		LEFT JOIN oil_contract p ON p.customer_no = b.accountnumber
		LEFT JOIN (
		#机构当日消费
		SELECT
			sum( a.trade_money ) day_total_money,
			b.orgcode
		FROM
			oil_card_vice_trades a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			a.trade_type IN ( ".TradesType::getCashAndSelfOilTradeTypeArr(true)." ) 
			AND a.oil_com IN ( 20, 21 ) 
			AND date_format( a.createtime, '%Y-%m-%d' ) = '".$actionDateDay."'
			and left(b.orgcode,6) = '201XW3'  
		GROUP BY
			b.orgcode
		) c ON a.orgcode = c.orgcode
		LEFT JOIN (
		#当日充值
		SELECT
			sum( a.arrival_money ) day_total_charge,
			b.orgcode
		FROM
			oil_account_money_charge a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			date_format( a.createtime, '%Y-%m-%d' ) = '".$actionDateDay."'
			AND a.`status` = 1 
			AND a.charge_type != 2 
			and left(b.orgcode,6) = '201XW3'
		GROUP BY
			b.orgcode
		) d ON a.orgcode = d.orgcode
		LEFT JOIN (
		#当日消费卡数
		SELECT
			count( DISTINCT a.vice_no ) day_card_num,
			b.orgcode
		FROM
			oil_card_vice_trades a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			a.trade_type IN ( ".TradesType::getCashAndSelfOilTradeTypeArr(true)." ) 
			AND a.oil_com IN ( 20, 21 ) 
			AND date_format( a.createtime, '%Y-%m-%d' ) = '".$actionDateDay."'
			and left(b.orgcode,6) = '201XW3'  
		GROUP BY
			b.orgcode
		) e ON a.orgcode = e.orgcode
		LEFT JOIN (
		#首次消费时间最近消费时间
		SELECT
			max( a.createtime ) new_trade_time,
			min( a.createtime ) first_trade_time,
			b.orgcode
		FROM
			oil_card_vice_trades a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			a.trade_type IN ( ".TradesType::getCashAndSelfOilTradeTypeArr(true).") 
			AND a.oil_com IN ( 20, 21 ) 
			and left(b.orgcode,6) = '201XW3' 
		GROUP BY
			b.orgcode
		) o ON a.orgcode = o.orgcode
		LEFT JOIN (
		#客户首次开卡时间
		SELECT
			min( a.createtime ) first_open_time,
			b.orgcode
		FROM
			oil_card_vice a
			LEFT JOIN oil_org b ON a.org_id = b.id 
			and left(b.orgcode,6) = '201XW3' 
		GROUP BY
			 b.orgcode
		) f ON a.orgcode = f.orgcode
		LEFT JOIN (
		#客户首次充值时间和最新充值时间
		SELECT
			max( a.createtime ) new_charge_time,
			min( a.createtime ) first_charge_time,
			b.orgcode
		FROM
			oil_account_money_charge a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			a.STATUS = 1 
			AND a.charge_type != 2 
			and left(b.orgcode,6) = '201XW3' 
		GROUP BY
			 b.orgcode
		) g ON a.orgcode = g.orgcode
		LEFT JOIN (
		#客户本月总消费
		SELECT
			sum( a.trade_money ) month_total_money,
			b.orgcode
		FROM
			oil_card_vice_trades a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			a.trade_type IN ( ".TradesType::getCashAndSelfOilTradeTypeArr(true).") 
			AND a.oil_com IN ( 20, 21 ) 
			AND date_format( a.createtime, '%Y-%m' ) = '".$actionDateMonth."'
			and left(b.orgcode,6) = '201XW3'  
		GROUP BY
			b.orgcode
		) h ON a.orgcode = h.orgcode
		LEFT JOIN (
		#客户上月总消费
		SELECT
			sum( a.trade_money ) last_month_total_money,
			b.orgcode
		FROM
			oil_card_vice_trades a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			a.trade_type IN ( ".TradesType::getCashAndSelfOilTradeTypeArr(true).") 
			AND a.oil_com IN ( 20, 21 ) 
			AND date_format( a.createtime, '%Y-%m' ) = '".$actionDateLastMonth."'
			and left(b.orgcode,6) = '201XW3'  
		GROUP BY
			b.orgcode
		) i ON a.orgcode = i.orgcode
		LEFT JOIN (
		#客户本月充值
		SELECT
			sum( a.arrival_money ) month_total_charge,
			b.orgcode
		FROM
			oil_account_money_charge a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			date_format( a.createtime, '%Y-%m' ) = '".$actionDateMonth."'
			AND a.`status` = 1 
			AND a.charge_type != 2 
			and left(b.orgcode,6) = '201XW3' 
		GROUP BY
			b.orgcode
		) j ON a.orgcode = j.orgcode
		LEFT JOIN (
		#客户上月充值
		SELECT
			sum( a.arrival_money ) last_month_total_charge,
			b.orgcode
		FROM
			oil_account_money_charge a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			date_format( a.createtime, '%Y-%m' ) = '".$actionDateLastMonth."'
			AND a.`status` = 1 
			AND a.charge_type != 2 
			and left(b.orgcode,6) = '201XW3' 
		GROUP BY
			b.orgcode
		) k ON a.orgcode = k.orgcode
		LEFT JOIN (
		#累计开卡数量
		SELECT
			count( DISTINCT a.vice_no ) total_card_num,
			b.orgcode 
		FROM
			oil_card_vice a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			a.oil_com IN ( 20, 21 ) 
			and left(b.orgcode,6) = '201XW3' 
		GROUP BY
			b.orgcode
		) l ON a.orgcode = l.orgcode
		LEFT JOIN (
		#客户累计消费金额和累计消费卡数
		SELECT
			sum( a.trade_money ) total_money,
			count( DISTINCT vice_no ) trade_card_num,
			b.orgcode
		FROM
			oil_card_vice_trades a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			a.trade_type IN ( ".TradesType::getCashAndSelfOilTradeTypeArr(true).") 
			AND a.oil_com IN ( 20, 21 ) 
			and left(b.orgcode,6) = '201XW3' 
		GROUP BY
			b.orgcode
		) m ON a.orgcode = m.orgcode
		LEFT JOIN (
		#客户累计充值
		SELECT
			sum( a.arrival_money ) total_charge,
			b.orgcode
		FROM
			oil_account_money_charge a
			LEFT JOIN oil_org b ON a.org_id = b.id 
		WHERE
			a.`status` = 1 
			AND a.charge_type != 2 
			and left(b.orgcode,6) = '201XW3' 
		GROUP BY
			b.orgcode
		) n ON a.orgcode = n.orgcode 
	) a 
WHERE
	( day_total_money > 0 OR day_total_charge > 0 OR date_format( open_org_time, '%Y-%m-%d' ) = '".$actionDateDay."')";

        $data2 = Capsule::connection('online_only_read')->select($sql2);

        if($data2){
            foreach ($data2 as $val){
                $val = (array)$val;
                try{
                    self::add($val);
                }catch (\Exception $e)
                {

                }
            }
        }

        echo "无车机构日报统计完毕".count($data2)."|所有都已执行完毕\t";

    }


}