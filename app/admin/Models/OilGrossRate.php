<?php
/**
 * oil_gross_rate
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/03/11
 * Time: 11:46:53
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilGrossRate extends \Framework\Database\Model
{
    protected $table = 'oil_gross_rate';

    protected $guarded = ["id"];
    protected $fillable = ['type','is_del','rate_fee','remark','createtime','endtime','updatetime','creator_id','last_operator'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By type
        if (isset($params['type']) && $params['type'] != '') {
            $query->where('type', '=', $params['type']);
        }

        //Search By rate_fee
        if (isset($params['rate_fee']) && $params['rate_fee'] != '') {
            $query->where('rate_fee', '=', $params['rate_fee']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By create_timeGe
        if (isset($params['create_timeGe']) && $params['create_timeGe'] != '') {
            $query->where('createtime', '>=', $params['create_timeGe']);
        }

        //Search By create_timeLe
        if (isset($params['create_timeLe']) && $params['create_timeLe'] != '') {
            $query->where('createtime', '<=', $params['create_timeLe']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By endtime
        if (isset($params['endtime']) && $params['endtime'] != '') {
            $query->where('endtime', '=', $params['endtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By is_del
        if (isset($params['is_del']) && $params['is_del'] != '') {
            $query->where('is_del', '=', $params['is_del']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        return $query;
    }

    /**
     * oil_gross_rate 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilGrossRate::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('createtime', 'desc')->get();
        }elseif(isset($params['take']) && isset($params['skip'])){
            $data = $sqlObj->skip(intval($params['skip']))->take(intval($params['take']))->orderBy('createtime', 'desc')->get();
        }elseif(isset($params['count']) && isset($params['count'])){
            return $sqlObj->count();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        foreach ($data as &$val){
            $val->type_txt = $val->type == 2 ? '电子油卡' : '传统油卡';
            $val->rate_txt = $val->rate_fee."%";
        }

        return $data;
    }

    /**
     * oil_gross_rate 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilGrossRate::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilGrossRate::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByTypeLock(array $params)
    {
        \helper::argumentCheck(['type'],$params);

        return OilGrossRate::lockForUpdate()->where('type',$params['type'])->where('is_del',2)->orderBy("createtime","desc")->first();
    }

    /**
     * oil_gross_rate 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilGrossRate::create($params);
    }

    /**
     * oil_gross_rate 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilGrossRate::find($params['id'])->update($params);
    }

    /**
     * oil_gross_rate 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilGrossRate::destroy($params['ids']);
    }




}