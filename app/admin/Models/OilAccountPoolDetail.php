<?php
/**
 * 充返资金池
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2022/06/28
 * Time: 10:56:45
 */
namespace Models;
use Fuel\Defines\CardViceConf;
use Fuel\Defines\CooperationStatus;
use Fuel\Defines\CooperationType;
use Fuel\Defines\StationArea;
use Fuel\Defines\SupplierConf;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilAccountPoolDetail extends \Framework\Database\Model
{
    protected $table = 'oil_account_pool_detail';

    protected $guarded = ["id"];
    protected $fillable = ['account_id','trade_id','actual_fee','mark_fee','fanli_fee','before_balance', 'fanli_balance',
        'fixed_status','createtime', 'updatetime',"is_del"];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        if (isset($params['trade_id']) && $params['trade_id'] != '') {
            $query->where('trade_id', '=', $params['trade_id']);
        }

        if (isset($params['fixed_status']) && $params['fixed_status'] != '') {
            $query->where('fixed_status', '=', $params['fixed_status']);
        }

        if ( isset($params['fanli_fee']) ) {
            $query->where('fanli_fee', '>=', $params['fanli_fee']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 欠票统计数据供应商视角数据快照表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        //Capsule::connection()->enableQueryLog();
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilAccountPoolDetail::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('id', 'asc')->get();
        }else{
            $data = $sqlObj->orderBy('id', 'asc')->paginate($params['limit'],['*'],'page',$params['page']);
        }
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;

       return $data;
    }

    /**
     * 欠票统计数据供应商视角数据快照表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountPoolDetail::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountPoolDetail::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 欠票统计数据供应商视角数据快照表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilAccountPoolDetail::create($params);
    }

    /**
     * 欠票统计数据供应商视角数据快照表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountPoolDetail::find($params['id'])->update($params);
    }

    /**
     * 欠票统计数据供应商视角数据快照表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilAccountPoolDetail::destroy($params['ids']);
    }

    static public function batchAdd($data = []){
        return OilAccountPoolDetail::insert($data);
    }



}