<?php
/**
 * 发票库存
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/03/27
 * Time: 15:42:19
 */
namespace Models;
use Framework\Helper;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilInvoiceStock extends \Framework\Database\Model
{
    protected $table = 'oil_invoice_stock';

    public $incrementing = FALSE;

    protected $guarded = ['id'];
    protected $fillable = ['id','account_no','third_id','quantity','into_quantity','out_quantity','remark','creator',
        'status','last_operator','createtime','updatetime','init_quantity','init_time','operator_id'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        if (isset($params['operator_id']) && $params['operator_id'] != '') {
            $query->where('operator_id', '=', $params['operator_id']);
        }

        if (isset($params['thirdIds']) && count($params['thirdIds']) > 0) {
            $query->whereIn('third_id', $params['thirdIds']);
        }

        if (isset($params['account_no']) && $params['account_no'] != '') {
            $query->where('account_no', '=', $params['account_no']);
        }

        if (isset($params['third_id']) && $params['third_id'] != '') {
            $query->where('third_id', '=', $params['third_id']);
        }

        //Search By quantity
        if (isset($params['quantity']) && $params['quantity'] != '') {
            $query->where('quantity', '=', $params['quantity']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 发票库存 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        if(isset($params['account_nameLk']) && $params['account_nameLk'] != ""){
            $_val = OilTypeBase::pluckByFilter(['nameLk'=>$params['account_nameLk']]);

            if(count($_val) > 0){
                $params['thirdIds'] = $_val->toArray();
            }
            unset($params['account_nameLk']);
        }

        if(isset($params['parent_id']) && $params['parent_id'] != "") {
            $_val=OilTypeCategory::getInfoSecondLevelOilByPid($params['parent_id']);
            if(count($_val) > 0){
                $params['thirdIds'] = $_val;
            }
            unset($params['parent_id']);
        }

        if(isset($params['pid']) && $params['pid'] != ""){
            $params['thirdIds']=OilTypeCategory::getInfoSecondLevelOilByPid($params['pid']);
        }

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 1000;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilInvoiceStock::Filter($params)
            ->select('oil_invoice_stock.*','oil_operators.company_name as operator_name','oil_operators.sub_name as operator_subname','oil_type_base.name as oil_name','oil_configure.sys_value as unit_txt')
            ->leftJoin('oil_operators', 'oil_invoice_stock.operator_id','=','oil_operators.id')
            ->leftJoin('oil_type_base', 'oil_invoice_stock.third_id','=','oil_type_base.id')
            ->leftJoin('oil_configure', 'oil_type_base.unit','=','oil_configure.id');
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('oil_invoice_stock.createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }
        $classifyMap = OilTypeCategory::getTypeName();
        foreach ($data as &$_item){
            $_tmp = isset($classifyMap[$_item->third_id]) ? $classifyMap[$_item->third_id] : [];
            $_item->parent_name = $_tmp['parent_name'];
            $_item->account_name = $_item['operator_subname'].'_'.$_item['oil_name'];
            $_item->remark = empty($_item->remark) ? "" : $_item->remark;
            $_item->quantity = number_format($_item->quantity,6,".","");
            $_item->into_quantity = number_format($_item->into_quantity,6,".","");
            $_item->out_quantity = number_format($_item->out_quantity,6,".","");
            $_item->init_quantity = number_format($_item->init_quantity,6,".","");
            $_item->init_time = empty($_item->init_time) ? '-' :$_item->init_time;
        }
        return $data;
    }

    /**
     * 发票库存 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilInvoiceStock::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilInvoiceStock::lockForUpdate()->where('id',$params['id'])->first();
    }

    static public function getInfoByFilter(array $params,$isLock = false)
    {
        $obj = OilInvoiceStock::Filter($params);
        if($isLock){
            $obj = $obj->lockForUpdate();
        }
        return $obj->first();
    }

    /**
     * 发票库存 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        //$params['id'] = Helper::uuid();

        return OilInvoiceStock::create($params);
    }

    /**
     * 发票库存 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilInvoiceStock::find($params['id'])->update($params);
    }

    /**
     * 发票库存 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilInvoiceStock::destroy($params['ids']);
    }



    static public function getStockAccount(array $params)
    {
        return self::lockForUpdate()->where('commodityType','=', $params['commodityType'])
            ->where('commodityName',$params['commodityName'])
            ->where('purchaserName',$params['purchaserName'])
            ->where('specificationModel', $params['specificationModel'])
            ->first();
    }

    /**
     * @title   按
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Models
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return array
     * @returns
     * []
     * @returns
     */
    static public function getStockStatistic(array $params)
    {
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $condition[] = 'status=10';
        if(isset($params['commodityType']) && $params['commodityType']){
            $condition[] = "commodityType = '".$params['commodityType']."'";
        }
        if(isset($params['card_operators_id']) && $params['card_operators_id']){
            $condition[] = "card_operators_id = '".$params['card_operators_id']."'";
        }
        $conditionString = implode(' and ', $condition);

        $pageString = ' limit '.($params['page']-1)*$params['limit'].','.$params['limit'];

        $sql = "select
	purchaserName,commodityTypeText,sum(amount) as total_amount,sum(quantity) as total_quantity
	from
	oil_invoice_stock
	where $conditionString
	group by card_operators_id,commodityType".$pageString;


        return Capsule::connection()->select($sql);
    }

    /**
     * OilInvoiceStock 批量新增
     * @param array $params
     * @return mixed
     */
    static public function batchAdd(array $params)
    {
        return OilInvoiceStock::insert($params);
    }


    /**
     * 邮票库存页-主表查询
     *  @param array $params
     * @return array
     */
    static public function getOperatorStatisticNode(array $params)
    {

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 1000;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $sqlObj = OilInvoiceStock::select(Capsule::connection()->raw("oil_invoice_stock.id,oil_invoice_stock.operator_id,oil_invoice_stock.updatetime,oil_invoice_stock.last_operator,oil_operators.company_name as name,oil_operators.sub_name,oil_configure.sys_value as unit_txt,oil_type_category.pid,GROUP_CONCAT(third_id) as third_id,GROUP_CONCAT(oil_type_base.name) as relation_oil,sum(quantity) as quantity,sum(into_quantity) as into_quantity,sum(out_quantity) as out_quantity"))
            ->leftJoin('oil_type_category','oil_invoice_stock.third_id','=','oil_type_category.oil_id')
            ->leftJoin('oil_type_base','oil_invoice_stock.third_id','=','oil_type_base.id')
            ->leftJoin('oil_operators','oil_operators.id','=','oil_invoice_stock.operator_id')
            ->leftJoin('oil_configure','oil_type_base.unit','=','oil_configure.id')
            ->where('oil_type_category.template_id','=',1)
            ->where('oil_type_category.statistic_node','=',1)
            ->groupBy('oil_invoice_stock.operator_id')
            ->groupBy('oil_type_category.pid')
            ->orderBy('oil_invoice_stock.updatetime','desc');

        if(isset($params['operator_id']) && !empty($params['operator_id'])){
            $sqlObj->where('oil_invoice_stock.operator_id','=',$params['operator_id']);
        }

        if(isset($params['statistic_id']) && !empty($params['statistic_id'])){
            $sqlObj->where('oil_type_category.pid','=',$params['statistic_id']);
        }

        if (isset($params['_export']) && !empty($params['_export']))
        {
            return  $sqlObj->get();
        }

        return  $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
    }

}