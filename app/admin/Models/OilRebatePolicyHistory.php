<?php
/**
 * oil_rebate_policy_history
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2022/06/17
 * Time: 10:04:20
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilRebatePolicyHistory extends \Framework\Database\Model
{
    protected $table = 'oil_rebate_policy_history';

    protected $guarded = ["id"];
    protected $fillable = ['policy_id','serial_num','content','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By policy_id
        if (isset($params['policy_id']) && $params['policy_id'] != '') {
            if (is_array($params['policy_id'])) {
                $query->whereIn('policy_id', $params['policy_id']);
            } else {
                $query->where('policy_id', '=', $params['policy_id']);
            }
        }

        //Search By serial_num
        if (isset($params['serial_num']) && $params['serial_num'] != '') {
            $query->where('serial_num', '=', $params['serial_num']);
        }

        //Search By content
        if (isset($params['content']) && $params['content'] != '') {
            $query->where('content', '=', $params['content']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * oil_rebate_policy_history 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilRebatePolicyHistory::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_rebate_policy_history 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilRebatePolicyHistory::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilRebatePolicyHistory::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_rebate_policy_history 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilRebatePolicyHistory::create($params);
    }

    /**
     * oil_rebate_policy_history 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilRebatePolicyHistory::find($params['id'])->update($params);
    }

    /**
     * oil_rebate_policy_history 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilRebatePolicyHistory::destroy($params['ids']);
    }

    static public function getMaxSerialNum($policyId)
    {
        $maxNum = self::where('policy_id',$policyId)->max('serial_num');
        return !$maxNum ? 0 : $maxNum;
    }
    
    /**
     * 列表全量查询
     * @param array $params
     * @return array
     */
    static public function getFilterList(array $params)
    {
        return self::Filter($params)->orderBy('createtime', 'desc')->get()->toArray();
    }

}