<?php
/**
 * 主卡现金返利记录表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:18
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCardMainFanliRecords extends \Framework\Database\Model
{
    protected $table = 'oil_card_main_fanli_records';

    protected $guarded = ["id"];

    protected $fillable = ['main_id','money','createdate','starttime','endtime','createtime','updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }


    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By main_id
        if (isset($params['main_id']) && $params['main_id'] != '') {
            $query->where('main_id', '=', $params['main_id']);
        }

        //Search By money
        if (isset($params['money']) && $params['money'] != '') {
            $query->where('money', '=', $params['money']);
        }

        //Search By createdate
        if (isset($params['createdate']) && $params['createdate'] != '') {
            $query->where('createdate', '=', $params['createdate']);
        }

        //Search By starttime
        if (isset($params['starttime']) && $params['starttime'] != '') {
            $query->where('starttime', '=', $params['starttime']);
        }

        //Search By endtime
        if (isset($params['endtime']) && $params['endtime'] != '') {
            $query->where('endtime', '=', $params['endtime']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }


        return $query;
    }

    /**
     * 主卡现金返利记录表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCardMainFanliRecords::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 主卡现金返利记录表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardMainFanliRecords::find($params['id']);
    }

    /**
     * 主卡现金返利记录表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCardMainFanliRecords::create($params);
    }

    /**
     * 主卡现金返利记录表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardMainFanliRecords::find($params['id'])->update($params);
    }

    /**
     * 主卡现金返利记录表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCardMainFanliRecords::destroy($params['ids']);
    }




}