<?php
/**
 * oil_sync_credit_data
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/01/31
 * Time: 14:39:35
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilSyncCreditData extends \Framework\Database\Model
{
    protected $table = 'oil_sync_credit_data';

    
    protected $fillable = ['status','updatetime',"createtime","orgcode"];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By no
        if (isset($params['no']) && $params['no']) {
            $query->whereIn('no', explode('|',$params['no']));
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By no_type
        if (isset($params['no_type']) && $params['no_type'] != '') {
            $query->where('no_type', '=', $params['no_type']);
        }

        //Search By orgcode
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $query->where('orgcode', '=', $params['orgcode']);
        }
        return $query;
    }

    /**
     * oil_1st_station 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilSyncCreditData::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('no_time','asc')->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_1st_station 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSyncCreditData::find($params['id']);
    }

    /**
     * oil_1st_station 详情查询
     * @param array $params
     * @return object
     */
    static public function getByNoType(array $params)
    {
        \helper::argumentCheck(['no_type','no'],$params);

        return OilSyncCreditData::where('no_type',$params['no_type'])->where('no',$params['no'])->first();
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSyncCreditData::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_1st_station 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilSyncCreditData::create($params);
    }


    /**
     * oil_1st_station 新增
     * @param array $params
     * @return mixed
     */
    static public function batchAdd(array $params)
    {
        return OilSyncCreditData::insert($params);
    }

    /**
     * oil_1st_station 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSyncCreditData::find($params['id'])->update($params);
    }

    /**
     * oil_1st_station 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilSyncCreditData::destroy($params['ids']);
    }

    static public function getMap(array $params,$key="id",$val='no')
    {
        $sqlObj = OilSyncCreditData::Filter($params);
        return $sqlObj->pluck($val,$key)->toArray();
    }

}