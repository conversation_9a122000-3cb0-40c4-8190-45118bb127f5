<?php
/**
 * oil_account_charge_wechat
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/10/20
 * Time: 11:20:28
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilAccountChargeWechat extends \Framework\Database\Model
{
    protected $table = 'oil_account_charge_wechat';

    protected $guarded = ["id"];
    protected $fillable = ['charge_id','transaction_id','result_code','is_subscribe','trade_type','bank_type','out_trade_no','total_fee','cash_fee','cash_fee_type','settlement_total_fee','fee_type','openid','attach','time_end','original_data','status','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By charge_id
        if (isset($params['charge_id']) && $params['charge_id'] != '') {
            $query->where('charge_id', '=', $params['charge_id']);
        }

        //Search By transaction_id
        if (isset($params['transaction_id']) && $params['transaction_id'] != '') {
            $query->where('transaction_id', '=', $params['transaction_id']);
        }

        //Search By result_code
        if (isset($params['result_code']) && $params['result_code'] != '') {
            $query->where('result_code', '=', $params['result_code']);
        }

        //Search By is_subscribe
        if (isset($params['is_subscribe']) && $params['is_subscribe'] != '') {
            $query->where('is_subscribe', '=', $params['is_subscribe']);
        }

        //Search By trade_type
        if (isset($params['trade_type']) && $params['trade_type'] != '') {
            $query->where('trade_type', '=', $params['trade_type']);
        }

        //Search By bank_type
        if (isset($params['bank_type']) && $params['bank_type'] != '') {
            $query->where('bank_type', '=', $params['bank_type']);
        }

        //Search By out_trade_no
        if (isset($params['out_trade_no']) && $params['out_trade_no'] != '') {
            $query->where('out_trade_no', '=', $params['out_trade_no']);
        }

        //Search By total_fee
        if (isset($params['total_fee']) && $params['total_fee'] != '') {
            $query->where('total_fee', '=', $params['total_fee']);
        }

        //Search By cash_fee
        if (isset($params['cash_fee']) && $params['cash_fee'] != '') {
            $query->where('cash_fee', '=', $params['cash_fee']);
        }

        //Search By cash_fee_type
        if (isset($params['cash_fee_type']) && $params['cash_fee_type'] != '') {
            $query->where('cash_fee_type', '=', $params['cash_fee_type']);
        }

        //Search By settlement_total_fee
        if (isset($params['settlement_total_fee']) && $params['settlement_total_fee'] != '') {
            $query->where('settlement_total_fee', '=', $params['settlement_total_fee']);
        }

        //Search By fee_type
        if (isset($params['fee_type']) && $params['fee_type'] != '') {
            $query->where('fee_type', '=', $params['fee_type']);
        }

        //Search By openid
        if (isset($params['openid']) && $params['openid'] != '') {
            $query->where('openid', '=', $params['openid']);
        }

        //Search By attach
        if (isset($params['attach']) && $params['attach'] != '') {
            $query->where('attach', '=', $params['attach']);
        }

        //Search By time_end
        if (isset($params['time_end']) && $params['time_end'] != '') {
            $query->where('time_end', '=', $params['time_end']);
        }

        //Search By original_data
        if (isset($params['original_data']) && $params['original_data'] != '') {
            $query->where('original_data', '=', $params['original_data']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * oil_account_charge_wechat 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilAccountChargeWechat::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_account_charge_wechat 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountChargeWechat::find($params['id']);
    }

    /**
     * oil_account_charge_wechat 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilAccountChargeWechat::create($params);
    }

    /**
     * oil_account_charge_wechat 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountChargeWechat::find($params['id'])->update($params);
    }

    /**
     * oil_account_charge_wechat 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilAccountChargeWechat::destroy($params['ids']);
    }

    /**
     * @param $transaction_id
     * @return mixed
     */
    static public function getByTransactionId($transaction_id)
    {
        return OilAccountChargeWechat::where('transaction_id','=',$transaction_id)->first();
    }


}