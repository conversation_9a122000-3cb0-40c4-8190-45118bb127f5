<?php
/**
 * 数据操作日志
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/10/23
 * Time: 15:29:16
 */
namespace Models;
use Framework\Helper;
use Framework\Log;
use Fuel\Service\AccountCenter\AccountService;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCardViceTradesZBank extends \Framework\Database\Model
{
    protected $table = 'oil_card_vice_trades_zbank';

    protected $guarded = ["id"];

    protected $fillable = ['stream_no','client_order_id','order_type','api_id','trade_address', 'vice_no', 'trade_time',
        'oil_name', 'trade_money','trade_place','oil_com','org_id', 'org_name', 'truck_no','trade_price', 'createtime',
        'updatetime', 'trade_api_id','is_pay','billID','extID','money_type','balance',
        'deduction_account_no','deduction_account_name','service_money','station_id','trade_num',"truename",'is_check',
        'check_faild_reason','data_from','use_fanli_money','driver_name','driver_tel','original_id'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        //参数处理
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            if (preg_match('/(.*)\s/', $params['orgcode'])) {
                preg_match('/(.*)\s/', $params['orgcode'], $arr);
                $params['orgcode'] = $arr[1];
            }
        }
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }
        
        //Search By vice_no
        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $viceNoArr = explode('|', $params['vice_no']);
            if (count($viceNoArr) == 1) {
                $query->where('oil_card_vice_trades_zbank.vice_no', '=', $params['vice_no']);
            } else {
                $query->whereIn('oil_card_vice_trades_zbank.vice_no', $viceNoArr);
            }
        }

        //Search By vice_noIn
        if (isset($params['vice_noIn']) && $params['vice_noIn'] != '') {
            $query->whereIn('vice_no', $params['vice_noIn']);
        }

        //Search By trade_time
        if (isset($params['timeGe']) && $params['timeGe'] != '') {
            $query->where('oil_card_vice_trades_zbank.trade_time', '>', $params['timeGe']);
        }

        //Search By trade_time
        if (isset($params['trade_time']) && $params['trade_time'] != '') {
            $query->where('oil_card_vice_trades_zbank.trade_time', '=', $params['trade_time']);
        }

        //Search By tradetimeGe
        if (isset($params['tradetimeGe']) && $params['tradetimeGe'] != '') {
            $query->where('oil_card_vice_trades_zbank.trade_time', '>=', $params['tradetimeGe'] . ' 00:00:00');
        }

        //Search By tradetimeLe
        if (isset($params['tradetimeLe']) && $params['tradetimeLe'] != '') {
            $query->where('oil_card_vice_trades_zbank.trade_time', '<=', $params['tradetimeLe'] . ' 23:59:59');
        }

        if (isset($params['tradetimeLne']) && $params['tradetimeLne'] != '') {
            $query->where('oil_card_vice_trades_zbank.trade_time', '<', $params['tradetimeLne']);
        }

        //Search By is_pay
        if (isset($params['is_payNeq']) && $params['is_payNeq'] != '') {
            $query->where('oil_card_vice_trades_zbank.is_pay', '!=',$params['is_payNeq']);
        }

        //Search By money_type
        if (isset($params['money_type']) && $params['money_type'] != '') {
            $query->where('oil_card_vice_trades_zbank.money_type', '=',$params['money_type']);
        }

        //Search By is_pay
        if (isset($params['is_pay']) && $params['is_pay'] != '') {
            $query->where('oil_card_vice_trades_zbank.is_pay', '=',$params['is_pay']);
        }

        //Search By extID
        if (isset($params['extID']) && $params['extID'] != '') {
            $query->where('oil_card_vice_trades_zbank.extID', '=',$params['extID']);
        }

        //Search By deduction_account_noNeq
        if (isset($params['deduction_account_noNeq']) && $params['deduction_account_noNeq'] != '') {
            $query->whereNotNull('oil_card_vice_trades_zbank.deduction_account_no');
        }

        //Search By deduction_account_nameNotIn
        if (isset($params['deduction_account_nameNotIn']) && $params['deduction_account_nameNotIn'] != '') {
            $query->whereNotIn('oil_card_vice_trades_zbank.deduction_account_name', $params['deduction_account_nameNotIn']);
        }

        //Search By trade_api_id
        if (isset($params['trade_api_id']) && $params['trade_api_id'] != '') {
            $query->where('oil_card_vice_trades_zbank.trade_api_id','=', $params['trade_api_id']);
        }

        //Search By trade_typeList
        if (isset($params['tradeTypeList']) && $params['tradeTypeList'] != '') {
            $query->whereIn('oil_card_vice_trades_zbank.trade_type', $params['tradeTypeList']);
        }

        //Search By oil_name
        if (isset($params['oil_name']) && $params['oil_name'] != '') {
            $query->where('oil_card_vice_trades_zbank.oil_name', '=', $params['oil_name']);
        }

        //Search By trade_money
        if (isset($params['trade_money']) && $params['trade_money'] != '') {
            $query->where('oil_card_vice_trades_zbank.trade_money', '=', $params['trade_money']);
        }

        //Search By use_fanli_money
        if (isset($params['use_fanli_money']) && $params['use_fanli_money'] != '') {
            $query->where('use_fanli_money', '=', $params['use_fanli_money']);
        }

        //Search By trade_price
        if (isset($params['trade_price']) && $params['trade_price'] != '') {
            $query->where('trade_price', '=', $params['trade_price']);
        }

        //Search By trade_num
        if (isset($params['trade_num']) && $params['trade_num'] != '') {
            $query->where('trade_num', '=', $params['trade_num']);
        }


        //Search By oil_com
        if (isset($params['oil_com']) && $params['oil_com'] != '') {
            $query->where('oil_card_vice_trades_zbank.oil_com', '=', $params['oil_com']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('oil_card_vice_trades_zbank.org_id', '=', $params['org_id']);
        }

        //Search By org_name
        if (isset($params['org_name']) && $params['org_name'] != '') {
            $query->where('oil_card_vice_trades_zbank.org_name', '=', $params['org_name']);
        }


        //Search By truck_no
        if (isset($params['truck_no']) && $params['truck_no'] != '') {
            $query->where('truck_no', '=', $params['truck_no']);
        }


        //Search By qz_drivername
        if (isset($params['driver_name']) && $params['driver_name'] != '') {
            $query->where('driver_name', '=', $params['driver_name']);
        }

        //Search By qz_drivertel
        if (isset($params['driver_tel']) && $params['driver_tel'] != '') {
            $query->where('driver_tel', '=', $params['driver_tel']);
        }

        //Search By createtime
        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('oil_card_vice_trades_zbank.createtime', '>=', $params['createtimeGe']);
        }

        //Search By createtime
        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $query->where('oil_card_vice_trades_zbank.createtime', '<=', $params['createtimeLe']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('oil_card_vice_trades_zbank.updatetime', '=', $params['updatetime']);
        }

        //Search By updatetimeGt
        if (isset($params['updatetimeGt']) && $params['updatetimeGt'] != '') {
            $query->where('oil_card_vice_trades_zbank.updatetime', '>', $params['updatetimeGt']);
        }

        //Search By updatetimeLe
        if (isset($params['updatetimeLe']) && $params['updatetimeLe'] != '') {
            $query->where('oil_card_vice_trades_zbank.updatetime', '<=', $params['updatetimeLe']);
        }

        if (isset($params['org_id_list']) && is_array($params['org_id_list'])) {
            $query->whereIn('org_id', $params['org_id_list']);
        }

        //Search By vice_no
        if (isset($params['oil_comIn']) && $params['oil_comIn'] != '') {
            $query->whereIn('oil_card_vice_trades_zbank.oil_com', $params['oil_comIn']);
        }

        //Search By card_from
        if (isset($params['card_from']) && $params['card_from'] != '') {
            $query->where('oil_card_vice_trades_zbank.data_from', '=', $params['card_from']);
        }

        //Search By card_fromIn
        if (isset($params['card_fromIn']) && $params['card_fromIn'] != '') {
            $query->whereIn('oil_card_vice_trades_zbank.data_from', $params['card_fromIn']);
        }

        if (isset($params['card_fromNin']) && $params['card_fromNin'] != '') {
            $query->whereNotIn('oil_card_vice_trades_zbank.data_from', $params['card_fromNin']);
        }

        //Search By oil_type
        if (isset($params['oil_type']) && $params['oil_type'] != '') {
            $query->where('oil_type_no.oil_type', $params['oil_type']);
        }

        //Search By order_type
        if (isset($params['order_type']) && $params['order_type'] != '') {
            $query->where('oil_card_vice_trades_zbank.order_type', $params['order_type']);
        }

        return $query;
    }

    /**
     * 操作日志 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = self::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('createtime', 'desc')->get();
        }elseif(isset($params['_isAll']) && $params['_isAll'] == 1){
            $data = $sqlObj->orderBy('createtime', 'desc')->get();
        }elseif(isset($params['_count']) && $params['_count'] == 1){
            return $sqlObj->orderBy('createtime', 'desc')->count();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        foreach ($data as &$val){
            $val->operator_time = substr($val->createtime,0,16);
            $val->remark = $val->remark == NULL ? "无" : $val->remark;
        }

        return $data;
    }

    /**
     * 操作日志 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return self::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return self::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByApiId(array $params)
    {
        \helper::argumentCheck(['api_id'],$params);

        return self::lockForUpdate()->where('api_id',$params['api_id'])->first();
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByTradeApiId(array $params)
    {
        \helper::argumentCheck(['trade_id','money_type'],$params);

        return self::lockForUpdate()->where('trade_api_id',$params['trade_id'])->where("money_type",$params['money_type'])->first();
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByExtId(array $params)
    {
        \helper::argumentCheck(['extID'],$params);

        return self::where('extID',$params['extID'])->first();
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getFilterByExtId(array $params)
    {
        return self::Filter($params)->first();
    }

    /**
     * 操作日志 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return self::create($params);
    }

    /**
     * @title  1号卡获取累计加油量
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Models
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getSumTradeNum(array $params)
    {
        Capsule::connection()->enableQueryLog();
        $data = self::Filter($params)
            ->sum("trade_num");
        $sql = Capsule::connection()->getQueryLog();
        Log::error("SumTotalSql".json_encode($sql),[],"getSumTradeMoney_");
        return $data;
    }

    /*
     * 异步新方案
     */
    static public function formatData2($params,$orgInfo,$cardViceInfo,$trade_money,$service_money = 0, $zbankOrder = NULL)
    {
        if(!$zbankOrder){
            try{
                $res = [];

                $res['stream_no'] = $params['stream_no'];
                $res['client_order_id'] = $params['client_order_id'];
                $res['order_type'] = $params['order_type'];
                $res['api_id'] = $params['id'];
                $res['trade_api_id'] = $params['id'];
                $res['org_id'] = $orgInfo->id;
                $res['org_name'] = $orgInfo->org_name;
                $res['oil_com'] = $cardViceInfo->oil_com;
                $res['vice_no'] = $cardViceInfo->vice_no;
                $res['trade_time'] = $params['oil_time'] ? $params['oil_time'] : \helper::nowTime();
                $res['oil_name'] = $params['oil_name'] ? $params['oil_name'] : '';
                $res['trade_money'] = $trade_money;
                $res['trade_price'] = $params['price'];
                $res['station_id'] = $params['station_id'];
                $res['trade_num'] = $params['oil_num'];
                $res['trade_place'] = $params['trade_place'] ? $params['trade_place'] : '';
                $res['trade_address'] = $params['trade_address'] ? $params['trade_address'] : '';
                $res['truck_no'] = $params['truck_no'] ? $params['truck_no'] : '';
                $res['truename'] = $params['truename'] ? $params['truename'] : '';
                $res['createtime'] = \helper::nowTime();
                $res['updatetime'] = \helper::nowTime();
                $res['deduction_account_no'] = $cardViceInfo->deduction_account_no;
                $res['deduction_account_name'] = $cardViceInfo->deduction_account_name;
                $res['service_money'] = $service_money;
                $res['data_from'] = $params['trade_from'] ? $params['trade_from'] : null;

                $isExist = self::getByTradeApiId(['trade_id'=>$params['id'],'money_type'=>1]);
                if (!$isExist || !$isExist->id) {
                    $insertArr = $res;
                } else {
                    //return $isExist;
                    throw new \RuntimeException("该消费已存在:".$params['id'], 2);
                }

                $insertArr['is_pay'] = 2;
                $insertArr['extID'] = Helper::uuid();

                Log::error('扣卡扣款日志2', [$insertArr], 'gasDeductMoney');

                //新增
                if ($insertArr) {
                    $result = self::add($insertArr);
                }
            }catch (\Exception $e) {
                Log::error("消费记录入库bug".var_export($params,true),[],"tradesZbank_");
                throw new \RuntimeException($e->getMessage(), $e->getCode());
            }
        }else{
            //二次继续付费
            $result = $zbankOrder;
        }

        return $result;
    }

    static public function formatData($params,$orgInfo,$cardViceInfo,$trade_money,$service_money = 0)
    {
        try{
            $res = [];

            $res['api_id'] = $params->trade_id;
            $res['org_id'] = $orgInfo->id;
            $res['org_name'] = $orgInfo->org_name;
            $res['oil_com'] = $cardViceInfo->oil_com;
            $res['vice_no'] = $cardViceInfo->vice_no;
            $res['trade_time'] = $params->trade_time;
            $res['oil_name'] = $params->oil_name;
            $res['trade_money'] = $trade_money;
            $res['station_id'] = $params['station_id'];
            $res['trade_num'] = $params['trade_num'];
            $res['trade_place'] = $params->station_name;
            $res['trade_address'] = $params->trade_address;
            $res['truck_no'] = $params->truck_no;
            $res['createtime'] = \helper::nowTime();
            $res['updatetime'] = \helper::nowTime();
            $res['deduction_account_no'] = $cardViceInfo->deduction_account_no;
            $res['deduction_account_name'] = $cardViceInfo->deduction_account_name;
            $res['service_money'] = $service_money;

            $isExist = self::getByApiId(['api_id'=>$params->trade_id]);
            if (!$isExist || !$isExist->id) {
                $insertArr = $res;
            } else {
                //return $isExist;
                throw new \RuntimeException("该消费已存在:".$params->trade_id, 2);
            }

            $insertArr['is_pay'] = 2;
            $insertArr['extID'] = $params->extID;

            Log::error('扣卡扣款日志2', [$insertArr], 'gasDeductMoney');

            //新增
            if ($insertArr) {
                $result = self::add($insertArr);
            }
        }catch (\Exception $e) {
            Log::error("消费记录入库bug".var_export($params,true),[],"tradesZbank_");
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
        return $result;
    }

    /**
     * 操作日志 批量新增
     * @param array $params
     * @return mixed
     */
    static public function batchAdd(array $params)
    {
        return self::insert($params);
    }


    /**
     * 操作日志 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return self::find($params['id'])->update($params);
    }

    /**
     * 操作日志 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return self::destroy($params['ids']);
    }

    static public function getByStreamNo($stream_no,$islock = FALSE)
    {
        if($islock){
            $res = OilCardViceTradesZBank::lockForUpdate()->where('stream_no',$stream_no)->first();
        }else{
            $res = OilCardViceTradesZBank::where('stream_no',$stream_no)->first();
        }
        return $res;
    }
    
    /**
     * 取字段值
     * @param array $params
     * @param $pluckField
     * @return array
     */
    static public function getPluckFields(array $params, $pluckField)
    {
        return self::Filter($params)->pluck($pluckField)->toArray();
    }
    
    /**
     * 取字段值 返回第一个值
     * @param array $params
     * @param $pluckField
     * @return mixed
     */
    static public function getResField(array $params, $pluckField)
    {
        $res = self::Filter($params)->pluck($pluckField)->toArray();
        
        return !empty($res[0]) ? $res[0] : '';
    }

    public static function countData($params)
    {
        return self::Filter($params)->count();
    }

    public static function getData(array $params, array $fields = ['*'])
    {
        return self::Filter($params)->get($fields)->toArray();
    }
}