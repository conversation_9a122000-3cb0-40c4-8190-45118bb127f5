<?php
/**
 * 返利测算监控
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2022/09/05
 * Time: 20:34:56
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilRebateMonitor extends \Framework\Database\Model
{
    protected $table = 'oil_rebate_monitor';

    protected $guarded = ["id"];
    protected $fillable = ['suit_obj','type','month','flag','should','dispatch','calculated','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By suit_obj
        if (isset($params['suit_obj']) && $params['suit_obj'] != '') {
            $query->where('suit_obj', '=', $params['suit_obj']);
        }

        //Search By type
        if (isset($params['type']) && $params['type'] != '') {
            $query->where('type', '=', $params['type']);
        }

        //Search By month
        if (isset($params['month']) && $params['month'] != '') {
            $query->where('month', '=', $params['month']);
        }

        //Search By flag
        if (isset($params['flag']) && $params['flag'] != '') {
            $query->where('flag', '=', $params['flag']);
        }

        //Search By should
        if (isset($params['should']) && $params['should'] != '') {
            $query->where('should', '=', $params['should']);
        }

        //Search By dispatch
        if (isset($params['dispatch']) && $params['dispatch'] != '') {
            $query->where('dispatch', '=', $params['dispatch']);
        }

        //Search By calculated
        if (isset($params['calculated']) && $params['calculated'] != '') {
            $query->where('calculated', '=', $params['calculated']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 返利测算监控 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilRebateMonitor::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 返利测算监控 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilRebateMonitor::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilRebateMonitor::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 返利测算监控 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilRebateMonitor::create($params);
    }

    /**
     * 返利测算监控 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilRebateMonitor::find($params['id'])->update($params);
    }

    /**
     * 返利测算监控 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilRebateMonitor::destroy($params['ids']);
    }




}