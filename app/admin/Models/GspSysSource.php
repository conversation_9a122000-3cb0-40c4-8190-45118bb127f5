<?php
/**
 * 资源表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:17
 */
namespace Models;
use Framework\Cache;
use Illuminate\Database\Capsule\Manager as Capsule;

class GspSysSource extends \Framework\Database\Model
{
    protected $table = 'gsp_sys_source';

    protected $guarded = ["id"];

    protected $fillable = ['source_code','source_name','url','method','type','is_display','sort','creator_id','last_operator','createtime','updatetime','status','is_del'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By source_code
        if (isset($params['source_code']) && $params['source_code'] != '') {
            $query->where('source_code', '=', $params['source_code']);
        }

        //Search By source_name
        if (isset($params['source_name']) && $params['source_name'] != '') {
            $query->where('source_name', '=', $params['source_name']);
        }

        //Search By url
        if (isset($params['url']) && $params['url'] != '') {
            $query->where('url', '=', $params['url']);
        }

        //Search By type
        if (isset($params['type']) && $params['type'] != '') {
            $query->where('type', '=', $params['type']);
        }

        //Search By is_display
        if (isset($params['is_display']) && $params['is_display'] != '') {
            $query->where('is_display', '=', $params['is_display']);
        }

        //Search By sort
        if (isset($params['sort']) && $params['sort'] != '') {
            $query->where('sort', '=', $params['sort']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By is_del
        if (isset($params['is_del']) && $params['is_del'] != '') {
            $query->where('is_del', '=', $params['is_del']);
        }


        return $query;
    }

    /**
     * 资源表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = GspSysSource::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 资源表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspSysSource::find($params['id']);
    }

    /**
     * 资源表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return GspSysSource::create($params);
    }

    /**
     * 资源表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspSysSource::find($params['id'])->update($params);
    }

    /**
     * 资源表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return GspSysSource::destroy($params['ids']);
    }

    /**
     * 获取指定source ids的method
     * @param $sourceIds
     * @return mixed
     */
    static public function getMethodMapByIds($sourceIds)
    {
        $sources = [];
        $record = GspSysSource::whereIn('id',$sourceIds)->whereNotNull('method')->pluck('method');
        if($record){
            foreach($record as $v){
                $_sources = explode(',',$v);
                $sources = array_merge($sources,\helper::arrayToLower($_sources));
            }
        }

        return array_unique($sources);
    }


    /**
     * 根据roleIds&url取指定资源
     * @param array $roleIds
     * @param $url
     * @return array
     */
    static public function getSourcesByRoleIdsAndUrl(array $roleIds, $url)
    {
        if(!$url){
            return [];
        }

        global $app;

        $cacheName = __CLASS__.'getSourcesByRoleIdsAndUrl_'.var_export($roleIds,true).$url;
        $data = Cache::get($cacheName);
        if(!$data || 1){
            $sqlObj = GspSysSource::select('gsp_sys_source.id','gsp_sys_source.source_code')
                ->leftJoin('gsp_sys_role_source','gsp_sys_role_source.source_id','=',
                    'gsp_sys_source.id')
                ->where('gsp_sys_source.url','/'.$url)
                ->where('gsp_sys_source.status', '=', 1)
                ->where('gsp_sys_source.is_del', '=', 0);

            if($app->myAdmin->id != 1){
                if(!$roleIds){
                    return [];
                }
                $sqlObj->whereIn('gsp_sys_role_source.role_id',$roleIds);
            }
            $data = $sqlObj->first();
            Cache::put($cacheName, $data, 3600);
        }

        return $data;

    }

    /**
     * 根据roleIds&$sourceCodel取指定资源
     * @param array $roleIds
     * @param $sourceCode
     * @return array
     */
    static public function getSourcesByRoleIdsAndLikeSourceCode(array $roleIds, $sourceCode)
    {
        if(!$sourceCode){
            return [];
        }

        global $app;

        $cacheName = __CLASS__.'getSourcesByRoleIdsAndLikeSourceCode'.var_export($roleIds,true).$sourceCode;
        $data = Cache::get($cacheName);
        if(!$data){
            Capsule::connection()->enableQueryLog();
            $sqlObj = GspSysSource::select('gsp_sys_source.id','gsp_sys_source.source_name','gsp_sys_source.url')
                ->leftJoin('gsp_sys_role_source','gsp_sys_role_source.source_id','=',
                    'gsp_sys_source.id')
                ->where('gsp_sys_source.source_code','like',$sourceCode.'%')
                ->where('gsp_sys_source.status','=',1)
                ->where('gsp_sys_source.is_del','=',0)
                ->where('gsp_sys_source.source_code','<>',$sourceCode);

            if($app->myAdmin->id != 1){
                if(!$roleIds){
                    return [];
                }
                $sqlObj->whereIn('gsp_sys_role_source.role_id',$roleIds);
            }
            $data = $sqlObj->groupBy('gsp_sys_source.id')->orderBy('gsp_sys_source.sort')->get();
            Cache::put($cacheName, $data, 60);
        }

        return $data;
    }


}