<?php
/**
 * 客户问题反馈
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/06/05
 * Time: 19:21:30
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCustomerIssue extends \Framework\Database\Model
{
    protected $table = 'oil_customer_issue';

    protected $guarded = ["id"];
    protected $fillable = ['caseno','caseid','customer_id','customername','account','truckno','contactname','phone','callback_num','content','status','level','reason1','reason2','reason3','org_id','orgname','exclusive_custom','username','usertime','hangendtime','third_createtime','third_updatetime','createtime','updatetime','last_operator'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By caseno
        if (isset($params['caseno']) && $params['caseno'] != '') {
            $query->where('caseno', '=', $params['caseno']);
        }

        //Search By caseid
        if (isset($params['caseid']) && $params['caseid'] != '') {
            $query->where('caseid', '=', $params['caseid']);
        }

        //Search By customer_id
        if (isset($params['customer_id']) && $params['customer_id'] != '') {
            $query->where('customer_id', '=', $params['customer_id']);
        }

        //Search By customername
        if (isset($params['customer']) && $params['customer'] != '') {
            $query->where('customername', 'like', '%'.$params['customer'].'%');
        }

        //Search By account
        if (isset($params['account']) && $params['account'] != '') {
            $query->where('account', '=', $params['account']);
        }

        //Search By truckno
        if (isset($params['truckno']) && $params['truckno'] != '') {
            $query->where('truckno', '=', $params['truckno']);
        }

        //Search By contactname
        if (isset($params['contactname']) && $params['contactname'] != '') {
            $query->where('contactname', '=', $params['contactname']);
        }

        //Search By phone
        if (isset($params['phone']) && $params['phone'] != '') {
            $query->where('phone', '=', $params['phone']);
        }

        //Search By callback_num
        if (isset($params['callback_start']) && $params['callback_start'] != '') {
            $query->where('callback_num', '>=', $params['callback_start']);
        }

        //Search By third_createtime
        if (isset($params['callback_end']) && $params['callback_end'] != '') {
            $query->where('callback_num', '<=', $params['callback_end']);
        }

        //Search By content
        if (isset($params['content']) && $params['content'] != '') {
            $query->where('content', '=', $params['content']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By level
        if (isset($params['level']) && $params['level'] != '') {
            $query->where('level', '=', $params['level']);
        }

        //Search By reason1
        if (isset($params['reason1']) && $params['reason1'] != '') {
            $query->where('reason1', '=', $params['reason1']);
        }

        //Search By reason2
        if (isset($params['reason2']) && $params['reason2'] != '') {
            $query->where('reason2', '=', $params['reason2']);
        }

        //Search By reason3
        if (isset($params['classify']) && $params['classify'] != '') {
            $query->where('reason3', 'like', $params['classify'].'%');
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By orgname
        if (isset($params['orgname']) && $params['orgname'] != '') {
            $query->where('orgname', '=', $params['orgname']);
        }

        if (isset($params['orgNameLike']) && $params['orgNameLike'] != '') {
            $query->where('orgname', 'like', '%'.$params['orgNameLike'].'%');
        }

        //Search By exclusive_custom
        if (isset($params['exclusive_custom']) && $params['exclusive_custom'] != '') {
            $query->where('exclusive_custom', '=', $params['exclusive_custom']);
        }

        //Search By username
        if (isset($params['username']) && $params['username'] != '') {
            $query->where('username', '=', $params['username']);
        }

        //Search By usertime
        if (isset($params['usertime']) && $params['usertime'] != '') {
            $query->where('usertime', '=', $params['usertime']);
        }

        //Search By hangendtime
        if (isset($params['hangendtime']) && $params['hangendtime'] != '') {
            $query->where('hangendtime', '=', $params['hangendtime']);
        }

        //Search By third_createtime
        if (isset($params['start_timeGe']) && $params['start_timeGe'] != '') {
            $query->where('third_createtime', '>=', $params['start_timeGe']);
        }

        //Search By third_createtime
        if (isset($params['end_timeLe']) && $params['end_timeLe'] != '') {
            $str = trim($params['end_timeLe']);
            $endTimeLe = preg_match('/^\d{4}(\-|\/|.)\d{1,2}\1\d{1,2}$/', $str, $matches) ? $str . ' 23:59:59' : $str;
            $query->where('third_createtime', '<=', $endTimeLe);
        }


        //Search By third_updatetime
        if (isset($params['third_updatetime']) && $params['third_updatetime'] != '') {
            $query->where('third_updatetime', '=', $params['third_updatetime']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        return $query;
    }

    /**
     * 客户问题反馈 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $sqlObj = OilCustomerIssue::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('createtime', 'desc')->get();
        }elseif(isset($params['_count']) && $params['_count'] == 1){
            return $sqlObj->count();
        }elseif (isset($params['skip']) && isset($params['take'])) {
            return $sqlObj->orderBy('createtime', 'desc')->skip(intval($params['skip']))->take(intval($params['take']))->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 客户问题反馈 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCustomerIssue::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCustomerIssue::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 客户问题反馈 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCustomerIssue::create($params);
    }

    /**
     * 客户问题反馈 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);
        $mainArr['updatetime'] = \helper::nowTime();
        global $app;
        $params['last_operator'] = $app->myAdmin->true_name;
        return OilCustomerIssue::find($params['id'])->update($params);
    }

    /**
     * 客户问题反馈 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCustomerIssue::destroy($params['ids']);
    }
    /**
     * 获取最后一条记录
     */
    static public function getLastData(array $params){
        if( count($params) > 0 ) {
            return OilCustomerIssue::where($params)->orderBy("id", "desc")->firstOrFail()->toArray();
        }else{
            return OilCustomerIssue::orderBy("third_updatetime", "desc")->take(1)->get()->toArray();
        }
    }
    /**
     * 查找及更新
     */
    static public function updateOrAdd (array $params,$values){
        \helper::argumentCheck(['caseid'],$params);
        return OilCustomerIssue::updateOrCreate($params,$values);
    }
    /**
     * 获取问题分类
     */
    static public function getReasonList($field='id',$isdistinct = false){
        if($isdistinct) {
            return OilCustomerIssue::select($field)->distinct()->get()->toArray();
        }else{
            return OilCustomerIssue::select($field)->get();
        }
    }

}