<?php
/**
 * 内部票发票申请明细
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/11/17
 * Time: 19:20:44
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilReceiptApplyInternalDetails extends \Framework\Database\Model
{
    protected $table = 'oil_receipt_apply_internal_details';

    protected $guarded = ["id"];
    protected $fillable = ['receipt_apply_id','receipt_return_id','creator_id','creator_name','last_operator_id',
        'last_operator','createtime','updatetime','receipt_translate_detail_id'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By receipt_apply_id
        if (isset($params['receipt_apply_id']) && $params['receipt_apply_id'] != '') {
            $query->where('receipt_apply_id', '=', $params['receipt_apply_id']);
        }

        //Search By receipt_return_id
        if (isset($params['receipt_return_id']) && $params['receipt_return_id'] != '') {
            $query->where('receipt_return_id', '=', $params['receipt_return_id']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By creator_name
        if (isset($params['creator_name']) && $params['creator_name'] != '') {
            $query->where('creator_name', '=', $params['creator_name']);
        }

        //Search By last_operator_id
        if (isset($params['last_operator_id']) && $params['last_operator_id'] != '') {
            $query->where('last_operator_id', '=', $params['last_operator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By receipt_translate_detail_id
        if (isset($params['receipt_translate_detail_id']) && $params['receipt_translate_detail_id'] != '') {
            $query->where('receipt_translate_detail_id', '=', $params['receipt_translate_detail_id']);
        }


        return $query;
    }

    /**
     * 内部票发票申请明细 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilReceiptApplyInternalDetails::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 内部票发票申请明细 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptApplyInternalDetails::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptApplyInternalDetails::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 内部票发票申请明细 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilReceiptApplyInternalDetails::create($params);
    }

    /**
     * 内部票发票申请明细 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptApplyInternalDetails::find($params['id'])->update($params);
    }

    /**
     * 内部票发票申请明细 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilReceiptApplyInternalDetails::destroy($params['ids']);
    }


    static public function getPluckField(array $params, $pluckField)
    {
        $res = self::Filter($params)->pluck($pluckField)->toArray();

        return !empty($res[0]) ? $res[0] : '';
    }

    static public function getInfoByFilter(array $params)
    {

        return self::Filter($params)->first();
    }
}