<?php
/**
 * 副卡返利记录表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:18
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilFanliRecords extends \Framework\Database\Model
{
    protected $table = 'oil_fanli_records';

    protected $guarded = ["id"];

    protected $fillable = ['fanli_no','policy_id','main_id','fanli_money','fanli_jifen','starttime','endtime','createtime','creator_id','auditor_id'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By fanli_no
        if (isset($params['fanli_no']) && $params['fanli_no'] != '') {
            $query->where('fanli_no', '=', $params['fanli_no']);
        }

        //Search By policy_id
        if (isset($params['policy_id']) && $params['policy_id'] != '') {
            $query->where('policy_id', '=', $params['policy_id']);
        }

        //Search By main_id
        if (isset($params['main_id']) && $params['main_id'] != '') {
            $query->where('main_id', '=', $params['main_id']);
        }

        //Search By fanli_money
        if (isset($params['fanli_money']) && $params['fanli_money'] != '') {
            $query->where('fanli_money', '=', $params['fanli_money']);
        }

        //Search By fanli_jifen
        if (isset($params['fanli_jifen']) && $params['fanli_jifen'] != '') {
            $query->where('fanli_jifen', '=', $params['fanli_jifen']);
        }

        //Search By starttime
        if (isset($params['starttime']) && $params['starttime'] != '') {
            $query->where('starttime', '=', $params['starttime']);
        }

        //Search By endtime
        if (isset($params['endtime']) && $params['endtime'] != '') {
            $query->where('endtime', '=', $params['endtime']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By auditor_id
        if (isset($params['auditor_id']) && $params['auditor_id'] != '') {
            $query->where('auditor_id', '=', $params['auditor_id']);
        }


        return $query;
    }

    /**
     * 副卡返利记录表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilFanliRecords::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 副卡返利记录表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilFanliRecords::find($params['id']);
    }

    /**
     * 副卡返利记录表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilFanliRecords::create($params);
    }

    /**
     * 副卡返利记录表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilFanliRecords::find($params['id'])->update($params);
    }

    /**
     * 副卡返利记录表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilFanliRecords::destroy($params['ids']);
    }




}