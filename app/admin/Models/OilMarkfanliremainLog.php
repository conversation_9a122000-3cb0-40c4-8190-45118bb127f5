<?php
/**
 * 使用返利扣减余额标记消费记录
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/10/23
 * Time: 15:29:16
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilMarkfanliremainLog extends \Framework\Database\Model
{
    protected $table = 'oil_markfanliremain_log';

    protected $guarded = ["id"];
    protected $fillable = ['trade_api_id','org_id','mark_fee','type','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        return $query;
    }

    /**
     * 返利扣减余额标记消费 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = self::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 返利扣减余额标记消费 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return self::find($params['id']);
    }

    /**
     * 返利扣减余额标记消费 详情查询
     * @param array $params
     * @return object
     */
    static public function getByTradeId(array $params)
    {
        \helper::argumentCheck(['trade_api_id','type'],$params);

        return self::where("trade_api_id",$params['trade_api_id'])->where("type",$params['type'])->first();
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return self::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 返利扣减余额标记消费 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        global $app;
        $params['createtime'] = \helper::nowTime();
        $params['updatetime'] = \helper::nowTime();
        return self::create($params);
    }

    /**
     * 返利扣减余额标记消费 批量新增
     * @param array $params
     * @return mixed
     */
    static public function batchAdd(array $params)
    {
        return self::insert($params);
    }

    /**
     * 返利扣减余额标记消费 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return self::find($params['id'])->update($params);
    }

    /**
     * 返利扣减余额标记消费 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return self::destroy($params['ids']);
    }




}