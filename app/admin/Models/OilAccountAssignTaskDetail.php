<?php
/**
 * oil_account_assign_task_detail
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/05/23
 * Time: 21:15:04
 */
namespace Models;
use Framework\Helper;
use Framework\Log;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilAccountAssignTaskDetail extends \Framework\Database\Model
{
    protected $table = 'oil_account_assign_task_detail';

    protected $guarded = ["id"];

    protected $fillable = ['task_id','taskId','assign_id','assign_detail_id','status','vice_no','createtime','updatetime',
        'message', 'master_status', 'master_status','creator_name'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By task_id
        if (isset($params['task_id']) && $params['task_id'] != '') {
            $query->where('task_id', '=', $params['task_id']);
        }

        //Search By assign_id
        if (isset($params['assign_id']) && $params['assign_id'] != '') {
            $query->where('assign_id', '=', $params['assign_id']);
        }

        //Search By assign_detail_id
        if (isset($params['assign_detail_id']) && $params['assign_detail_id'] != '') {
            $query->where('assign_detail_id', '=', $params['assign_detail_id']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }


        return $query;
    }

    /**
     * oil_account_assign_task_detail 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilAccountAssignTaskDetail::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_account_assign_task_detail 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountAssignTaskDetail::find($params['id']);
    }

    /**
     * oil_account_assign_task_detail 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilAccountAssignTaskDetail::create($params);
    }

    /**
     * oil_account_assign_task_detail 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountAssignTaskDetail::find($params['id'])->update($params);
    }

    static public function updateByTaskId(array $params)
    {
        \helper::argumentCheck(['taskId'],$params);

        return OilAccountAssignTaskDetail::where('taskId',$params['taskId'])->update($params);
    }

    /**
     * oil_account_assign_task_detail 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilAccountAssignTaskDetail::destroy($params['ids']);
    }

    /**
     * 根据任务id获取分配明细ids
     * @param $taskId
     * @return mixed
     */
    static public function getAssignDetailIds($taskId)
    {
        return OilAccountAssignTaskDetail::where('taskId',$taskId)->pluck('assign_detail_id')->toArray();
    }

    /**
     * PDO批量修改
     * @param array $apiData
     * @return bool|int
     */
    static public function batchEditByPdo($apiData = [])
    {
        //批量入库
        $data = FALSE;
        if ($apiData) {

            $batchSql = Helper::getBatchEditSql($apiData,'oil_account_assign_task_detail');

            Log::info('edit_sql',[$batchSql],'taskDetailsEdit');

            $data = Capsule::connection()->getPdo()->exec($batchSql);
        }

        return $data;
    }

    /*
     * 根据明细id获取所有任务相关明细
     */
    static public function getTaskListByDetailId($detail_id)
    {
        return self::where('assign_detail_id',$detail_id)->get();
    }
}