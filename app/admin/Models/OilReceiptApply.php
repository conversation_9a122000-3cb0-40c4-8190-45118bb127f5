<?php
/**
 * oil_receipt_apply
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:19
 */
namespace Models;
use Fuel\Defines\DataFrom;
use Fuel\Defines\OilType;
use Fuel\Defines\ReceiptApplyStatus;
use Fuel\Defines\ReceiptManageChoiceStatus;
use Fuel\Defines\ReceiptManageColorType;
use Fuel\Defines\ReceiptManageStatus;
use Fuel\Defines\ReceiptSalesCheckStatus;
use Fuel\Defines\ReceiptSalesReceiptStatus;
use Fuel\Defines\ReceiptScope;
use Fuel\Defines\ReceiptType;
use Fuel\Service\ReceiptApply;
use Fuel\Service\ReceiptApplyInternal;
use Fuel\Service\ReceiptManage;
use GosSDK\Defines\Methods\Operators;
use GosSDK\Defines\Methods\OrgAddrForG7s;
use GosSDK\Defines\Methods\OrgContactsForG7s;
use GosSDK\Gos;
use Illuminate\Database\Capsule\Manager as Capsule;
use Framework\Log;
use Fuel\Defines\ReceiptApplyDefine;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class OilReceiptApply
 * @package Models
 *
 * @property $id
 * @property $is_internal
 */
class OilReceiptApply extends \Framework\Database\Model
{
    protected $table = 'oil_receipt_apply';

    protected $guarded = ["id"];

    protected $fillable = ['sn','no','no_type', 'is_internal','org_id','org_code','org_name','receipt_title_id',
        'pay_company_id','receipt_amount','receipt_status',
        'receipt_type','org_addr_id','org_contact_id','deliver_corp','deliver_no','apply_time','createtime','data_from',
        'creator_id','creator_name','other_creator_id','other_creator','last_operator_id','last_operator','custom_remark',
        'contact_name','mobile','address','admin_remark','updatetime','oil_type','receipt_remark',
        'seller_name','seller_taxpayer_no','seller_company_code','customer_type','trade_end_time', 'unit','corp_name',
        'pay_company_name','trade_start_time','receipt_time','receipt_num','assgin_amount','org_operator_id', 'email',
        'open_channel','is_sign','split_amount_type', 'apply_fetch_amount_diff','month_root','stock','basis'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function OilOrg()
    {
        return $this->belongsTo('Models\OilOrg','org_id','id');
    }

    public function ReceiptTitle()
    {
        return $this->belongsTo('Models\OilReceiptTitle','receipt_title_id','id');
    }

    public function OilContact()
    {
        return $this->belongsTo('Models\OilOrgContacts','org_contact_id','id');
    }

    public function OilAddr()
    {
        return $this->belongsTo('Models\OilOrgAddr','org_addr_id','id');
    }

    public function Creator()
    {
        return $this->belongsTo('Models\GspSysUsers', 'creator_id');
    }


    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id'])) {
            if (is_array($params['id']) && !empty($params['id'])) {
                $query->whereIn('oil_receipt_apply.id', $params['id']);
            } elseif ($params['id'] != '') {
                $query->where('oil_receipt_apply.id', '=', $params['id']);
            }
        }
        //Search By ids
        if (isset($params['ids']) && $params['ids']) {
            $params['ids'] = is_array($params['ids']) ? $params['ids'] : explode(',',$params['ids']);
            $query->whereIn('oil_receipt_apply.id', $params['ids']);
        }
        if (isset($params['org_id_list']) && $params['org_id_list'] != '') {
            $query->whereIn('org_id', $params['org_id_list']);
        }
        if (isset($params['org_addr_id']) && $params['org_addr_id'] != '') {
            $query->where('org_addr_id', '=', $params['org_addr_id']);
        }
        if (isset($params['org_contact_id']) && $params['org_contact_id'] != '') {
            $query->where('org_contact_id', '=', $params['org_contact_id']);
        }

        if (isset($params['no']) && $params['no']) {
            $query->whereIn('no', explode('|',$params['no']));
        }

        if (isset($params['sn']) && $params['sn']) {
            $query->where('sn', $params['sn']);
        }

        //Search By no_type
        if (isset($params['no_type']) && $params['no_type'] != '') {
            $query->where('no_type', '=', $params['no_type']);
        }

        if (! empty($params['is_internal'])) {
            $query->where('is_internal', '=', $params['is_internal']);
        }

        //Search By pay_company_id
        if (isset($params['pay_company_id']) && $params['pay_company_id'] != '') {
            $query->where('pay_company_id', '=', $params['pay_company_id']);
        }

        //Search By org_operator_id
        if (isset($params['org_operator_id']) && $params['org_operator_id'] != '') {
            $query->where('oil_receipt_apply.org_operator_id', '=', $params['org_operator_id']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

	    if( (isset($params['orgcode_eq']) && $params['orgcode_eq'])){
		    $query->where('oil_receipt_apply.org_code', $params['orgcode_eq']);
	    }

	    if( (isset($params['orgcode_lk']) && $params['orgcode_lk'])){
		    $query->where('oil_receipt_apply.org_code', 'like', $params['orgcode_lk'].'%');
	    }

        if (isset($params['orgcode_own']) && $params['orgcode_own'] != '') {
            $orgcode = explode(",",$params['orgcode_own']);
            $query->whereIn('org_code', $orgcode);
        }

        //Search By org_code
        if (isset($params['org_code']) && $params['org_code'] != '') {
            $query->where('org_code', 'like', $params['org_code'].'%');
        }
        if (isset($params['orgcode_list']) && $params['orgcode_list'] != '') {
            $query->where('org_code', 'like', '%'.$params['orgcode_list'].'%');
        }

        //Search By org_name
        if (isset($params['org_name']) && $params['org_name'] != '') {
            $query->where('org_name', '=', $params['org_name']);
        }

        //Search By receipt_title_id
        if (isset($params['receipt_title_id']) && $params['receipt_title_id'] != '') {
            $query->where('receipt_title_id', '=', $params['receipt_title_id']);
        }

        //Search By receipt_amount
        if (isset($params['receipt_amount']) && $params['receipt_amount'] != '') {
            $query->where('receipt_amount', '=', $params['receipt_amount']);
        }
        if (isset($params['is_not_del']) && $params['is_not_del']) {
            $query->whereIn('receipt_status', $params['is_not_del']);
        }

        //Search By receipt_status
        if (isset($params['receipt_status']) && $params['receipt_status'] != '') {
            $query->where('receipt_status', '=', $params['receipt_status']);
        }

        //Search By receipt_statusIn
        if (isset($params['receipt_statusIn']) && $params['receipt_statusIn']) {
            $query->whereIn('receipt_status', $params['receipt_statusIn']);
        }

        //Search By deliver_corp
        if (isset($params['deliver_corp']) && $params['deliver_corp'] != '') {
            $query->where('deliver_corp', '=', $params['deliver_corp']);
        }

        //Search By deliver_no
        if (isset($params['deliver_no']) && $params['deliver_no'] != '') {
            $query->where('deliver_no', '=', $params['deliver_no']);
        }

        //Search By apply_time
//        if (isset($params['apply_time']) && $params['apply_time'] != '') {
//            $query->where('apply_time', '=', $params['apply_time']);
//        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By data_from
        if (isset($params['data_from']) && $params['data_from'] != '') {
            $query->where('data_from', '=', $params['data_from']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where(function($query) use($params){
                $query->where('oil_receipt_apply.other_creator', '=', $params['creator_id'])->orwhere('oil_receipt_apply.creator_name','=',$params['creator_id']);
            });
        }
        if(isset($params['receipt_type']) && $params['receipt_type'] != ''){
            $query->where('oil_receipt_apply.receipt_type','=',$params['receipt_type']);
        }
        //Search By other_creator_id
        if (isset($params['other_creator_id']) && $params['other_creator_id'] != '') {
            $query->where('other_creator_id', '=', $params['other_creator_id']);
        }
        if (isset($params['receipt_amount_from']) && $params['receipt_amount_from'] != '') {
            $query->where('receipt_amount', '>=', $params['receipt_amount_from']);
        }
        if (isset($params['receipt_amount_to']) && $params['receipt_amount_to'] != '') {
            $query->where('receipt_amount', '<=', $params['receipt_amount_to']);
        }
        if (isset($params['apply_time_from']) && $params['apply_time_from'] != '') {
            $query->where('apply_time', '>=', $params['apply_time_from']);
        }
        if (isset($params['apply_time_to']) && $params['apply_time_to'] != '') {
            if(preg_match('/^\d{4}-\d{2}-\d{2}$/',$params['apply_time_to'])){
                $params['apply_time_to'] = $params['apply_time_to'].' 23:59:59';
            }
            $query->where('apply_time', '<=', $params['apply_time_to']);
        }
        //Search By other_creator
        if (isset($params['other_creator']) && $params['other_creator'] != '') {
            $query->where('other_creator', 'like', '%'.$params['other_creator'].'%');
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('oil_receipt_apply.last_operator', '=', $params['last_operator']);
        }

        //Search By custom_remark
        if (isset($params['custom_remark']) && $params['custom_remark'] != '') {
            $query->where('custom_remark', '=', $params['custom_remark']);
        }

        //Search By admin_remark
        if (isset($params['admin_remark']) && $params['admin_remark'] != '') {
            $query->where('admin_remark', '=', $params['admin_remark']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        if (isset($params['operators_id']) && $params['operators_id'] != '') {
            $query->where('oil_org.operators_id', '=', $params['operators_id']);
        }

        if (isset($params['oil_type']) && $params['oil_type'] != '') {
            if( $params['oil_type'] == 15 ){
                $query->where(function($query) use($params){
                    $query->where('oil_receipt_apply.oil_type', '=', 12)->where('oil_receipt_apply.is_internal','=',1);
                });
            }elseif($params['oil_type'] == 12){
                $query->where(function($query) use($params){
                    $query->where('oil_receipt_apply.oil_type', '=', 12)->where('oil_receipt_apply.is_internal','=',2);
                });
            }else{
                $query->where('oil_receipt_apply.oil_type', '=', $params['oil_type']);
            }
        }

        if (isset($params['snNull']) && $params['snNull']) {
            $query->whereNull('sn');
        }

        if (isset($params['seller_name']) && $params['seller_name'] != '') {
            $query->where('oil_receipt_apply.seller_name', '=', $params['seller_name']);
        }


        //销售方 LIKE
        if (isset($params['seller_name_like']) && $params['seller_name_like'] != '') {
            $query->where('oil_receipt_apply.seller_name','like', '%' . $params['seller_name_like'] . '%');
        }

        //购买方 LIKE
        if (isset($params['org_name_like']) && $params['org_name_like'] != '') {
            $query->where('oil_receipt_apply.org_name','like', '%' . $params['org_name_like'] . '%');
        }

        if (isset($params['trade_start_like']) && $params['trade_start_like'] != '') {
            $query->where('oil_receipt_apply.trade_start_time','like', '%' . $params['trade_start_like'] . '%');
        }

        if (isset($params['open_channel']) && $params['open_channel']) {
            $query->where('open_channel', '=', $params['open_channel']);
        }

        if (isset($params['seller_taxpayer_no']) && $params['seller_taxpayer_no']) {
            $query->where('oil_receipt_apply.seller_taxpayer_no', '=', $params['seller_taxpayer_no']);
        }

        if (isset($params['gms_order_id']) && $params['gms_order_id']) {
            $query->where('oil_receipt_apply.gms_order_id', '=', $params['gms_order_id']);
        }

        if (isset($params['receipt_title_id_arr']) && !empty($params['receipt_title_id_arr'])) {
            $query->whereIn('receipt_title_id', $params['receipt_title_id_arr']);
        }

        if (isset($params['apply_fetch_amount_diff']) && $params['apply_fetch_amount_diff']) {
            $query->where('oil_receipt_apply.apply_fetch_amount_diff', '=', $params['apply_fetch_amount_diff']);
        }

        return $query;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilterForweb($query, $params)
    {
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('oil_receipt_apply.id', '=', $params['id']);
        }
        if (isset($params['org_id_list']) && $params['org_id_list']) {
            $query->whereIn('org_id', $params['org_id_list']);
        }
        if (isset($params['org_addr_id']) && $params['org_addr_id'] != '') {
            $query->where('org_addr_id', '=', $params['org_addr_id']);
        }
        if (isset($params['org_contact_id']) && $params['org_contact_id'] != '') {
            $query->where('org_contact_id', '=', $params['org_contact_id']);
        }

        if (isset($params['corp_name']) && $params['corp_name'] != '') {
            $query->where('oil_receipt_title.corp_name', 'like', '%'.$params['corp_name'].'%');
        }

        //Search By no
        if (isset($params['no']) && $params['no']) {
            Log::info('no--'.var_export(explode('|',$params['no']),true),[],'zzg');
            $query->whereIn('no', explode('|',$params['no']));
        }

        //Search By no_type
        if (isset($params['no_type']) && $params['no_type'] != '') {
            $query->where('no_type', '=', $params['no_type']);
        }

        //Search By org_operator_id
        if (isset($params['org_operator_id']) && $params['org_operator_id'] != '') {
            $query->where('oil_receipt_apply.org_operator_id', '=', $params['org_operator_id']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

	    if( (isset($params['orgcode_eq']) && $params['orgcode_eq'])){
		    $query->where('oil_receipt_apply.org_code', $params['orgcode_eq']);
	    }

	    if( (isset($params['orgcode_lk']) && $params['orgcode_lk'])){
		    $query->where('oil_receipt_apply.org_code', 'like', $params['orgcode_lk'].'%');
	    }

        //Search By org_code
        if (isset($params['org_code']) && $params['org_code'] != '') {
	        $query->where('oil_receipt_apply.org_code', 'like', $params['org_code'].'%');
        }

        if (isset($params['orgcode_own']) && $params['orgcode_own'] != '') {
            $orgcode = explode(",",$params['orgcode_own']);
            $query->whereIn('oil_receipt_apply.org_code', $orgcode);
        }

        if (isset($params['orgcode_list']) && $params['orgcode_list'] != '') {
            $query->where('oil_receipt_apply.org_code', 'like', $params['orgcode_list'].'%');
        }

        //Search By org_name
        if (isset($params['org_name']) && $params['org_name'] != '') {
            $query->where('oil_receipt_apply.org_name', '=', $params['org_name']);
        }

        //Search By receipt_title_id
        if (isset($params['receipt_title_id']) && $params['receipt_title_id'] != '') {
            $query->where('oil_receipt_apply.receipt_title_id', '=', $params['receipt_title_id']);
        }

        //Search By receipt_amount
        if (isset($params['receipt_amount']) && $params['receipt_amount'] != '') {
            $query->where('oil_receipt_apply.receipt_amount', '=', $params['receipt_amount']);
        }
        if (isset($params['is_not_del']) && $params['is_not_del'] != '') {
            $query->whereIn('oil_receipt_apply.receipt_status', $params['is_not_del']);
        }

        //Search By receipt_status
        if (isset($params['receipt_status']) && $params['receipt_status'] != '') {
            $query->where('oil_receipt_apply.receipt_status', '=', $params['receipt_status']);
        }

        //Search By deliver_corp
        if (isset($params['deliver_corp']) && $params['deliver_corp'] != '') {
            $query->where('oil_receipt_apply.deliver_corp', '=', $params['deliver_corp']);
        }

        //Search By deliver_no
        if (isset($params['deliver_no']) && $params['deliver_no'] != '') {
            $query->where('oil_receipt_apply.deliver_no', '=', $params['deliver_no']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('oil_receipt_apply.createtime', '=', $params['createtime']);
        }


        if(isset($params['receipt_type']) && $params['receipt_type'] != ''){
            $query->where('oil_receipt_apply.receipt_type','=',$params['receipt_type']);
        }

        if (isset($params['receipt_amount_from']) && $params['receipt_amount_from'] != '') {
            $query->where('oil_receipt_apply.receipt_amount', '>=', $params['receipt_amount_from']);
        }
        if (isset($params['receipt_amount_to']) && $params['receipt_amount_to'] != '') {
            $query->where('oil_receipt_apply.receipt_amount', '<=', $params['receipt_amount_to']);
        }
        if (isset($params['apply_time_from']) && $params['apply_time_from'] != '') {
            $query->where('oil_receipt_apply.apply_time', '>=', $params['apply_time_from']);
        }
        if (isset($params['apply_time_to']) && $params['apply_time_to'] != '') {
            $query->where('oil_receipt_apply.apply_time', '<=', $params['apply_time_to']);
        }

        if (isset($params['snNull']) && $params['snNull']) {
            $query->whereNull('sn');
        }

        if (isset($params['open_channel']) && $params['open_channel']) {
            $query->where('open_channel', '=', $params['open_channel']);
        }

        return $query;
    }

    /**
     * oil_receipt_apply 列表查询
     * @param array $params
     * @return array | Collection
     */
    static public function getList(array $params)
    {
    	//Capsule::connection()->enableQueryLog();
        $data = [];
        if(isset($params['requestFrom']) && $params['requestFrom'] == 'g7s'){
            $params['limit'] = isset($params['page_size']) ? $params['page_size'] : 10;
            $params['page'] = isset($params['page_no']) ? $params['page_no'] : 1;
        }else{
            $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
            $params['page'] = isset($params['page']) ? $params['page'] : 1;
        }

        //根据gms订单号，查询发票申请单
        if( isset($params['order_id']) && !empty($params['order_id']) ){
            $details = OilReceiptApplyDetails::getList(['order_id'=>$params['order_id']]);
            if(count($details) > 0){
                $app_id = [];
                foreach ($details as $_item){
                    $app_id[] = $_item->receipt_apply_id;
                }
                if(count($app_id) > 0){
                    $params['ids'] = $app_id;
                }
            }else{
                return [];
            }
            unset($params['order_id']);
        }

        $sqlObj = OilReceiptApply::Filter($params)->with(
            [
                'OilOrg',
                'ReceiptTitle',
                'OilContact',
                'OilAddr'
            ]
        )
        ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_receipt_apply.org_id')
        ->leftJoin('gsp_sys_users', 'gsp_sys_users.id', '=', 'oil_receipt_apply.creator_id')
        ->select(Capsule::connection()->raw('oil_receipt_apply.*,gsp_sys_users.true_name'));
        if(isset($params['sortColumns'])){
            $_sortColumns = explode(" ", $params['sortColumns']);
            $orderField = $_sortColumns[0];
            $orderType = $_sortColumns[1];
        }else{
            $orderField = 'apply_time';
            $orderType = 'desc';
        }
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy($orderField,$orderType)->get();
        }else {
            $data = $sqlObj->orderBy($orderField,$orderType)->paginate($params['limit'],['*'],'page',$params['page']);
        }
        //Log::debug('sql--'.\GuzzleHttp\json_encode(Capsule::connection()->getQueryLog(),true),[],'zzg');

        $data = self::formatData($data,$params);
        if (isset($params['id']) && $params['id'] != '' && !empty($data[0]) && $data[0]->is_internal == ReceiptScope::INTERNAL) {
        }
        return $data;
    }

    static private function formatData($data,$params=[])
    {
        $contactIds = [];
        $nos = $downUrlData = [];
        $addrIds = [];
        $buyerNames = [];
        $operatorMap = OilOperators::getIdMapName('company_name');
        $taxNoMap = OilOperators::getTaxpayerMapName('id');
        $emailSendCycleMap = OilReceiptEmailLog::getEmailSendCycle([
            'receipt_apply_id' => $data->pluck('id')->toArray()
        ]);
        foreach ($data as $v) {
            if($v->OilOrg->is_receipt_white == 2){
                if($v->is_internal != ReceiptScope::INTERNAL){
                    $v->trade_start_time = '';
                    $v->trade_end_time = '';
                }
            }
            if($v->receipt_type == '数电专票' || $v->receipt_type == '数电普票'){
                $nos[] = $v->no;
            }
            $v->operators_id = 0;
            $v->taxpayer_no = $v->corp_addr = $v->bank_name = '';  // 购方纳税人识别号     地址、电话     开户行及账号

            $v->isMonthReceipt = 2;
            $v->stepMonthStr = "";
            $v->is_receipt_white = "";
            if ($v->is_internal == ReceiptScope::INTERNAL) {
//                if( !empty($v->org_operator_id) ){
//                    $v->operators_id = $v->org_operator_id;
//                    $v->org_operators_name = isset($operatorMap[$v->org_operator_id]) ? $operatorMap[$v->org_operator_id] : "";
//                }else {
                    $v->org_operators_name = $v->seller_name;
//                }
                $v->addr_name = $v->contact_name;
                $v->addr_region = $v->address;
                $v->addr_mobile = $v->mobile;

                if($v->trade_start_time == $v->trade_end_time) {
                    $v->isMonthReceipt = 1;
                    $v->stepMonthStr = $v->receipt_time."&nbsp;&nbsp;&nbsp;申请金额:".
                        $v->receipt_amount."&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;可开升数:".$v->receipt_num;
                }
                $buyerNames[] = $v->corp_name;
            } else {
                $contactIds[] = $v->org_contact_id;
                $addrIds[] = $v->org_addr_id;

                $v->orgcode = $v->OilOrg->orgcode;
                $v->org_name = $v->OilOrg->org_name;
                if( !empty($v->org_operator_id) ){
                    $v->operators_id = $v->org_operator_id;
                    $v->org_operators_name = isset($operatorMap[$v->org_operator_id]) ? $operatorMap[$v->org_operator_id] : "";
                }else {
                    $v->org_operators_name = isset($operatorMap[$v->OilOrg->operators_id]) ? $operatorMap[$v->OilOrg->operators_id] : "";
                    $v->operators_id = $v->OilOrg->operators_id;
                }
                $v->corp_name = $v->ReceiptTitle->corp_name;
                $v->taxpayer_no = $v->ReceiptTitle->taxpayer_no;//纳税人识别号
                $v->corp_addr = $v->ReceiptTitle->corp_addr;//地址、电话
                $v->bank_name = $v->ReceiptTitle->bank_name;//开户行及账号
                $v->addr_name = $v->OilAddr->name;
                $v->addr_region = $v->OilAddr->address;
                $v->is_receipt_white = $v->OilOrg->is_receipt_white == 1 ? true : false; //开票黑白名单，1：白名单，2：黑名单
            }

            $v->creator_name = $v->creator_name ? $v->creator_name : $v->other_creator;
            $v->_receipt_status = ReceiptApplyStatus::getById($v->receipt_status);
            $v->_data_from = DataFrom::getById($v->data_from);
            $v->oil_classify = $v->is_internal == ReceiptScope::INTERNAL ? OilType::getByInId($v->oil_type) : OilType::getById($v->oil_type);
            if($v->oil_classify == "甲醇汽油" && $v->is_internal == ReceiptScope::INTERNAL ){
                $v->oil_type = 15;
            }
            $v->_deliver_corp = $v->deliver_corp ? $v->deliver_corp.'_'.$v->deliver_no : '';

            $v->receipt_operator_id = isset($v->seller_taxpayer_no) && !empty($v->seller_taxpayer_no) ? $taxNoMap[$v->seller_taxpayer_no] : '';

            //历史数据不处理
            if(strtotime($v->createtime) > strtotime("2018-06-27 16:40:00")) {
                //计算实际开票金额
                //实际开票金额为发票导明细页中的申请单号下所有销售单据编号发票状态为已开，核对状态为通过且蓝票的合计金额
                $v->real_amount = OilReceiptSalesDetails::where('receipt_apply_id', $v->id)
                    ->where('receipt_status', ReceiptSalesReceiptStatus::RECEIPTED)
                    ->where('check_status', ReceiptSalesCheckStatus::AUDIT)
                    ->where('color_type', ReceiptManageColorType::BLUE)
                    ->sum('real_amount');
                //todo ENINET-4131,期初历史开票数据
                if($v->no == "FA200901133574491" && substr($v->orgcode,0,6) == "200Q4S"){
                    $v->real_amount = $v->receipt_amount;
                }
                $v->real_amount = number_format($v->real_amount, 2, ".", "");
                if (bccomp($v->receipt_amount, $v->real_amount, 2) == 0) {
                    $v->amount_status = '一致';
                } else {
                    $v->amount_status = '不一致';
                }
            }else{
                $v->amount_status = "--";
            }

            if(in_array($v->receipt_status,[ReceiptApplyStatus::PRE_AUDIT,ReceiptApplyStatus::AUDITED,ReceiptApplyStatus::HANDLING,ReceiptApplyStatus::OPENING])){
                $v->receipt_status_g7s = '<span class="label label-primary">已受理</span>';
                $v->_receipt_status_g7s = '已受理';
            }else if(in_array($v->receipt_status,[ReceiptApplyStatus::SUCCESS,ReceiptApplyStatus::MAILED])){
                $v->receipt_status_g7s = '<span class="label label-success">开票成功</span>';
                $v->_receipt_status_g7s = '开票成功';
            }else if(in_array($v->receipt_status,[ReceiptApplyStatus::REJECT])){
                $v->receipt_status_g7s = '<span class="label label-warning">已作废</span>';
                $v->_receipt_status_g7s = '已作废';
            }else{
                $v->receipt_status_g7s = '-';
            }

            $v->is_internal_txt = ReceiptScope::getIsInternalTxt($v->is_internal);

            $v->split_amount_type_txt = ReceiptScope::getSplitAmountTypeTxt($v->split_amount_type,$v->is_internal);

            //修改-导出机构编码显示科学计数法 bysxy
            if(isset($params['_export']) && $params['_export'] == 1){
                $v->orgcode = $v->orgcode . "\t";//机构
            }

            $v->open_channel_name = ReceiptType::$receipt_open_channel_list[$v->open_channel] ?? '';
            $v->email_send_cycle = (int)$emailSendCycleMap[$v->id];

            $v->sign_txt = ReceiptType::$receiptSign[$v->is_sign] ?? '';
        }

        $buyerInfo = [];
        if (! empty($buyerNames)) {
            $buyer = OilOperators::Filter(['company_name' => array_unique($buyerNames)])->get();
            foreach ($buyer as $v) {
                $buyerInfo[$v->company_name] = $v;
            }
        }

        $cInfo = [];
        if($contactIds){
            $contactInfo = (new Gos())
                ->setMethod(OrgContactsForG7s::GET_LIST)
                ->setParams(['ids'=>$contactIds,'_export'=>1])
                ->sync();

            foreach ($contactInfo as $v){
                $cInfo[$v->id] = $v;
            }
        }

        $aInfo = [];
        if($addrIds){
            $addrInfo = (new Gos())
                ->setMethod(OrgAddrForG7s::GET_LIST)
                ->setParams(['ids'=>$addrIds,'_export'=>1])
                ->sync();

            foreach ($addrInfo as $v){
                $aInfo[$v->id] = $v;
            }
        }

        //查询
        if($nos){
            $downUrlData = OilReceiptManage::whereIn('receipt_apply_no',$nos)->pluck('download_url','receipt_apply_no')->toArray();
        }

        foreach ($data as $v) {
            $v->download_url = $downUrlData[$v->no] ?? '';
            if ($v->is_internal == ReceiptScope::INTERNAL) {
                if (isset($buyerInfo[$v->corp_name])) {
                    $v->taxpayer_no = $buyerInfo[$v->corp_name]->taxpayer_no;
                    $v->bank_name = $buyerInfo[$v->corp_name]->receipt_bank. ' ' . $buyerInfo[$v->corp_name]->receipt_bank_account_no;
                    //$v->addr_mobile = $buyerInfo[$v->corp_name]->company_address. ' ' . $buyerInfo[$v->corp_name]->company_tel;
                    //$v->address = $buyerInfo[$v->corp_name]->company_address;
                    $v->corp_addr = $buyerInfo[$v->corp_name]->company_address. ' ' . $buyerInfo[$v->corp_name]->company_tel;
                }
            } else {
                if (isset($cInfo[$v->org_contact_id])) {
                    $v->contact_name = $cInfo[$v->org_contact_id]->contact_name;
                }

                if (isset($aInfo[$v->org_addr_id])) {
                    $v->addr_name   = $aInfo[$v->org_addr_id]->addr_name;
                    $v->addr_mobile = $aInfo[$v->org_addr_id]->addr_mobile;
                    $v->address     = $aInfo[$v->org_addr_id]->address;
                }
            }
        }

//        Log::info('$contactInfo--'.var_export($contactInfo,true),[],'zzg');
//        Log::info('$addrInfo--'.var_export($addrInfo,true),[],'zzg');

        return $data;
    }

    /**
     * oil_receipt_apply 列表查询
     * @param array $params
     * @return array
     */
    static public function getListForWeb(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['page_size']) ? $params['page_size'] : 10;
        $params['page'] = isset($params['page_no']) ? $params['page_no'] : 1;

        Capsule::connection()->enableQueryLog();
        $sqlObj = OilReceiptApply::FilterForweb($params)->with(
            [
                'OilContact',
                'OilAddr'
            ]
        )
            ->leftJoin('oil_receipt_title','oil_receipt_apply.receipt_title_id','=','oil_receipt_title.id')
            ->select('oil_receipt_apply.*','oil_receipt_title.corp_name');

        if(isset($params['sortColumns'])){
            $_sortColumns = explode(" ", $params['sortColumns']);
            $orderField = $_sortColumns[0] == 'corp_name' ? "convert(corp_name using gbk) " : $_sortColumns[0];
            $orderType = $_sortColumns[1];
        }else{
            $orderField = 'apply_time';
            $orderType = 'desc';
        }
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy(Capsule::connection()->raw($orderField),$orderType)->get();
        }else {
            $data = $sqlObj->orderBy(Capsule::connection()->raw($orderField),$orderType)->paginate($params['limit'],['*'],'page',$params['page']);
        }

        Log::debug('getListForWeb', Capsule::connection()->getQueryLog());
        return $data;
    }

    /**
     * @title 求累计开票之和
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function sumReceiptAmount(array $params)
    {
        return self::Filter($params)->sum('receipt_amount');
    }

    /**
     * @title   按机构统计累计开票
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Models
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function sumReceiptAmountByOrgRoot(array $params)
    {
        $sqlObj = self::where('org_code','like',$params['orgroot'].'%');

        if(isset($params['pay_company_id'])){
            $sqlObj->where('pay_company_id',$params['pay_company_id']);
        }

        if(isset($params['is_autoNq']) && $params['is_autoNq']){
            $sqlObj->where('is_auto','!=',$params['is_autoNq']);
        }

        if(isset($params['oil_type_list']) && $params['oil_type_list']){
            $sqlObj->whereIn('oil_type',$params['oil_type_list']);
        }

        if(isset($params['oil_type_eq']) && $params['oil_type_eq']){
            $sqlObj->where('oil_type','=',$params['oil_type_eq']);
        }

        if(isset($params['not_receipt_statusIn']) && $params['not_receipt_statusIn']){
            $sqlObj->whereNotIn('receipt_status',$params['not_receipt_statusIn']);
        }

        if(isset($params['receipt_statusIn']) && $params['receipt_statusIn']){
            $sqlObj->whereIn('receipt_status',$params['receipt_statusIn']);
        }

        if(isset($params['isNowOrgCode']) && $params['isNowOrgCode']){
            $sqlObj->where('org_code',$params['isNowOrgCode']);
        }

        if(isset($params['receipt_operator_id']) && !empty($params['receipt_operator_id'])){
            $sqlObj->where('org_operator_id',$params['receipt_operator_id']);
        }

        if(isset($params['createtimeGe']) && !empty($params['createtimeGe'])){
            $sqlObj->where('createtime','>=',$params['createtimeGe']);
        }

        Log::error('sumReceiptAmountByOrgRoot-$params' . var_export($params, TRUE), [], 'rrrrrrrr');
        Log::error('sumReceiptAmountByOrgRoot-sql' . var_export($sqlObj->toSql(), TRUE), [$sqlObj->getBindings()], 'rrrrrrrr');
        return $sqlObj->sum('receipt_amount');
    }

    //统计总开票金额和开票条数
    static public function countReceiptApplyMoney($params){
//        Capsule::connection()->enableQueryLog();
        $sqlObj = Capsule::connection()->table('oil_receipt_apply');
        $sqlObj->select(Capsule::connection()->raw('sum(oil_receipt_apply.receipt_amount) as receipt_amount_total,count(oil_receipt_apply.id) receipt_apply_count'));
        $sqlObj
            ->leftJoin('oil_receipt_title','oil_receipt_title.id','=','oil_receipt_apply.receipt_title_id')
            ->leftJoin('oil_org_contacts','oil_org_contacts.id','=','oil_receipt_apply.org_contact_id')
            ->leftJoin('oil_org_addr','oil_org_addr.id','=','oil_receipt_apply.org_addr_id');

        if(isset($params['apply_time_from']) && $params['apply_time_from'] != ''){
            $sqlObj->where('apply_time','>=',$params['apply_time_from']);
        }
        if(isset($params['apply_time_to']) && $params['apply_time_to'] != ''){
            $sqlObj->where('apply_time','<=',$params['apply_time_to']);
        }

        if (isset($params['corp_name']) && $params['corp_name'] != '') {
            $sqlObj->where('oil_receipt_title.corp_name', 'like', '%'.$params['corp_name'].'%');
        }
//        if(isset($params['receipt_title_id']) && $params['receipt_title_id'] != ''){
//            $sqlObj->where('receipt_title_id','=',$params['receipt_title_id']);
//        }
        if(isset($params['org_contact_id']) && $params['org_contact_id'] != ''){
            $sqlObj->where('org_contact_id','=',$params['org_contact_id']);
        }
        if(isset($params['org_addr_id']) && $params['org_addr_id'] != ''){
            $sqlObj->where('org_addr_id','=',$params['org_addr_id']);
        }
        if(isset($params['receipt_type']) && $params['receipt_type'] != ''){
            $sqlObj->where('oil_receipt_apply.receipt_type','=',$params['receipt_type']);
        }
        if(isset($params['receipt_status']) && $params['receipt_status'] != ''){
            $sqlObj->where('receipt_status','=',$params['receipt_status']);
        }
        if(isset($params['org_code']) && $params['org_code'] != ''){
            $sqlObj->where('oil_receipt_apply.org_code','like',$params['org_code'].'%');
        }
        if (isset($params['orgcode_own']) && $params['orgcode_own'] != '') {
            $orgcode = explode(",",$params['orgcode_own']);
            $sqlObj->whereIn('oil_receipt_apply.org_code', $orgcode);
        }
        $sqlObj->where('receipt_status','!=',-1);

        $group = $sqlObj->get();

        return $group;
    }

    /**
     * oil_receipt_apply 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptApply::with(
            [
                'ReceiptTitle',
                'OilContact',
                'OilAddr'
            ])->find($params['id']);
    }

    /**
     * @title 获取单条记录
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getSingleRecord(array $params,$is_order = false)
    {
        //Capsule::connection()->enableQueryLog();
        $obj = self::Filter($params);
        if($is_order){
            $obj = $obj->orderby("trade_end_time",'desc');
        }
        $data = $obj->first();
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        return $data;
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdForLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return self::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 悲观锁查询sn
     * @param array $params
     * @return object
     */
    static public function getBySnForLock(array $params)
    {
        \helper::argumentCheck(['sn'], $params);

        return self::where('sn',$params['sn'])->lockForUpdate()->first();
    }

    /**
     * 悲观锁查询sn
     * @param array $params
     * @return object
     */
    static public function getUniqueTitle(array $params)
    {
        $data = self::where('receipt_title_id',$params['receipt_title_id'])
            ->whereIn('receipt_status',$params['receipt_statusIn'])
            ->where('apply_time',">=",$params['apply_time_from'])
            ->where('oil_type',$params['oil_type'])
            ->where('org_code', 'like', $params['org_code'].'%')
            ->lockForUpdate()
            ->first();
        return $data;
    }

    /**
     * 生成工单单号
     * @param string $flag
     * @return string
     */
    static public function createOrderNo($flag = 'FA')
    {
        $microTime = microtime();
        $microArr  = explode(" ", $microTime);
        $str       = $flag . date("ymds", time());
        $str       .= substr($microArr[0], 3, 5);
        //$str       .= substr($microArr[1], -2);
        $str       .= sprintf('%02d', rand(0, 99));

        $exist = self::where("no", "=", $str)->lockForUpdate()->first();
        if ($exist) {
            self::createOrderNo($flag);
        }

        return $str;
    }

    /**
     * oil_receipt_apply 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        global $app;
        $params['no'] = self::createOrderNo();
        $params['no_type'] = 'FA';
        if(isset($app->myAdmin)){
            $params['creator_id'] = $app->myAdmin->id;
            $params['creator_name'] = $app->myAdmin->true_name;
        }

        $params['createtime'] = \helper::nowTime();

        $data = OilReceiptApply::create($params);

        return $data;
    }

    /**
     * oil_receipt_apply 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);
        global $app;
        $params['last_operator_id'] = $app->myAdmin->id;
        //$params['last_operator'] = $app->myAdmin->true_name;
        $params['updatetime'] = \helper::nowTime();

        return OilReceiptApply::find($params['id'])->update($params);
    }

    /**
     * oil_receipt_apply 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilReceiptApply::destroy($params['ids']);
    }

    static public function updateByNo(array $params)
    {
        \helper::argumentCheck(['no'],$params);

        return self::where('no',$params['no'])->update($params);
    }

    /**
     * getFanliAll 获取按月返利的统计
     * @param array $params
     * <AUTHOR>
     * @since ${DATE}
     */
    static public function getReceiptAll(array $params)
    {
        $pageSize = $params['limit'] ? $params['limit'] : 10;
        $pageNo = $params['page'] ? $params['page'] : 1;

        $_startMonth = $pageSize * ($pageNo - 1);
        $startMonth = date("Ym",strtotime("-{$_startMonth} month"));

        $_endMonth = ($pageSize * $pageNo) - 1;
        $endMonth = date("Ym",strtotime("-{$_endMonth} month"));

        $record = Capsule::connection()->select("SELECT
	DATE_FORMAT(apply_time, '%Y%m') AS month,
	sum(receipt_amount) AS receipt_money
FROM
	oil_receipt_apply
WHERE
	org_id = {$params['org_id']}
AND receipt_status != - 1
AND DATE_FORMAT(apply_time,'%Y%m') >= {$endMonth} AND DATE_FORMAT(apply_time,'%Y%m') <= {$startMonth}
GROUP BY DATE_FORMAT(apply_time, '%Y%m') ORDER BY month desc");

        $record_count = Capsule::connection()->select("SELECT
	DATE_FORMAT(apply_time, '%Y%m') AS month,
	sum(receipt_amount) AS receipt_money
FROM
	oil_receipt_apply
WHERE
	org_id = {$params['org_id']}
AND receipt_status != - 1
AND DATE_FORMAT(apply_time,'%Y%m') >= {$endMonth} AND DATE_FORMAT(apply_time,'%Y%m') <= {$startMonth}
GROUP BY DATE_FORMAT(apply_time, '%Y%m') ORDER BY month desc");

        $data = [];
        $data['data'] = $record;
        $data['total'] = count($record_count);
        $data['from'] = 1;
        $data['to'] = floor($data['total'] / $pageSize);
        $data['per_page'] = $pageSize;
        $data['current_page'] = $pageNo;
        $data['last_page'] = $data['to'];

        return $data;
    }


    /**
     * getFanliMonth 获取年月的返利流水记录
     * @param array $params
     * <AUTHOR>
     * @since ${DATE}
     */
    static public function getReceiptMonth(array $params)
    {
        $year = strlen($params['month']) > 5 ? substr($params['month'],0,4) : date("Y");
        $starttime = $params['month'] ? $year.'-'.str_pad(substr($params['month'],4,2),2,"0",STR_PAD_LEFT).'-01 00:00:00' : date("Y-m-01",time())." 00:00:00";
        $endtime = date('Y-m-d', strtotime(date('Y-m-01', strtotime($starttime)) . ' +1 month -1 day')) . ' 23:59:59';

        $sqlObj = OilReceiptApply::where('apply_time','>=',$starttime)
            ->where('apply_time','<=',$endtime)
            ->where('org_id','=',$params['org_id'])
            ->select('id','no','receipt_amount','receipt_title_id','receipt_status','deliver_corp','deliver_no','receipt_type','createtime','apply_time')
            ->orderBy('apply_time','desc');

        if($params['type'] == 'money'){
            $data = $sqlObj->sum('receipt_amount');
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     *
     * @param array $params
     * @return mixed
     */
    static public function sumReceiptAmountByOrgIdList(array $params)
    {
        return OilReceiptApply::Filter($params)->sum('receipt_amount');
    }

    /**
     * @title 统计昨天机构已邮寄的开票金额
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getOrgSendReceiptTotalForYestoday($params)
    {
        $beginTime = isset($params['begin_date']) && $params['begin_date'] ? $params['begin_date']." 00:00:00" : date("Y-m-d",strtotime("-1 day"))." 00:00:00";
        $endTime = isset($params['end_date']) && $params['end_date'] ? $params['end_date']." 23:59:59" : date("Y-m-d",strtotime("-1 day"))." 23:59:59";

        $data = OilReceiptApply::leftJoin('oil_receipt_title','oil_receipt_apply.receipt_title_id','=','oil_receipt_title.id')
            ->where('oil_receipt_apply.updatetime','>=',$beginTime)
            ->where('oil_receipt_apply.updatetime','<=',$endTime)
            ->where('oil_receipt_apply.receipt_status',ReceiptApplyStatus::MAILED)
            ->selectRaw('oil_receipt_title.corp_name as org_name,oil_receipt_apply.org_code,sum(oil_receipt_apply.receipt_amount) as totalMoney,oil_receipt_apply.updatetime')
            ->groupBy('oil_receipt_apply.org_code')
            ->get();

        return $data;
    }

    /**
     * 取字段值
     * @param array $params
     * @param $pluckField
     * @return array
     */
    static public function getPluckFields(array $params, $pluckField)
    {
        return OilReceiptApply::Filter($params)->pluck($pluckField)->toArray();
    }

    /**
     * 取字段值 返回第一个值
     * @param array $params
     * @param $pluckField
     * @return mixed
     */
    static public function getResField(array $params, $pluckField)
    {
        $res = self::Filter($params)->pluck($pluckField)->toArray();

        return !empty($res[0]) ? $res[0] : '';
    }

    /**
     * @param array $params
     * @return mixed
     */
    static public function getApplyInfoById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        $data = OilReceiptApply::leftJoin('oil_receipt_title','oil_receipt_title.id','=','oil_receipt_apply.receipt_title_id')
            ->leftJoin('oil_org','oil_org.id','=','oil_receipt_apply.org_id')
            ->leftJoin('oil_operators','oil_org.operators_id','=','oil_operators.id')
            ->select('oil_receipt_apply.id as receipt_apply_id','oil_receipt_apply.no as receipt_apply_no','oil_receipt_apply.receipt_status as receipt_status','oil_receipt_apply.receipt_amount','oil_receipt_title.corp_name as receipt_title','oil_receipt_title.taxpayer_no','oil_receipt_title.corp_addr as addr_tel',
                'oil_receipt_title.bank_name as bank_account','oil_receipt_apply.receipt_type','oil_operators.receipt_no')
            ->where('oil_receipt_apply.id',$params['id'])->first();

        return !$data ? [] : $data->toArray();
    }

    /**
     * 取总数
     * @param array $params
     * @return mixed
     */
    static public function getTotal(array $params)
    {
        return self::Filter($params)->count();
    }

    /**
     * 根据条件更新
     * @param array $params
     * @param array $updateInfo
     * @return mixed
     */
    static public function updateByFilter(array $params, array $updateInfo)
    {
        return self::Filter($params)->update($updateInfo);
    }

    /**
     * 申请单号获取申请
     * @param $no
     * @return mixed
     */
    static public function getApplyInfoByNo($no)
    {
        return OilReceiptApply::where('no','=',$no)->first();
    }


    static public function sumReceiptAmountForOperator(array $params,$isTime = false)
    {
        //Capsule::connection()->enableQueryLog();
        $sqlObj = self::where("is_internal",'=',1);
        if(isset($params['oil_type_list']) && $params['oil_type_list']){
            $sqlObj->whereIn('oil_type',$params['oil_type_list']);
        }

        if(isset($params['oil_type_eq']) && $params['oil_type_eq']){
            $sqlObj->where('oil_type','=',$params['oil_type_eq']);
        }

        if(isset($params['org_id']) && $params['org_id']){
            $sqlObj->where('org_id','=',$params['org_id']);
        }

        if(isset($params['apply_time_ge']) && $params['apply_time_ge']){
            $sqlObj->where('apply_time','>=',$params['apply_time_ge']);
        }

        if(isset($params['receipt_statusIn']) && $params['receipt_statusIn']){
            $sqlObj->whereIn('receipt_status',$params['receipt_statusIn']);
        }

        if(isset($params['seller_company_code']) && $params['seller_company_code']){
            $sqlObj->where("seller_company_code",'=',$params['seller_company_code']);
        }

        if($isTime){
            $data = $sqlObj->max("trade_end_time");
        }else{
            $data = $sqlObj->sum('receipt_amount');
        }
        //\Framework\Log::error('sumReceiptAmountForOperator -=- SQL:'.\var_export(Capsule::connection()->getQueryLog(), TRUE), [$params], 'Inner_receipt_');
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        return $data;
    }
    /**
     * Desc:
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 2/12/22 下午3:20
     * @param $start_time
     * @param $end_time
     * @param $main_no
     * @return array
     */
    static public function checkReceiptApply($params,$is_month = 2)
    {
        $sql = "SELECT *
FROM oil_receipt_apply
WHERE is_internal = 1 and org_id = '".$params['seller_id']."' and seller_company_code ='".$params['company_code']."' and oil_type ='".$params['oil_type']."'
AND receipt_status!='-1' ";
        if ($is_month == 1){
            $sql .= " and receipt_time is null and trade_start_time = trade_end_time and trade_start_time like '".$params['month']."%'";
        }else{
            $sql .= " and is_sign = ".ReceiptType::RECEIPT_NO_FOR_SIGN." and receipt_time is not null and (trade_start_time BETWEEN '".$params['trade_start_time']."' AND '".$params['trade_end_time']."' OR
trade_end_time BETWEEN '".$params['trade_start_time']."' AND '".$params['trade_end_time']."' OR
'".$params['trade_start_time']."' BETWEEN trade_start_time AND trade_end_time OR
'".$params['trade_end_time']."' BETWEEN trade_start_time AND trade_end_time)";
        }
        return Capsule::connection('slave')->select($sql);
    }


    static public function checkOilReceipt($params = [])
    {
        if(in_array($params['oil_type_txt'],['天然气类',"天然气"])){
            $params['oil_type_txt'] = "天然气";
        }
        $sql = "SELECT
  (
    CASE
      WHEN `oil_type_no`.receipt_oil_name IS NULL
      OR `oil_type_no`.receipt_oil_name = '' THEN ''
      ELSE `oil_type_no`.receipt_oil_name
    END
  ) AS oil_receipt_name,
  oil_supplier_receipt_return_unit.unit,
  group_concat(DISTINCT oil_trades.supplier_id) as supplier_id,
  group_concat(DISTINCT oil_trades.oil_name) as oil_name_str,
  group_concat(DISTINCT oil_station_supplier.supplier_name) as supplier_name_str
FROM
  `oil_trades`
  LEFT JOIN `oil_type_no` ON `oil_type_no`.`oil_no` = `oil_trades`.`oil_name`
  LEFT JOIN oil_station_supplier ON oil_trades.`supplier_id` = oil_station_supplier.id
  LEFT JOIN oil_supplier_receipt_return_unit ON oil_supplier_receipt_return_unit.supplier_id = oil_trades.supplier_id AND oil_supplier_receipt_return_unit.oil_name = '".$params['oil_type_txt']."'
  WHERE
oil_trades.`up_operator_id` = ".$params['up_operator_id']."
AND oil_trades.`down_operator_id` = ".$params['down_operator_id']."
AND oil_trades.`trade_createtime` >= '".$params['trade_start_time']."'
AND oil_type_no.`oil_base_id` IN (".implode(",",$params['oil_type']).")
AND oil_trades.supplier_id not in (".implode(",",ReceiptApplyDefine::getExceptSupplierId()).")
AND oil_trades.`is_lock_operator` = ".$params['is_lock'];

        if(isset($params['trade_end_time']) && !empty($params['trade_end_time'])){
            $sql .= " AND oil_trades.`trade_createtime` <= '".$params['trade_end_time']."'";
        }
        if(isset($params['trade_end_timeNeq']) && !empty($params['trade_end_timeNeq'])){
            $sql .= " AND oil_trades.`trade_createtime` < '".$params['trade_end_timeNeq']."'";
        }
        if( isset($params['org_root_in']) && !empty($params['org_root_in']) ){
            $sql .= " AND oil_trades.`top_orgcode` in ('".implode("','",$params['org_root_in'])."')";
        }

        $sql .= "
GROUP BY
  `oil_receipt_name`,
  oil_supplier_receipt_return_unit.unit;";
        Log::error("check-receipt",[$sql],"inner_receipt_");
        return Capsule::connection()->select($sql);
    }


    //发票数据For换签
    static public function getReceiptData($params = [])
    {
        $sql = "SELECT
	sum( receipt_amount ) AS fee,
	LEFT ( o.orgcode, 6 ) as orgroot,
	group_concat( DISTINCT IF ( r.org_operator_id IS NULL OR r.org_operator_id = 0, '空', r.org_operator_id ) ) AS operator_txt 
FROM
	oil_receipt_apply AS r
	LEFT JOIN oil_org AS o ON r.org_id = o.id 
WHERE
	r.is_internal = 2 and LEFT (o.orgcode,6 ) != '201XW3' ";

        if(isset($params['begin_time']) && $params['begin_time'] != ""){
            $sql .= " and r.createtime >= '".$params['begin_time']."'";
        }
        if(isset($params['end_time']) && $params['end_time'] != ""){
            $sql .= " and r.createtime < '".$params['end_time']."'";
        }

        if( isset($params['orgcode']) && !empty($params['orgcode']) ){
            $sql .= " and o.orgcode like '".$params['orgcode']."%'";
        }

        $sql .= " GROUP BY LEFT (o.orgcode,6 );";
        $list = Capsule::connection("slave")->select($sql);
        $map = [];
        foreach ($list as $_val){
            $map[$_val->orgroot] = [
                "operator_id" => $_val->operator_txt,
                'money' => $_val->fee,
            ];
        }
        return $map;
    }

    public static function getReceiptByApplyIds($applyIds = [])
    {
        $oilReceiptManageTableName = (new OilReceiptManage)->getTable();
        $oilReceiptApplyTableName = (new OilReceiptApply)->getTable();
        return OilReceiptApply::Filter([
            'id' => $applyIds,
        ])->where("$oilReceiptManageTableName.color_type", ReceiptManageColorType::BLUE)
          ->where("$oilReceiptManageTableName.status", ReceiptManageStatus::RECEIPTED)
          ->where("$oilReceiptManageTableName.choice_status", ReceiptManageChoiceStatus::CHOICED)
          ->leftjoin(
            $oilReceiptManageTableName,
            "$oilReceiptApplyTableName.no",
            '=',
            "$oilReceiptManageTableName.receipt_apply_no"
        )->groupBy("$oilReceiptApplyTableName.no")
                              ->get(
                                  [
                                      Capsule::connection()->raw(
                                          "$oilReceiptApplyTableName.*,count($oilReceiptManageTableName.id) as receipt_num"
                                      )
                                  ]
                              )
                              ->toArray();
    }

    static public function getSingleRecordList(array $params,$is_order = false)
    {
        //Capsule::connection()->enableQueryLog();
        $obj = self::Filter($params);
        if($is_order){
            $obj = $obj->orderby("trade_end_time",'desc');
        }
        $data = $obj->get();
        return $data;
    }
}
