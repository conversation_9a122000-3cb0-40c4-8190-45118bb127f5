<?php
/**
 * 油卡返利政策
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:18
 */

namespace Models;

use Framework\Log;
use Fuel\Defines\CoeUnit;
use Fuel\Defines\OilCom;
use Illuminate\Database\Capsule\Manager as Capsule;
use Fuel\Response;

class OilOrgRuleRelations extends \Framework\Database\Model
{
    protected $table = 'oil_org_rule_relations';

    protected $guarded = ["id"];

    protected $fillable = ['org_id', 'rule_no','is_del','createtime', 'updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }


    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {



        //Search By supplyer_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        return $query;
    }

    /**
     * 油卡返利政策 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $sqlObj = OilOrgRuleRelations::Filter($params)
            ->orderBy('rule_id', 'desc');

        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } elseif (isset($params['count']) && $params['count'] == 1) {
            return $sqlObj->count();
        } elseif (isset($params['take']) && isset($params['skip'])) {
            $data = $sqlObj->skip($params['skip'])->take($params['take'])->get();
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }
        return $data;
    }

    /**
     * 油卡返利政策 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return self::find($params['id']);
    }

    /**
     * 油卡返利政策 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        global $app;
        $input['creator_id'] = $app->myAdmin->id;
        $input['last_operator'] = $app->myAdmin->true_name;
        $input['createtime'] = \helper::nowTime();

        return self::create($params);
    }

    /**
     * 获取机构下的返利规则
     * @param array $params
     * @return mixed
     */
    static public function getRuleIds(array $params)
    {
        \helper::argumentCheck(['org_id'], $params);

        return self::where("org_id",$params['org_id'])->where("is_del",0)->pluck("rule_no");
    }

    /**
     * 油卡返利政策 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return self::find($params['id'])->update($params);
    }


    /**
     * 油卡返利政策 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return self::destroy($params['ids']);
    }

}