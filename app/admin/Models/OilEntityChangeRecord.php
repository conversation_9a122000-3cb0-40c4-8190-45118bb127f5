<?php
/**
 * 服务区/油站变更记录
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/09/01
 * Time: 17:47:40
 */
namespace Models;
use Fuel\Defines\StationArea;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilEntityChangeRecord extends \Framework\Database\Model
{
    protected $table = 'oil_entity_change_record';

    protected $guarded = ["id"];
    protected $fillable = ['no','classify','res_type','res_name','res_code','res_from_id','res_from_name','res_to_id',
        'res_to_name','remark','creator','last_operator','createtime','updatetime','origin_statistics_type','new_statistics_type',
        'change_time'
    ];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By no
        if (isset($params['no']) && $params['no'] != '') {
            $query->where('no', '=', $params['no']);
        }

        if (isset($params['noLk']) && $params['noLk'] != '') {
            $query->where('no', 'like', "%".$params['noLk']."%");
        }

        //Search By classify
        if (isset($params['classify']) && $params['classify'] != '') {
            $query->where('classify', '=', $params['classify']);
        }

        //Search By res_type
        if (isset($params['res_type']) && $params['res_type'] != '') {
            $query->where('res_type', '=', $params['res_type']);
        }

        //Search By res_name
        if (isset($params['res_name']) && $params['res_name'] != '') {
            $query->where('res_name', '=', $params['res_name']);
        }

        if (isset($params['res_nameLk']) && $params['res_nameLk'] != '') {
            $query->where('res_name', 'like', "%".$params['res_nameLk']."%");
        }

        //Search By res_code
        if (isset($params['res_code']) && $params['res_code'] != '') {
            $query->where('res_code', '=', $params['res_code']);
        }

        //Search By res_from_id
        if (isset($params['res_from_id']) && $params['res_from_id'] != '') {
            $query->where('res_from_id', '=', $params['res_from_id']);
        }

        //Search By res_from_name
        if (isset($params['res_from_name']) && $params['res_from_name'] != '') {
            $query->where('res_from_name', '=', $params['res_from_name']);
        }

        if (isset($params['res_from_nameLk']) && $params['res_from_nameLk'] != '') {
            $query->where('res_from_name', 'like', "%".$params['res_from_nameLk']."%");
        }

        //Search By res_to_id
        if (isset($params['res_to_id']) && $params['res_to_id'] != '') {
            $query->where('res_to_id', '=', $params['res_to_id']);
        }

        //Search By res_to_name
        if (isset($params['res_to_name']) && $params['res_to_name'] != '') {
            $query->where('res_to_name', '=', $params['res_to_name']);
        }

        if (isset($params['res_to_nameLk']) && $params['res_to_nameLk'] != '') {
            $query->where('res_to_name', 'like', "%".$params['res_to_nameLk']."%");
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By creator
        if (isset($params['creator']) && $params['creator'] != '') {
            $query->where('creator', '=', $params['creator']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        if (isset($params['create_timeGe']) && $params['create_timeGe'] != '') {
            $query->where('createtime', '>=', $params['create_timeGe']);
        }

        if (isset($params['create_timeLe']) && $params['create_timeLe'] != '') {
            $query->where('createtime', '<=', $params['create_timeLe']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        if (isset($params['update_timeGe']) && $params['update_timeGe'] != '') {
            $query->where('updatetime', '>=', $params['update_timeGe']);
        }

        if (isset($params['update_timeLe']) && $params['update_timeLe'] != '') {
            $query->where('updatetime', '<=', $params['update_timeLe']);
        }

        return $query;
    }

    /**
     * 服务区/油站变更记录 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilEntityChangeRecord::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('createtime', 'desc')->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        $changeList = StationArea::$entity_change_type;
        foreach ($data as &$_val){
            $_val->classify_txt = $_val->classify == 20 ? "服务区" : "油站";
            $_val->res_type_txt = isset($changeList[$_val->res_type]) ? $changeList[$_val->res_type] : "-";
            $_val->res_from_id = empty($_val->res_from_id) ? "-" : $_val->res_from_id;
            $_val->res_from_name = empty($_val->res_from_name) ? "-" : $_val->res_from_name;
            $_val->remark = empty($_val->remark) ? "" : $_val->remark;
        }

        return $data;
    }

    /**
     * 服务区/油站变更记录 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilEntityChangeRecord::find($params['id']);
    }

    static public function getInfoByFilter(array $params)
    {
        return OilEntityChangeRecord::Filter($params)->first();
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilEntityChangeRecord::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 服务区/油站变更记录 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilEntityChangeRecord::create($params);
    }

    /**
     * 服务区/油站变更记录 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilEntityChangeRecord::find($params['id'])->update($params);
    }

    /**
     * 服务区/油站变更记录 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilEntityChangeRecord::destroy($params['ids']);
    }

    static public function createNo($prefix = "SJGX")
    {
        $microTime = microtime();
        $microArr = explode(" ", $microTime);
        $str = $prefix . date("ymds", time());
        $str .= substr($microArr[0], 3, 3);
        $str .= sprintf('%02d', rand(0, 99));

        $exist = self::where("no", "=", $str)->lockForUpdate()->first();
        if ($exist) {
            self::createNo();
        }
        return $str;
    }



}