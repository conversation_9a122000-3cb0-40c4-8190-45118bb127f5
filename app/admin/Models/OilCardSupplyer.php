<?php
/**
 * oil_card_supplyer
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/11/26
 * Time: 10:07:05
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCardSupplyer extends \Framework\Database\Model
{
    protected $table = 'oil_card_supplyer';

    protected $guarded = ["id"];
    protected $fillable = ['supplyer_name','supplyer_sub_name','status','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By supplyer_name
        if (isset($params['supplyer_name']) && $params['supplyer_name'] != '') {
            $query->where('supplyer_name', '=', $params['supplyer_name']);
        }

        //Search By supplyer_sub_name
        if (isset($params['supplyer_sub_name']) && $params['supplyer_sub_name'] != '') {
            $query->where('supplyer_sub_name', '=', $params['supplyer_sub_name']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * oil_card_supplyer 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCardSupplyer::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_card_supplyer 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardSupplyer::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardSupplyer::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_card_supplyer 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCardSupplyer::create($params);
    }

    /**
     * oil_card_supplyer 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardSupplyer::find($params['id'])->update($params);
    }

    /**
     * oil_card_supplyer 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCardSupplyer::destroy($params['ids']);
    }

    static public function getSupplyerMap(array $params)
    {
        return OilCardSupplyer::where('status',0)->pluck('supplyer_name','id')->toArray();
    }

    static public function getSupplyerList(array $params)
    {
        return OilCardSupplyer::where('status',0)/*->dataRange()*/->select('supplyer_name','id')->get()->toArray();
    }
    static public function getSupplyerByName($name)
    {
        return self::where('supplyer_name',$name)->where('status',0)->first();
    }

}
