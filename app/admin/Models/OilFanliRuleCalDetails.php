<?php
/**
 * 油卡返利政策
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:18
 */

namespace Models;

use Framework\Log;
use Fuel\Defines\CoeUnit;
use Fuel\Defines\OilCom;
use Fuel\Defines\OilType;
use Illuminate\Database\Capsule\Manager as Capsule;
use Fuel\Response;

class OilFanliRuleCalDetails extends \Framework\Database\Model
{
    protected $table = 'oil_fanli_rule_cal_details';

    protected $guarded = ["id"];

    protected $fillable = ['cal_id','oil_com', 'provice_id', 'org_id', 'oil_type', 'main_id', 'fanli_jifen','fanli_money','createtime', 'updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('oil_fanli_rule.id', '=', $params['id']);
        }

        //Search By cal_id
        if (isset($params['cal_id']) && $params['cal_id'] != '') {
            $query->where('cal_id', '=', $params['cal_id']);
        }

        //Search By ids
        if (isset($params['ids']) && $params['ids']) {
            $query->whereIn('oil_fanli_rule.id', $params['ids']);
        }

        //Search By rule_object
        if (isset($params['rule_object']) && $params['rule_object'] != '') {
            $query->where('oil_fanli_rule.rule_object', '=', $params['rule_object']);
        }

        //Search By start_time
        if (isset($params['expire_time']) && $params['expire_time'] != '') {
            $query->where('oil_fanli_rule.start_time', '<=', $params['expire_time']);
        }

        //Search By end_time
        if (isset($params['expire_time']) && $params['expire_time'] != '') {
            $query->where('oil_fanli_rule.end_time', '>=', $params['expire_time']);
        }

        //Search By is_del
        if (isset($params['is_del']) && $params['is_del'] != '') {
            $query->where('oil_fanli_rule.is_del', '=', $params['is_del']);
        }

        //Search By fanLiNo
        if (isset($params['fanLiNo']) && $params['fanLiNo'] != '') {
            $query->where('oil_fanli_rule_cal.no', '=', $params['fanLiNo']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('oil_fanli_rule_cal.status', '=', $params['status']);
        }

        //Search By audit_status
        if (isset($params['audit_status']) && $params['audit_status'] != '') {
            $query->where('oil_fanli_rule_cal.audit_status', '=', $params['audit_status']);
        }

        //Search By fanli_moneyGt
        if (isset($params['fanli_moneyGt']) && $params['fanli_moneyGt'] != '') {
            $query->where('oil_fanli_rule_cal_details.fanli_money', '>', $params['fanli_moneyGt']);
        }

        //Search By fanli_jifenGt
        if (isset($params['fanli_jifenGt']) && $params['fanli_jifenGt'] != '') {
            $query->where('oil_fanli_rule_cal_details.fanli_jifen', '>', $params['fanli_jifenGt']);
        }

        return $query;
    }

    /**
     * 油卡返利政策 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $sqlObj = self::Filter($params)
            ->leftJoin('oil_fanli_rule_details','oil_fanli_rule.id','=','oil_fanli_rule_details.rule_id')
            ->select('oil_fanli_rule.rule_name','oil_fanli_rule.fanli_way','oil_fanli_rule.fanli_type',
                'oil_fanli_rule.fanli_coe','oil_fanli_rule.start_time','oil_fanli_rule.end_time','oil_fanli_rule.step_fanli_data',"oil_fanli_rule.oil_amount_limit","oil_fanli_rule.oil_money_limit",
                'oil_fanli_rule.fanli_money','oil_fanli_rule.coe_unit','oil_fanli_rule.add_fanli_edu','oil_fanli_rule_details.*')
            ->orderBy('oil_fanli_rule.level', 'desc');

        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } elseif (isset($params['count']) && $params['count'] == 1) {
            return $sqlObj->count();
        } elseif (isset($params['take']) && isset($params['skip'])) {
            $data = $sqlObj->skip($params['skip'])->take($params['take'])->get();
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * 油卡返利政策 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return self::find($params['id']);
    }

    /**
     * 油卡返利政策 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return self::create($params);
    }

    /**
     * 油卡返利政策 新增
     * @param array $params
     * @return mixed
     */
    static public function batchAdd(array $params)
    {
        return self::insert($params);
    }

    /**
     * 油卡返利政策 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return self::find($params['id'])->update($params);
    }

    /**
     * 油卡返利政策 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return self::destroy($params['ids']);
    }

    /**
     * 油卡返利政策 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function getOneField(array $params,$field="main_id")
    {
        return self::Filter($params)->pluck($field);
    }

    /**
     * 组装现金返利推送记录
     * @param array $params
     * @return int
     */
    static public function getPushCashData($params,$field)
    {
        Capsule::connection()->enableQueryLog();
        $data = self::Filter($params)
            ->leftJoin('oil_fanli_rule_cal','oil_fanli_rule_cal.id','=','oil_fanli_rule_cal_details.cal_id')
            ->leftJoin('oil_account_money','oil_account_money.org_id','=','oil_fanli_rule_cal_details.org_id')
            ->select(Capsule::Raw($field))
            ->groupBy("oil_fanli_rule_cal_details.org_id")
            ->get();
        $ss = Capsule::connection()->getQueryLog();
        //print_r($ss);exit;
        //Log::error("pushSql-Cash:".json_encode($ss),[],"fanliTask_");
        return $data;
    }

    /**
     * 组装积分返利推送记录
     * @param array $params
     * @return int
     */
    static public function getPushJifenData($fanLiNo)
    {

        $sql = 'select oil_account_jifen.id as account_id,sum(oil_fanli_rule_cal_details.fanli_jifen) as amount,oil_fanli_rule_cal_details.main_id,oil_fanli_rule_cal_details.org_id,oil_account_jifen.subAccountID as g7pay_account_id,"POINTS" as type,oil_fanli_rule_cal.no as fanli_no from oil_fanli_rule_cal_details left join `oil_fanli_rule_cal` ON `oil_fanli_rule_cal`.`id` = `oil_fanli_rule_cal_details`.`cal_id` left join `oil_account_jifen` ON `oil_account_jifen`.`org_id` = `oil_fanli_rule_cal_details`.`org_id` and oil_account_jifen.main_id = oil_fanli_rule_cal_details.main_id where oil_fanli_rule_cal.no = "'.$fanLiNo.'" and oil_fanli_rule_cal_details.fanli_jifen  > 0 and oil_fanli_rule_cal.audit_status = 1 group by oil_fanli_rule_cal_details.main_id,oil_fanli_rule_cal_details.org_id';

        //Log::error("pushSql-Jifen:".json_encode($sql),[],"fanliTask_");

        return Capsule::connection()->select($sql);
    }

    /**
     * @title 获取返利详细数据
     * <AUTHOR>
     * @return string
     */
    static public function getDetailList(array $params,$isPack=true)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $sqlObj = self::Filter($params);

        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } elseif (isset($params['count']) && $params['count'] == 1) {
            return $sqlObj->count();
        } elseif (isset($params['take']) && isset($params['skip'])) {
            $data = $sqlObj->skip($params['skip'])->take($params['take'])->get();
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        if(!$isPack){
            return $data;
        }

        $main_no = OilCardMain::getAllMainNo();
        //$provider = OilCardSupplyer::getSupplyerMap([]);
        $region = OilProvinces::getProvince();
        //$opreator = OilStationOperators::getOperatorMap([],"id","operators_name");

        foreach ($data as &$val){
            if(!empty($val->org_id) && $val->org_id > 0) {
                $orgInfo = OilOrg::getById(['id' => $val->org_id]);
                $orgname = $orgInfo->orgcode." ".$orgInfo->org_name;
            }else{
                $orgname = "全部";
            }
            if($val->main_id == -1){
                $val->use_main = '全部';
            }else{
                $val->use_main = $main_no[$val->main_id];
            }
            if($val->oil_type == -1){
                $oil_type = "全部";
            }else{
                $oil_type = $val->oil_type ? OilType::getById($val->oil_type) : '-';
            }
            if($val->oil_com == -1){
                $oil_com = "全部";
            }else{
                $oil_com = $val->oil_com ? OilCom::getById($val->oil_com)['name']  : "-";
            }
            if($val->provice_id == -1){
                $pro_txt = "全部";
            }else{
                $pro_txt = $val->provice_id ? $region[$val->provice_id] : '-';
            }
            $val->use_org = $orgname;
            $val->use_oil_com = $oil_com;
            $val->use_oil_type = $oil_type;
            $val->use_region = $pro_txt;
            $val->fanli_jifen = number_format($val->fanli_jifen,2,".","");
            $val->fanli_money = number_format($val->fanli_money,2,".","");
        }

        return $data;

    }

    /**
     * 处理返利详情数据
     * @param array $params
     * @return int
     */
    static public function packCalDetail(array $calDetail,$cal_id)
    {
        $money_arr = array();
        foreach ($calDetail as $oil_com => $val) {
            foreach ($val as $org_id => $val1) {
                foreach ($val1 as $oil_type => $val2) {
                    foreach ($val2 as $region => $val3) {
                        foreach ($val3 as $main_id => $val4) {
                            $item['cal_id'] = $cal_id;
                            $item['oil_com'] = $oil_com ? $oil_com : NULL;
                            $item['provice_id'] = $region ? $region : NULL;
                            $item['org_id'] = $org_id;
                            $item['oil_type'] = $oil_type ? $oil_type : NULL;
                            $item['main_id'] = $main_id ? $main_id : NULL;
                            $item['createtime'] = \helper::nowTime();
                            $item['updatetime'] = \helper::nowTime();

                            $item['fanli_jifen'] = $val4['fanli_jifen'] ? $val4['fanli_jifen'] : 0 ;
                            $item['fanli_money'] = $val4['fanli_money'] ? $val4['fanli_money'] : 0;
                            /*if ($val4['fanli_jifen'] > 0) {
                                $item['fanli_jifen'] = $val4['fanli_jifen'];
                                $jifen_arr[] = $item;
                            }
                            if ($val4['fanli_money'] > 0) {
                                $item['fanli_money'] = $val4['fanli_money'];
                                $money_arr[] = $item;
                            }*/
                            $money_arr[] = $item;
                        }
                    }
                }
            }
        }
        return $money_arr;
    }
}