<?php
/**
 * gsp异常事件监控表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:16
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class GspErrorMonitor extends \Framework\Database\Model
{
    protected $table = 'gsp_error_monitor';

    protected $guarded = ["id"];

    protected $fillable = ['project','module','info','content','mobiles','is_sent','is_done','createtime','updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By project
        if (isset($params['project']) && $params['project'] != '') {
            $query->where('project', '=', $params['project']);
        }

        //Search By module
        if (isset($params['module']) && $params['module'] != '') {
            $query->where('module', '=', $params['module']);
        }

        //Search By info
        if (isset($params['info']) && $params['info'] != '') {
            $query->where('info', '=', $params['info']);
        }

        //Search By content
        if (isset($params['content']) && $params['content'] != '') {
            $query->where('content', '=', $params['content']);
        }

        //Search By mobiles
        if (isset($params['mobiles']) && $params['mobiles'] != '') {
            $query->where('mobiles', '=', $params['mobiles']);
        }

        //Search By is_sent
        if (isset($params['is_sent']) && $params['is_sent'] != '') {
            $query->where('is_sent', '=', $params['is_sent']);
        }

        //Search By is_done
        if (isset($params['is_done']) && $params['is_done'] != '') {
            $query->where('is_done', '=', $params['is_done']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }


        return $query;
    }

    /**
     * gsp异常事件监控表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = GspErrorMonitor::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * gsp异常事件监控表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspErrorMonitor::find($params['id']);
    }

    /**
     * gsp异常事件监控表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return GspErrorMonitor::create($params);
    }

    /**
     * gsp异常事件监控表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspErrorMonitor::find($params['id'])->update($params);
    }

    /**
     * gsp异常事件监控表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return GspErrorMonitor::destroy($params['ids']);
    }




}