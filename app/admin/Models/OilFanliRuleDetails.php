<?php
/**
 * 油卡返利政策
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:18
 */

namespace Models;

use Framework\Log;
use Fuel\Defines\CoeUnit;
use Fuel\Defines\OilCom;
use Fuel\Defines\OilType;
use Illuminate\Database\Capsule\Manager as Capsule;
use Fuel\Response;

class OilFanliRuleDetails extends \Framework\Database\Model
{
    protected $table = 'oil_fanli_rule_details';

    protected $guarded = ["id"];

    protected $fillable = ['rule_id','oil_com', 'oil_vice_provider', 'main_id', 'region_id', 'fanli_min_money', 'station_opreater_id','station_id','oil_type','is_del','createtime', 'updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('oil_fanli_rule.id', '=', $params['id']);
        }

        //Search By ids
        if (isset($params['ids']) && $params['ids']) {
            $query->whereIn('oil_fanli_rule.id', $params['ids']);
        }

        //Search By rule_id
        if (isset($params['rule_id']) && $params['rule_id']) {
            $query->where('oil_fanli_rule_details.rule_id', $params['rule_id']);
        }

        //Search By rule_id
        if (isset($params['rule_idIn']) && $params['rule_idIn']) {
            $query->whereIn('oil_fanli_rule_details.rule_id', $params['rule_idIn']);
        }

        //Search By rule_object
        if (isset($params['rule_object']) && $params['rule_object'] != '') {
            $query->where('oil_fanli_rule.rule_object', '=', $params['rule_object']);
        }

        //Search By start_time
        if (isset($params['expire_time']) && $params['expire_time'] != '') {
            $query->where('oil_fanli_rule.start_time', '<=', $params['expire_time']);
        }

        //Search By end_time
        if (isset($params['expire_time']) && $params['expire_time'] != '') {
            $query->where('oil_fanli_rule.end_time', '>=', $params['expire_time']);
        }

        //Search By is_del
        if (isset($params['is_del']) && $params['is_del'] != '') {
            $query->where('oil_fanli_rule.is_del', '=', $params['is_del']);
        }

        return $query;
    }

    /**
     * 油卡返利政策 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $sqlObj = self::Filter($params)
            ->leftJoin('oil_fanli_rule','oil_fanli_rule.id','=','oil_fanli_rule_details.rule_id')
            ->select('oil_fanli_rule.rule_name','oil_fanli_rule.fanli_way','oil_fanli_rule.fanli_type',
                'oil_fanli_rule.fanli_coe','oil_fanli_rule.start_time','oil_fanli_rule.end_time','oil_fanli_rule.step_fanli_data',"oil_fanli_rule.oil_amount_limit","oil_fanli_rule.oil_money_limit",
                'oil_fanli_rule.fanli_money','oil_fanli_rule.coe_unit','oil_fanli_rule.add_fanli_edu','oil_fanli_rule_details.*')
            ->orderBy('oil_fanli_rule.level', 'desc');

        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } elseif (isset($params['count']) && $params['count'] == 1) {
            return $sqlObj->count();
        } elseif (isset($params['take']) && isset($params['skip'])) {
            $data = $sqlObj->skip($params['skip'])->take($params['take'])->get();
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * 油卡返利政策 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return self::find($params['id']);
    }

    /**
     * 油卡返利政策 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        global $app;
        return self::create($params);
    }

    /**
     * 油卡返利政策 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return self::find($params['id'])->update($params);
    }

    /**
     * 油卡返利政策 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return self::destroy($params['ids']);
    }

    /**
     * 油卡返利政策 根据rule_id批量删除
     * @param array $params
     * @return int
     */
    static public function batchDel($rule_id,$upData)
    {
        return self::where("rule_id",$rule_id)->update($upData);
    }

    /**
     * @title 生成返利单号
     * <AUTHOR>
     * @return string
     */
    static public function createOrderNo()
    {
        $order_no = 'FL'.date('ymd');
        $fanli_no   = self::where('fanli_no','like',$order_no.'%')->max('fanli_no');
        if($fanli_no){
            $order_no .= sprintf("%04d",(substr($fanli_no,-4) + 1));
        }else{
            $order_no .= '0001';
        }

        return $order_no;
    }

    /**
     * @title 生成返利单号
     * <AUTHOR>
     * @return string
     */
    static public function getLastNo()
    {
        return self::max('rule_no');
    }

    /**
     * @title 获取返利详细数据
     * <AUTHOR>
     * @return string
     */
    static public function getDetailList(array $params)
    {
        $sqlObj = self::Filter($params)
            ->leftJoin('oil_fanli_rule','oil_fanli_rule.id','=','oil_fanli_rule_details.rule_id')
            ->where("oil_fanli_rule_details.is_del",0)
            ->where("oil_fanli_rule.is_del",0)
            ->select("oil_fanli_rule_details.*","oil_fanli_rule.add_fanli_edu","oil_fanli_rule.fanli_coe","oil_fanli_rule.fanli_money","oil_fanli_rule.fanli_type","oil_fanli_rule.coe_unit","oil_fanli_rule.add_fanli_edu","oil_fanli_rule.step_fanli_data")
            ->orderBy('oil_fanli_rule.level', 'desc');

        if (isset($params['_export']) && $params['_export'] == 1) {
            $result = $sqlObj->get();
        } elseif (isset($params['count']) && $params['count'] == 1) {
            return $sqlObj->count();
        } elseif (isset($params['take']) && isset($params['skip'])) {
            $result = $sqlObj->skip($params['skip'])->take($params['take'])->get();
        } else {
            $result = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        $main_no = OilCardMain::getAllMainNo();
        $provider = OilCardSupplyer::getSupplyerMap([]);
        $region = OilProvinces::getProvince();
        $opreator = OilStationOperators::getOperatorMap([],"id","operators_name");
        $station = OilStation::getStationInfo(["is_del"=>0],"station_name","id");
        //$station = array();

        foreach ($result as &$val){
            $val->use_main = $val->main_id == -1 ? "全部" : $main_no[$val->main_id];
            $val->use_org = OilFanliRule::getOrgList(["rule_id"=>$val->id]);
            $val->use_oil_com = $val->oil_com == -1 ? "全部" : OilCom::getById($val->oil_com)['name'];
            $val->use_provider = $val->oil_vice_provider == -1 ? "全部" : $provider[$val->oil_vice_provider];
            $val->use_oil_type = $val->oil_type == -1 ? "全部" : OilType::getById($val->oil_type);
            $val->use_opreater = $val->station_opreater_id == -1 ? "全部" : $opreator[$val->station_opreater_id];
            $val->use_region = $val->region_id == -1 ? "全部" : $region[$val->region_id];
            $val->use_station = $val->station_id == -1 ? "全部" : $station[$val->station_id];
            $val->add_fanli_txt = $val->add_fanli_edu == NULL ? "无" : $val->add_fanli_edu;

            switch($val->fanli_type){
                case 1:
                    $val->use_unit = $val->fanli_coe;
                    $val->calType = "按消费金额比例";
                    break;
                case 2:
                    $val->use_unit = $val->fanli_money;
                    $val->calType = "按消费数量升返";
                    break;
                case 3:case 4:
                    $val->use_unit = $val->add_fanli_edu;
                    if($val->coe_unit == 1){
                        $val->calType = "按累消金额阶梯比例";
                    }elseif($val->coe_unit == 2){
                        $val->calType = "按累消数量阶梯比例";
                    }else{
                        $val->calType = "";
                    }
                    break;
                default:
                    $val->use_unit = "无";
                    $val->calType = "";
            }
            $val->fanli_min_money = $val->fanli_min_money == NULL ? "-" : $val->fanli_min_money;
        }
        return $result;
    }

}