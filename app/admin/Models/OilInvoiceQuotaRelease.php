<?php
/**
 * 柴油专用卡开票额度释放表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/04/18
 * Time: 10:50:22
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilInvoiceQuotaRelease extends \Framework\Database\Model
{
    protected $table = 'oil_invoice_quota_release';

    protected $guarded = ["id"];
    protected $fillable = ['trade_id','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By trade_id
        if (isset($params['trade_id']) && $params['trade_id'] != '') {
            $query->where('trade_id', '=', $params['trade_id']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 柴油专用卡开票额度释放表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilInvoiceQuotaRelease::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 柴油专用卡开票额度释放表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilInvoiceQuotaRelease::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilInvoiceQuotaRelease::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 柴油专用卡开票额度释放表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilInvoiceQuotaRelease::create($params);
    }

    /**
     * 柴油专用卡开票额度释放表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilInvoiceQuotaRelease::find($params['id'])->update($params);
    }

    /**
     * 柴油专用卡开票额度释放表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilInvoiceQuotaRelease::destroy($params['ids']);
    }




}