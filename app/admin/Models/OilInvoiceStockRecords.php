<?php
/**
 * 发票库存流水
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/03/27
 * Time: 15:42:26
 */
namespace Models;
use Framework\Helper;
use Fuel\Defines\OilType;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilInvoiceStockRecords extends \Framework\Database\Model
{
    protected $table = 'oil_invoice_stock_records';
    public $incrementing = FALSE;

    protected $guarded = ['id'];
    protected $fillable = ['id','invoice_stock_id','category','res_type','res_id','summary',
        'change_quantity','after_quantity','remark','createtime','updatetime'];


    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        //Search By id
        if (isset($params['id']) && $params['id']) {
            $query->where('id', '=', $params['id']);
        }

        //Search By invoice_stock_id
        if (isset($params['invoice_stock_id']) && $params['invoice_stock_id']) {
            $query->where('invoice_stock_id', '=', $params['invoice_stock_id']);
        }

        if (isset($params['category']) && $params['category']) {
            $query->where('category', '=', $params['category']);
        }

        if (isset($params['res_type']) && $params['res_type']) {
            $query->where('res_type', '=', $params['res_type']);
        }

        if (isset($params['res_id']) && $params['res_id']) {
            $query->where('res_id', '=', $params['res_id']);
        }


        //Search By change_quantity
        if (isset($params['change_quantity']) && $params['change_quantity'] != '') {
            $query->where('change_quantity', '=', $params['change_quantity']);
        }

        //Search By after_quantity
        if (isset($params['after_quantity']) && $params['after_quantity'] != '') {
            $query->where('after_quantity', '=', $params['after_quantity']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('createtime', '>=', $params['createtimeGe']);
        }

        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $query->where('createtime', '<=', $params['createtimeLe']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 发票库存流水 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilInvoiceStockRecords::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('id', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        foreach ($data as &$_item){
            $_item->change_quantity = number_format($_item->change_quantity,6,".","");
            $_item->after_quantity = number_format($_item->after_quantity,6,".","");
            $_item->before_quantity = number_format(bcsub($_item->after_quantity, $_item->change_quantity,10),6,".","");
            $_item->category_txt = OilType::$stock_classify[$_item->category];
            if($_item->category == OilType::STOCK_CLASSIFY_INTO) {
                $_item->res_type_txt = $_item->res_type == 10 ? "入库" : "撤销";
            }else{
                $_item->res_type_txt = $_item->res_type == 10 ? "蓝票" : "红票";
            }
            $_item->init_quantity = number_format($_item->init_quantity,6,".","");
            $_item->init_time = empty($_item->init_time) ? "" : $_item->init_time;
        }

        return $data;
    }

    /**
     * 发票库存流水 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilInvoiceStockRecords::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilInvoiceStockRecords::lockForUpdate()->where('id',$params['id'])->first();
    }

    static public function getInfoByFilter(array $params,$isLock = false)
    {
        $obj = OilInvoiceStockRecords::Filter($params);
        if($isLock){
            $obj = $obj->lockForUpdate();
        }
        return $obj->first();
    }

    /**
     * 发票库存流水 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        //$params['id'] = Helper::uuid();

        return OilInvoiceStockRecords::create($params);
    }

    /**
     * 发票库存流水 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilInvoiceStockRecords::find($params['id'])->update($params);
    }

    /**
     * 发票库存流水 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilInvoiceStockRecords::destroy($params['ids']);
    }




}