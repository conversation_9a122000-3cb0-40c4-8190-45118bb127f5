<?php
/**
 * 发票核验
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/03/19
 * Time: 18:16:39
 */

namespace Models;

use Framework\Helper;
use Framework\Log;
use Fuel\Defines\VerifyInvoiceStatus;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilVerifyInvoice extends \Framework\Database\Model
{
    protected $table = 'oil_verify_invoice';

    public $incrementing = FALSE;

    protected $guarded = ['id'];
    protected $fillable = ['id', 'invoiceCode', 'invoiceNumber', 'billingDate', 'totalAmount', 'invoiceType',
        'administrativeDivisionNo', 'administrativeDivisionName', 'purchaserName', 'purchaserTaxNo', 'purchaserAddressPhone',
        'purchaserBank', 'salesName', 'salesTaxNo', 'salesAddressPhone', 'salesBank', 'totalTax', 'amountTax', 'amountTaxCn',
        'remarks', 'machineCode', 'checkCode', 'state', 'operator', 'status', 'data', 'message', 'createtime', 'fetchtime', 'updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function VerifyInvoiceDetails()
    {
        return $this->hasMany('Models\\OilVerifyInvoiceDetails', 'verify_invoice_id', 'id');
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('oil_verify_invoice.id', '=', $params['id']);
        }

        //Search By ids
        if (isset($params['ids']) && $params['ids'] != '') {
            $ids = is_array($params['ids']) ? $params['ids'] : explode(',', $params['ids']);
            $query->whereIn('oil_verify_invoice.id', $ids);
        }

        //Search By invoiceCode
        if (isset($params['invoiceCode']) && $params['invoiceCode'] != '') {
            $query->where('oil_verify_invoice.invoiceCode', '=', $params['invoiceCode']);
        }

        //Search By invoiceCodeIn
        if (isset($params['invoiceCodeIn']) && $params['invoiceCodeIn']) {
            $invoiceCodeIn = is_array($params['invoiceCodeIn']) ? $params['invoiceCodeIn'] : explode('|', $params['invoiceCodeIn']);
            $query->whereIn('oil_verify_invoice.invoiceCode', $invoiceCodeIn);
        }

        //Search By invoiceNumber
        if (isset($params['invoiceNumberIn']) && $params['invoiceNumberIn']) {
            $invoiceNumberIn = is_array($params['invoiceNumberIn']) ? $params['invoiceNumberIn'] : explode('|', $params['invoiceNumberIn']);
            $query->whereIn('oil_verify_invoice.invoiceNumber', $invoiceNumberIn);
        }

        //Search By invoiceNumber
        if (isset($params['invoiceNumber']) && $params['invoiceNumber'] != '') {
            $query->where('oil_verify_invoice.invoiceNumber', '=', $params['invoiceNumber']);
        }

        //Search By billingDate
        if (isset($params['billingDate']) && $params['billingDate'] != '') {
            $query->where('oil_verify_invoice.billingDate', '=', $params['billingDate']);
        }

        //Search By billingDateGe
        if (isset($params['billingDateGe']) && $params['billingDateGe'] != '') {
            $query->where('oil_verify_invoice.billingDate', '>=', $params['billingDateGe']);
        }

        //Search By billingDateLe
        if (isset($params['billingDateLe']) && $params['billingDateLe'] != '') {
            $query->where('oil_verify_invoice.billingDate', '<=', $params['billingDateLe']);
        }

        //Search By totalAmount
        if (isset($params['totalAmount']) && $params['totalAmount'] != '') {
            $query->where('oil_verify_invoice.totalAmount', '=', $params['totalAmount']);
        }

        //Search By invoiceType
        if (isset($params['invoiceType']) && $params['invoiceType'] != '') {
            $query->where('oil_verify_invoice.invoiceType', '=', $params['invoiceType']);
        }

        //Search By administrativeDivisionNo
        if (isset($params['administrativeDivisionNo']) && $params['administrativeDivisionNo'] != '') {
            $query->where('oil_verify_invoice.administrativeDivisionNo', '=', $params['administrativeDivisionNo']);
        }

        //Search By administrativeDivisionName
        if (isset($params['administrativeDivisionName']) && $params['administrativeDivisionName'] != '') {
            $query->where('oil_verify_invoice.administrativeDivisionName', '=', $params['administrativeDivisionName']);
        }

        //Search By purchaserName
        if (isset($params['purchaserName']) && $params['purchaserName'] != '') {
            $query->where('oil_verify_invoice.purchaserName', '=', $params['purchaserName']);
        }

        //Search By purchaserNameLike
        if (isset($params['purchaserNameLike']) && $params['purchaserNameLike'] != '') {
            $query->where('oil_verify_invoice.purchaserName', 'like', '%'.$params['purchaserNameLike'].'%');
        }

        //Search By purchaserTaxNo
        if (isset($params['purchaserTaxNo']) && $params['purchaserTaxNo'] != '') {
            $query->where('oil_verify_invoice.purchaserTaxNo', '=', $params['purchaserTaxNo']);
        }

        //Search By purchaserAddressPhone
        if (isset($params['purchaserAddressPhone']) && $params['purchaserAddressPhone'] != '') {
            $query->where('oil_verify_invoice.purchaserAddressPhone', '=', $params['purchaserAddressPhone']);
        }

        //Search By purchaserBank
        if (isset($params['purchaserBank']) && $params['purchaserBank'] != '') {
            $query->where('oil_verify_invoice.purchaserBank', '=', $params['purchaserBank']);
        }

        //Search By salesName
        if (isset($params['salesName']) && $params['salesName'] != '') {
            $query->where('oil_verify_invoice.salesName', '=', $params['salesName']);
        }

        //Search By salesTaxNo
        if (isset($params['salesTaxNo']) && $params['salesTaxNo'] != '') {
            $query->where('oil_verify_invoice.salesTaxNo', '=', $params['salesTaxNo']);
        }

        //Search By salesAddressPhone
        if (isset($params['salesAddressPhone']) && $params['salesAddressPhone'] != '') {
            $query->where('oil_verify_invoice.salesAddressPhone', '=', $params['salesAddressPhone']);
        }

        //Search By salesBank
        if (isset($params['salesBank']) && $params['salesBank'] != '') {
            $query->where('oil_verify_invoice.salesBank', '=', $params['salesBank']);
        }

        //Search By totalTax
        if (isset($params['totalTax']) && $params['totalTax'] != '') {
            $query->where('oil_verify_invoice.totalTax', '=', $params['totalTax']);
        }

        //Search By amountTax
        if (isset($params['amountTax']) && $params['amountTax'] != '') {
            $query->where('oil_verify_invoice.amountTax', '=', $params['amountTax']);
        }

        //Search By amountTaxCn
        if (isset($params['amountTaxCn']) && $params['amountTaxCn'] != '') {
            $query->where('oil_verify_invoice.amountTaxCn', '=', $params['amountTaxCn']);
        }

        //Search By remarks
        if (isset($params['remarks']) && $params['remarks'] != '') {
            $query->where('oil_verify_invoice.remarks', '=', $params['remarks']);
        }

        //Search By machineCode
        if (isset($params['machineCode']) && $params['machineCode'] != '') {
            $query->where('oil_verify_invoice.machineCode', '=', $params['machineCode']);
        }

        //Search By checkCode
        if (isset($params['checkCode']) && $params['checkCode'] != '') {
            $query->where('oil_verify_invoice.checkCode', '=', $params['checkCode']);
        }

        //Search By state
        if (isset($params['state']) && $params['state'] != '') {
            $query->where('oil_verify_invoice.state', '=', $params['state']);
        }

        //Search By operator
        if (isset($params['operator']) && $params['operator'] != '') {
            $query->where('oil_verify_invoice.operator', '=', $params['operator']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('oil_verify_invoice.status', '=', $params['status']);
        }

        //Search By statusIn
        if (isset($params['statusIn']) && $params['statusIn']) {
            $query->whereIn('oil_verify_invoice.status', $params['statusIn']);
        }

        //Search By statusNq
        if (isset($params['statusNq']) && $params['statusNq'] != '') {
            $query->where('oil_verify_invoice.status', '!=', $params['statusNq']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('oil_verify_invoice.createtime', '=', $params['createtime']);
        }

        //Search By fetchtime
        if (isset($params['fetchtime']) && $params['fetchtime'] != '') {
            $query->where('oil_verify_invoice.fetchtime', '=', $params['fetchtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('oil_verify_invoice.updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 发票核验 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

//        Capsule::connection()->enableQueryLog();
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilVerifyInvoice::Filter($params)->orderBy('billingDate', 'desc')->orderBy('invoiceNumber','asc');
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj
                ->select('id', 'invoiceCode', 'invoiceNumber', 'billingDate', 'totalAmount', 'invoiceType',
                    'administrativeDivisionNo', 'administrativeDivisionName', 'purchaserName', 'purchaserTaxNo', 'purchaserAddressPhone',
                    'purchaserBank', 'salesName', 'salesTaxNo', 'salesAddressPhone', 'salesBank', 'totalTax', 'amountTax', 'amountTaxCn',
                    'remarks', 'machineCode', 'checkCode', 'state', 'operator', 'status', 'message', 'createtime', 'fetchtime', 'updatetime')
                ->with(
                    [
                        'VerifyInvoiceDetails'
                    ]
                )
                ->get();
        } elseif (isset($params['skip'])) {
            $data = $sqlObj
                ->select('id', 'invoiceCode', 'invoiceNumber', 'billingDate', 'totalAmount', 'invoiceType',
                    'administrativeDivisionNo', 'administrativeDivisionName', 'purchaserName', 'purchaserTaxNo', 'purchaserAddressPhone',
                    'purchaserBank', 'salesName', 'salesTaxNo', 'salesAddressPhone', 'salesBank', 'totalTax', 'amountTax', 'amountTaxCn',
                    'remarks', 'machineCode', 'checkCode', 'state', 'operator', 'status', 'message', 'createtime', 'fetchtime', 'updatetime')
                ->with(
                    [
                        'VerifyInvoiceDetails'
                    ]
                )
                ->skip(intval($params['skip']))
                ->take(intval($params['take']))
                ->get();
        } else {
            $data = $sqlObj->select('id', 'invoiceCode', 'invoiceNumber', 'billingDate', 'totalAmount', 'invoiceType',
                'administrativeDivisionNo', 'administrativeDivisionName', 'purchaserName', 'purchaserTaxNo', 'purchaserAddressPhone',
                'purchaserBank', 'salesName', 'salesTaxNo', 'salesAddressPhone', 'salesBank', 'totalTax', 'amountTax', 'amountTaxCn',
                'remarks', 'machineCode', 'checkCode', 'state', 'operator', 'status', 'message', 'createtime', 'fetchtime', 'updatetime')
                ->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

//        Log::info('sql--'.var_export(Capsule::connection()->getQueryLog(),true),[],'zzg');
        if ($data) {
            foreach ($data as &$v) {
                $v->_status = $v->status;
                $v->status = VerifyInvoiceStatus::getById($v->status);
            }
        }

        return $data;
    }

    /**
     * 发票核验 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilVerifyInvoice::find($params['id']);
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilVerifyInvoice::lockForUpdate()->where('id', $params['id'])->first();
    }

    /**
     * 发票核验 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        $params['id'] = Helper::uuid();

        return OilVerifyInvoice::create($params);
    }

    /**
     * 发票核验 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilVerifyInvoice::find($params['id'])->update($params);
    }

    /**
     * 发票核验 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilVerifyInvoice::destroy($params['ids']);
    }

    static public function getTask(array $params)
    {
        \helper::argumentCheck(['statusIn'], $params);

        $sqlObj = OilVerifyInvoice::Filter($params)
            ->orderBy('createtime', 'asc');

        if(isset($params['take'])){
            $sqlObj->take(intval($params['take']));
        }

        return $sqlObj->get();
    }

    static public function deleteByIds(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return self::whereIn('status',[VerifyInvoiceStatus::FAILED,VerifyInvoiceStatus::PRE_DO])
            ->whereIn('ids',$params['ids'])
            ->delete();
    }
}