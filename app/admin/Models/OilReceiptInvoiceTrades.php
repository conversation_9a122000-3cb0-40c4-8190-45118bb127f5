<?php
/**
 * 红冲交易记录For客户换签
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2023/04/23
 * Time: 14:13:34
 */
namespace Models;
use Framework\Log;
use Fuel\Defines\CardTradeConf;
use Fuel\Defines\OilType;
use Illuminate\Database\Capsule\Manager as Capsule;


class OilReceiptInvoiceTrades extends \Framework\Database\Model
{
    protected $table = 'oil_receipt_invoice_trades';

    protected $guarded = ["id"];
    protected $fillable = ['trades_id','data_from','receipt_num','receipt_money','receipt_fanli','oil_type','oil_sec_type',
        'is_need_sale_relation','is_sale_relation','relation_receipt_no','relation_sale_no','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('oil_receipt_invoice_trades.id', '=', $params['id']);
        }

        if (isset($params['idNIn']) && count($params['idNIn']) > 0 ) {
            $query->whereNotIn('oil_receipt_invoice_trades.id', $params['idNIn']);
        }

        //Search By trades_id
        if (isset($params['trades_id']) && $params['trades_id'] != '') {
            $query->where('oil_receipt_invoice_trades.trades_id', '=', $params['trades_id']);
        }

        //Search By api_id
        if (isset($params['api_id']) && $params['api_id'] != '') {
            $query->where('oil_card_vice_trades.api_id', '=', $params['api_id']);
        }

        if (isset($params['org_operator_id']) && $params['org_operator_id'] != '') {
            $query->where('oil_card_vice_trades.org_operator_id', '=', $params['org_operator_id']);
        }

        //Search By data_from
        if (isset($params['data_from']) && $params['data_from'] != '') {
            $query->where('oil_receipt_invoice_trades.data_from', '=', $params['data_from']);
        }

        if (isset($params['data_fromIn']) && count($params['data_fromIn']) > 0 ) {
            $query->whereIn('oil_receipt_invoice_trades.data_from', $params['data_fromIn']);
        }

        //Search By receipt_num
        if (isset($params['receipt_num']) && $params['receipt_num'] != '') {
            $query->where('oil_receipt_invoice_trades.receipt_num', '=', $params['receipt_num']);
        }

        //Search By receipt_money
        if (isset($params['receipt_money']) && $params['receipt_money'] != '') {
            $query->where('oil_receipt_invoice_trades.receipt_money', '=', $params['receipt_money']);
        }

        //Search By receipt_fanli
        if (isset($params['receipt_fanli']) && $params['receipt_fanli'] != '') {
            $query->where('oil_receipt_invoice_trades.receipt_fanli', '=', $params['receipt_fanli']);
        }

        //Search By oil_type
        if (isset($params['oil_type']) && $params['oil_type'] != '') {
            $query->where('oil_receipt_invoice_trades.oil_type', '=', $params['oil_type']);
        }

        if (isset($params['oil_typeIn']) && count($params['oil_typeIn']) > 0) {
            $query->whereIn('oil_receipt_invoice_trades.oil_type', $params['oil_typeIn']);
        }

        //Search By oil_sec_type
        if (isset($params['oil_sec_type']) && $params['oil_sec_type'] != '') {
            $query->where('oil_receipt_invoice_trades.oil_sec_type', '=', $params['oil_sec_type']);
        }

        //Search By is_need_sale_relation
        /*if (isset($params['is_need_sale_relation']) && $params['is_need_sale_relation'] != '') {
            $query->where('oil_receipt_invoice_trades.is_need_sale_relation', '=', $params['is_need_sale_relation']);
        }*/

        //Search By is_sale_relation
        if (isset($params['is_sale_relation']) && $params['is_sale_relation'] != '') {
            $query->where('oil_receipt_invoice_trades.is_sale_relation', '=', $params['is_sale_relation']);
        }

        //Search By relation_receipt_no
        if (isset($params['relation_receipt_no']) && $params['relation_receipt_no'] != '') {
            $query->where('oil_receipt_invoice_trades.relation_receipt_no', '=', $params['relation_receipt_no']);
        }

        if (isset($params['relation_receipt_no_null']) && $params['relation_receipt_no_null'] == 1) {

            $query->where(function ($query) use ($params) {
                $query->whereNull('oil_receipt_invoice_trades.relation_receipt_no')
                    ->orWhere("oil_receipt_invoice_trades.relation_receipt_no",'=','');
            });
        }

        if (isset($params['relation_receipt_no_lk']) && $params['relation_receipt_no_lk'] != '') {
            $query->where('oil_receipt_invoice_trades.relation_receipt_no', 'like', "%".$params['relation_receipt_no_lk']."%");
        }

        if (isset($params['relation_receipt_no_nlk']) && $params['relation_receipt_no_nlk'] != '') {
            $query->where('oil_receipt_invoice_trades.relation_receipt_no', 'not like', "%".$params['relation_receipt_no_nlk']."%");
        }


        //Search By relation_sale_no
        if (isset($params['relation_sale_no']) && $params['relation_sale_no'] != '') {
            $query->where('oil_receipt_invoice_trades.relation_sale_no', '=', $params['relation_sale_no']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('oil_receipt_invoice_trades.createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('oil_receipt_invoice_trades.updatetime', '=', $params['updatetime']);
        }

        if (isset($params['orgCodeLike']) && $params['orgCodeLike'] != '' && isset($params['org_flag'])) {
            $query->where('oil_org.orgcode', 'like', $params['orgCodeLike'] . '%');
        }
        if (isset($params['orgCodeLike']) && $params['orgCodeLike'] != '' && !isset($params['org_flag'])) {
            $query->where('oil_org.orgcode', '=', $params['orgCodeLike']);
        }

        return $query;
    }

    /**
     * 红冲交易记录For客户换签 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $field = "oil_card_vice_trades.api_id,oil_card_vice_trades.trade_price,oil_card_vice_trades.trade_money,oil_card_vice_trades.oil_name,
        oil_card_vice_trades.trade_num,oil_card_vice_trades.org_id,oil_card_vice_trades.trade_price,oil_card_vice_trades.org_operator_id,oil_receipt_invoice_trades.*,
        oil_org.orgcode,oil_org.org_name,oil_card_vice_trades.cancel_sn";
        //Capsule::connection()->enableQueryLog();
        $sqlObj = OilReceiptInvoiceTrades::Filter($params)
            ->select(Capsule::connection('')->raw($field))
            ->leftJoin('oil_card_vice_trades','oil_receipt_invoice_trades.trades_id','=','oil_card_vice_trades.id')
            ->leftJoin('oil_org','oil_card_vice_trades.org_id','=','oil_org.id');
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('oil_receipt_invoice_trades.createtime', 'desc')->get();
        }else{
            $data = $sqlObj->orderBy('oil_receipt_invoice_trades.createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;

        $operatorMap = OilOperators::getIdMapName("company_name");
        foreach ($data as &$_val){
            $_val->operator_name = isset($operatorMap[$_val->org_operator_id]) ? $operatorMap[$_val->org_operator_id] : "";
            $_val->from_txt = isset(CardTradeConf::$trade_sale_from[$_val->data_from]) ? CardTradeConf::$trade_sale_from[$_val->data_from] : "";
            //$_val->sale_need = isset(CardTradeConf::$trade_for_sale[$_val->is_need_sale_relation]) ? CardTradeConf::$trade_for_sale[$_val->is_need_sale_relation] : '异常';
            $_val->sale_relation = isset(CardTradeConf::$trade_sale_relation[$_val->is_sale_relation]) ? CardTradeConf::$trade_sale_relation[$_val->is_sale_relation] : '未关联';
            $_val->oil_type_txt = OilType::$oil_type[$_val->oil_type];
            $_val->oil_sec_type_txt = OilType::$oil_sec_type[$_val->oil_sec_type]['title'];
            $_val->org_code = $_val->orgcode;
            $_val->org_root = substr($_val->orgcode,0,6);
            $orgroot = OilOrg::getByCacheOrgcode($_val->org_root);
            $_val->org_root_name = $orgroot->org_name;
            $_val->use_fanli_money = empty($_val->use_fanli_money) ? "0.00" : $_val->use_fanli_money;
            $_val->show_value = $_val->trades_id."：".$_val->trade_money."【".$_val->oil_name."】";
            $_val->original_order_id = "";
            if( $_val->data_from == CardTradeConf::Trade_Sale_From_Refund && !empty($_val->cancel_sn) ){
                $original = OilCardViceTrades::getOneInfo(['cancel_sn'=>$_val->cancel_sn,"trade_money_gt"=>1]);
                //$_val->api_id = $original && isset($original->api_id) ? $original->api_id : "";
                $_val->original_order_id = $original && isset($original->api_id) ? $original->api_id : "";
            }elseif ($_val->data_from == CardTradeConf::Trade_Sale_From_Exception){
                $ext = OilCardViceTradesExt::getOneInfo(['trades_id'=>$_val->trades_id]);
                $_val->original_order_id = $ext && isset($ext->original_order_id) ? $ext->original_order_id : "";
            }
        }

        return $data;
    }

    /**
     * 红冲交易记录For客户换签 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptInvoiceTrades::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptInvoiceTrades::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 红冲交易记录For客户换签 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilReceiptInvoiceTrades::create($params);
    }

    /**
     * 红冲交易记录For客户换签 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptInvoiceTrades::find($params['id'])->update($params);
    }

    static public function editByFilter($condition = [],array $params)
    {
        return OilReceiptInvoiceTrades::Filter($condition)->update($params);
    }

    /**
     * 红冲交易记录For客户换签 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilReceiptInvoiceTrades::destroy($params['ids']);
    }

    static public function getPluckData($params = [],$field = "id")
    {
        Capsule::connection()->enableQueryLog();
        $data = OilReceiptInvoiceTrades::Filter($params)->pluck($field);
        $sql = Capsule::connection()->getQueryLog();
        Log::error("invoice-trades",['sql'=>$sql,'param'=>$params],"invoice_trades");
        if(count($data) > 0){
            return $data->toArray();
        }
        return [];
    }

    static public function getTradesByFilter( $params = [])
    {
        return OilReceiptInvoiceTrades::Filter($params)->first();
    }

    static public function getInfoByFilter(array $params,$isLock = false)
    {
        if($isLock){
            //Capsule::connection()->enableQueryLog();
            $field = "oil_card_vice_trades.api_id,oil_card_vice_trades.trade_price,oil_card_vice_trades.trade_money,oil_card_vice_trades.oil_name,
        oil_card_vice_trades.trade_num,oil_card_vice_trades.org_id,oil_card_vice_trades.trade_price,oil_card_vice_trades.org_operator_id,oil_receipt_invoice_trades.*,
        oil_org.orgcode,oil_org.org_name,oil_card_vice_trades.use_fanli_money";
            $data = OilReceiptInvoiceTrades::lockForUpdate()->Filter($params)
                ->select(Capsule::connection('')->raw($field))
                ->leftJoin('oil_card_vice_trades','oil_receipt_invoice_trades.trades_id','=','oil_card_vice_trades.id')
                ->leftJoin('oil_org','oil_card_vice_trades.org_id','=','oil_org.id')
                ->first();
            //$sql = Capsule::connection()->getQueryLog();
            //print_r($sql);exit;
            return $data;
        }else{
            return OilReceiptInvoiceTrades::Filter($params)->first();
        }
    }
}