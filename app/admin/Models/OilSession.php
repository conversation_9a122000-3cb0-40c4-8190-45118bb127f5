<?php
/**
 * session表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/09/05
 * Time: 12:07:33
 */
namespace Models;
use Framework\Cache;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilSession extends \Framework\Database\Model
{
    protected $table = 'oil_session';

    protected $guarded = ["id"];
    protected $fillable = ['session_id','hash','data','expiretime','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By session_id
        if (isset($params['session_id']) && $params['session_id'] != '') {
            $query->where('session_id', '=', $params['session_id']);
        }

        //Search By hash
        if (isset($params['hash']) && $params['hash'] != '') {
            $query->where('hash', '=', $params['hash']);
        }

        //Search By data
        if (isset($params['data']) && $params['data'] != '') {
            $query->where('data', '=', $params['data']);
        }

        //Search By expiretime
        if (isset($params['expiretime']) && $params['expiretime'] != '') {
            $query->where('expiretime', '=', $params['expiretime']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * session表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilSession::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * session表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSession::find($params['id']);
    }

    /**
     * session表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        $commonModel = new \commonModel();
        $cookieSessionId = $commonModel->cookieUid();

        $info = self::getByHash($cookieSessionId);
        if($info){
            return $info;
        }

        OilSession::where('expiretime','<',date("Y-m-d H:i:s"))->delete();

        return OilSession::create($params);
    }

    /**
     * @param $sessionId
     * @return mixed
     */
    static public function getBySessionId($sessionId)
    {
//        OilSession::where('expiretime','<',date("Y-m-d H:i:s"))->delete();
        $cacheName = __CLASS__.__METHOD__.'getBySessionId'.$sessionId;
        $data = Cache::get($cacheName);
        if(!$data){
            $data = OilSession::where('session_id','=',$sessionId)->where('expiretime','>',date("Y-m-d H:i:s"))
                ->first();
            //Cache::put($cacheName, $data, 86400*7);
            if($data) {
                Cache::put($cacheName, $data, 86400); //1天
            }
        }

        return $data;
    }

    static public function getByHash($hash)
    {
        $cacheName = __CLASS__.__METHOD__.'getByHash'.$hash;
        $data = Cache::get($cacheName);
        if(!$data){
            $data = OilSession::where('hash','=',$hash)->where('expiretime','>',date("Y-m-d H:i:s"))->first();
            Cache::put($cacheName, $data, 86400*7);
        }

        return $data;
    }

    /**
     * session表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSession::find($params['id'])->update($params);
    }

    /**
     * session表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilSession::destroy($params['ids']);
    }




}