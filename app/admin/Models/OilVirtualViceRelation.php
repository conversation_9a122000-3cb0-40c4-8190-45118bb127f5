<?php
/**
 * 中石油虚拟卡对应关系
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/07/31
 * Time: 15:54:01
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilVirtualViceRelation extends \Framework\Database\Model
{
    protected $table = 'oil_virtual_vice_relation';

    protected $guarded = ["id"];
    protected $fillable = ['org_id','cardID','virtual_vice_no','cash_cardSubAccountID','point_cardSubAccountID','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By org_idIn
        if (isset($params['org_idIn']) && $params['org_idIn'] != '') {
            $query->whereIn('org_id', $params['org_idIn']);
        }

        //Search By cardID
        if (isset($params['cardID']) && $params['cardID'] != '') {
            $query->where('cardID', '=', $params['cardID']);
        }

        //Search By virtual_vice_no
        if (isset($params['virtual_vice_no']) && $params['virtual_vice_no'] != '') {
            $query->where('virtual_vice_no', '=', $params['virtual_vice_no']);
        }

        //Search By cash_cardSubAccountID
        if (isset($params['cash_cardSubAccountID']) && $params['cash_cardSubAccountID'] != '') {
            $query->where('cash_cardSubAccountID', '=', $params['cash_cardSubAccountID']);
        }

        //Search By point_cardSubAccountID
        if (isset($params['point_cardSubAccountID']) && $params['point_cardSubAccountID'] != '') {
            $query->where('point_cardSubAccountID', '=', $params['point_cardSubAccountID']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 中石化虚拟卡对应关系 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilVirtualViceRelation::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 中石化虚拟卡对应关系 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilVirtualViceRelation::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilVirtualViceRelation::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 中石化虚拟卡对应关系 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilVirtualViceRelation::create($params);
    }

    /**
     * 中石化虚拟卡对应关系 新增
     * @param array $params
     * @return mixed
     */
    static public function batchAdd(array $params)
    {
        return OilVirtualViceRelation::insert($params);
    }

    /**
     * 中石化虚拟卡对应关系 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilVirtualViceRelation::find($params['id'])->update($params);
    }

    /**
     * 中石化虚拟卡对应关系 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilVirtualViceRelation::destroy($params['ids']);
    }

    /**
     * @title 获取虚拟卡列表
     * @title 获取虚拟卡列表
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param arrat $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getCardByLimit(array $params)
    {
        \helper::argumentCheck(['org_id','cardNum'],$params);

        return OilVirtualViceRelation::where('org_id',$params['org_id'])
            ->orderBy(Capsule::connection()->raw('RAND()'))
            ->limit($params['cardNum'])
            ->get();
    }

    /**
     * 获取关系表中，某个机构的最后id
     * @param array $params
     * @return int
     */
    static public function getLastId()
    {
        return OilVirtualViceRelation::orderby("createtime","desc")->first();
    }

    /**
     * 获取机构已有的中石化的虚拟卡数
     * @param array $params
     * @return int
     */
    static public function getOrgNums($condition)
    {
        return OilVirtualViceRelation::where($condition)->count("id");
    }

    /**
     * 获取机构已有的中石化的虚拟卡数
     * @param array $params
     * @return int
     */
    static public function getAllOrgNums($condition)
    {
        Capsule::connection()->enableQueryLog();
        $data = OilVirtualViceRelation::Filter($condition)->select(Capsule::Raw("count(id) as num,org_id"))->groupBy("org_id")->get();
        $sql = Capsule::connection()->getQueryLog();
        return $data;
    }

}