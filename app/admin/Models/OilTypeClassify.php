<?php
/**
 * 油品种类
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/08/09
 * Time: 16:23:38
 */
namespace Models;
use Fuel\Service\OilTypeService;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilTypeClassify extends \Framework\Database\Model
{
    protected $table = 'oil_type_classify';

    protected $guarded = ["id"];

    protected $fillable = ['name','unit','pid','tax','tax_no','creator','last_operator','createtime','updatetime','status',"tag","mode"];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By name
        if (isset($params['name']) && $params['name'] != '') {
            $query->where('name', '=', $params['name']);
        }

        if (isset($params['nameLk']) && $params['nameLk'] != '') {
            $query->where('name', 'like', "%".$params['nameLk']."%");
        }

        //Search By unit
        if (isset($params['unit']) && $params['unit'] != '') {
            $query->where('unit', '=', $params['unit']);
        }

        //Search By pid
        if (isset($params['pid']) && $params['pid'] != '') {
            $query->where('pid', '=', $params['pid']);
        }

        if (isset($params['pidGt']) && $params['pidGt'] != '') {
            $query->where('pid', '>', 0);
        }

        //Search By creator
        if (isset($params['creator']) && $params['creator'] != '') {
            $query->where('creator', '=', $params['creator']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('createtime', '>=', $params['createtimeGe']);
        }

        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $query->where('createtime', '<=', $params['createtimeLe']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        if (isset($params['updatetimeGe']) && $params['updatetimeGe'] != '') {
            $query->where('updatetime', '>=', $params['updatetimeGe']);
        }

        if (isset($params['updatetimeLe']) && $params['updatetimeLe'] != '') {
            $query->where('updatetime', '<=', $params['updatetimeLe']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        if (isset($params['tax_noLk']) && $params['tax_noLk'] != '') {
            $query->where('tax_no', 'like', "%".$params['tax_noLk']."%");
        }

        if (isset($params['tax_no']) && $params['tax_no'] != '') {
            $query->where('tax_no', '=', $params['tax_no']);
        }

        //Search By id
        if (isset($params['tag']) && $params['tag'] != '') {
            if(is_array($params['tag'])){
                $query->whereIn('tag',$params['tag']);
            }else{
                $query->where('tag', '=', $params['tag']);
            }
        }

        return $query;
    }

    /**
     * 油品种类 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 1000;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        //Capsule::connection()->enableQueryLog();
        if(isset($params['pid']) && $params['pid'] > 0){
            $params['pidGt'] = 10;
            unset($params['pid']);
        }
        $sqlObj = OilTypeClassify::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('id', 'asc')->paginate($params['limit'],['*'],'page',$params['page']);
        }
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        //print_r($data->toArray());exit;
        $menus = [];
        $unitArr = (new OilTypeService())->getOilUnit(true);
        $rootClassify = (new OilTypeService())->getRootClassify();
        foreach ($data as $_item){
            $arr = [];
            if( stripos($_item->unit,",") !== false ){
                $_tmpArr = explode(",",$_item->unit);
                $unitTxt = [];
                foreach ($_tmpArr as $_sub){
                    $_txt = isset($unitArr[$_sub]) ? $unitArr[$_sub] : "";
                    $arr[$_sub] = ["id"=>$_sub,"name"=>$_txt];
                    $unitTxt[] = $_txt;
                }
                $unit_txt = implode(",",$unitTxt);
            }else{
                $unit_txt = isset($unitArr[$_item->unit]) ? $unitArr[$_item->unit] : "";
                $arr[$_item->unit] = ["id"=>$_item->unit,"name"=>$unit_txt];
            }
            $level = $_item->pid > 0 ? "2级" : "1级";

            $oneItem['id'] = $_item->id;
            $oneItem['name'] = $_item->name;
            $oneItem['root_name'] = isset($rootClassify[$_item->tag]) ? $rootClassify[$_item->tag]['key'] : "-";
            $oneItem['level'] = $level;
            $oneItem['unit_txt'] = $unit_txt;
            $oneItem['unit'] = $_item->unit;
            $oneItem['unitArr'] = array_values($arr);
            $oneItem['tax'] = $_item->tax."%";
            $oneItem['tax_no'] = empty($_item->tax_no) ? "--" : $_item->tax_no;
            $oneItem['creator'] = $_item->creator;
            $oneItem['last_operator'] = $_item->last_operator;
            $oneItem['createtime'] = strval($_item->createtime);
            $oneItem['updatetime'] = strval($_item->updatetime);
            $oneItem['children'] = [];

            if ($_item->pid == 0) {
                $menus[$_item->id] = $oneItem;
            } else {
                if (in_array($_item->pid, array_keys($menus))) {
                    $menus[$_item->pid]['children'][] = $oneItem;
                }else{
                    $menus[] = $oneItem;
                }
            }

        }

        return array_values($menus);
    }

    /**
     * 油品种类 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilTypeClassify::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilTypeClassify::lockForUpdate()->where('id',$params['id'])->first();
    }

    static public function getInfoLock(array $params)
    {
        return OilTypeClassify::lockForUpdate()->Filter($params)->first();
    }

    static public function getInfoByFilter(array $params)
    {
        return OilTypeClassify::Filter($params)->first();
    }

    static public function pluckByFilter(array $params)
    {
        return OilTypeClassify::Filter($params)->pluck("id");
    }

    /**
     * 油品种类 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilTypeClassify::create($params);
    }

    /**
     * 油品种类 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilTypeClassify::find($params['id'])->update($params);
    }

    /**
     * 油品种类 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilTypeClassify::destroy($params['ids']);
    }

    static public function getTypeName()
    {
        $list = self::getList(["_export" => 1]);
        $arr = [];
        foreach ($list as $_item) {
            foreach ($_item['children'] as $_sub) {
                $arr[$_sub['id']] = ['name' => $_sub['name'], "unit" => $_sub['unit_txt'],"unitId"=>$_sub['unit'], "id"=>$_sub['id'],"parent_name" => $_item['name']];
            }
        }
        return $arr;
    }
    /**
     * 获取1级油品分类名称
     * @return mixed
     */
    static  public function getAllOneLevelOilTypeName(){

        return self::where('pid','=',0)->pluck('name')->toArray();
    }
    /**
     * 获取2级油品分类名称
     * @param $params
     * @return mixed
     */
    static  public function getAllSecondLevelOilTypeName($params=[]){


        return self::Filter($params)->where('pid','>',0)->pluck('name')->toArray();
    }

    /**
     * 获取1级油品分类
     * @return mixed
     */
    static  public function getAllOneLevelOil(){
        return self::where('pid','=',0)->select('id','pid','name')->get()->toArray();
    }

    /**
     * 获取非1级油品分类
     * @return mixed
     */
    static  public function getAllNotOneLevelOil(){
        return self::where('pid','!=',0)->select('id','pid','name')->get()->toArray();
    }

    /**
     * 条件查询二级油品
     * @param $params
     * @return mixed
     */
    static  public function getLevel2OilByFilter($params){
        return self::Filter($params)->where('pid','!=',0)->select('id','pid','name')->get()->toArray();
    }
    /**
     * @param $params
     * @return mixed
     */
    static public function getInfoSecondLevelOil($params){
        return OilTypeClassify::Filter($params)->where('pid','!=',0)->first();
    }

    /**
     * whereIn查询二级油品
     * @param $params
     * @return mixed
     */
    static public function getLevel2Oil($params){
        return OilTypeClassify::whereIn('id',$params)->where('pid','!=',0)->select('id','name')->get();
    }

    /**
     * 获取某1级下的二级油品
     * @param array $params
     * @return mixed
     */
    static  public function getSecondOilByFilter($params){
        return OilTypeClassify::Filter($params)
            ->leftJoin('oil_configure','oil_type_classify.unit','=','oil_configure.id')
            ->where('pid','!=',0)->select('oil_type_classify.id','oil_type_classify.name','oil_configure.sys_value as in_unit_val')->get()->toArray();
    }
}