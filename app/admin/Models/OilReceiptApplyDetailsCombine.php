<?php

namespace Models;

use Framework\Log;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilReceiptApplyDetailsCombine extends \Framework\Database\Model
{
    protected $table = 'oil_receipt_apply_details_combine';
    
    protected $guarded  = ["id"];
    protected $fillable = ['receipt_apply_id','trades_id_str','trade_num','trade_money','use_fanli_money',
        'receipt_unit','receipt_oil_name','createtime','updatetime'];
    
    public function getFillAble()
    {
        return $this->fillable;
    }
    
    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        if (isset($params['idIn']) && $params['id'] != '') {
            $query->whereIn('id', $params['idIn']);
        }

        if (isset($params['receipt_apply_id']) && $params['receipt_apply_id'] != '') {
            $query->where('receipt_apply_id', '=', $params['receipt_apply_id']);
        }

        if (isset($params['receipt_unit']) && $params['receipt_unit'] != '') {
            $query->where('receipt_unit', '=', $params['receipt_unit']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }
        
        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }
        
        return $query;
    }


    static public function getList(array $params)
    {
        $data = [];
        
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilReceiptApplyDetailsCombine::Filter($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }
        
        return $data;
    }

    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilReceiptApplyDetailsCombine::find($params['id']);
    }
    

    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilReceiptApplyDetailsCombine::lockForUpdate()->where('id', $params['id'])->first();
    }
    

    static public function add(array $params)
    {
        return OilReceiptApplyDetailsCombine::create($params);
    }

    static public function checkNum(array $params)
    {
        return OilReceiptApplyDetailsCombine::Filter($params)->count();
    }

    static public function batchAdd(array $params)
    {
        return OilReceiptApplyDetailsCombine::insert($params);
    }
    

    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilReceiptApplyDetailsCombine::find($params['id'])->update($params);
    }

    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);
        
        return OilReceiptApplyDetailsCombine::destroy($params['ids']);
    }

    static public function groupDetails($receipt_id = [])
    {
        $sql = "select receipt_apply_id,receipt_unit from oil_receipt_apply_details_combine where receipt_apply_id in (".implode(",",$receipt_id).") group by receipt_apply_id,receipt_unit;";
        $data = Capsule::connection()->select($sql);
        $map = [];
        foreach ($data as $_item){
            $map[$_item->receipt_apply_id][] = $_item->receipt_unit;
        }
        return $map;
    }

    static public function getListForSplit($params)
    {
        return OilReceiptApplyDetailsCombine::Filter($params)
            ->selectRaw("id,trades_id_str,trade_num,trade_money,use_fanli_money,(trade_money - use_fanli_money) as receipt_money")
            ->orderBy('trade_money', 'desc')
            ->get()->toArray();
    }

    static public function groupOilName($ids = [])
    {
        $sql = "select 
sum(trade_num) as trade_num,sum(trade_money) as trade_money,sum(use_fanli_money) as fanli,
sum(trade_money - use_fanli_money) as receipt_money,receipt_oil_name 
from oil_receipt_apply_details_combine 
where id in (".implode(",",$ids).") 
group by receipt_oil_name;";
Log::error('groupOilName:', [$sql],'receiptSplitSql_');
        return Capsule::connection()->select($sql);
    }

    static public function groupApplyDetails($receipt_apply_id = 0,$oil_type = "柴油")
    {
        $sql = "SELECT
  (
    CASE
      WHEN `oil_type_no`.receipt_oil_name IS NULL
      OR `oil_type_no`.receipt_oil_name = '' THEN ''
      ELSE `oil_type_no`.receipt_oil_name
    END
  ) AS oil_receipt_name,
  count(
    case
      WHEN oil_trades.trade_num < 0 then 1
    end
  ) as num1,
  count(
    case
      WHEN oil_trades.trade_num >= 0 then 1
    end
  ) as num2,
  oil_supplier_receipt_return_unit.unit,
  group_concat(DISTINCT oil_trades.supplier_id) as supplier_id,
  group_concat(DISTINCT oil_trades.oil_name) as oil_name_str
FROM
  `oil_receipt_apply_details`
  LEFT JOIN `oil_trades` ON `oil_trades`.`trades_id` = `oil_receipt_apply_details`.`trades_id`
  LEFT JOIN `oil_type_no` ON `oil_type_no`.`oil_no` = `oil_trades`.`oil_name`
  LEFT JOIN oil_supplier_receipt_return_unit ON oil_supplier_receipt_return_unit.supplier_id = oil_trades.supplier_id AND oil_supplier_receipt_return_unit.oil_name = '".$oil_type."' 
  WHERE
  `oil_receipt_apply_details`.`receipt_apply_id` = ".$receipt_apply_id."
  AND `oil_trades`.`supplier_id` IS NOT NULL
GROUP BY
  `oil_receipt_name`,
  oil_supplier_receipt_return_unit.unit";
  Log::error('groupApplyDetails:', [$sql],'receiptSplitSql_');
        return Capsule::connection()->select($sql);
    }

    static public function getGroupApplyDetails($receipt_apply_id = 0,$unit= "",$oil_type = "",$oil_name = "",$excep_id = [],$num = "",$flag = 1)
    {
        $sql = "SELECT
        oil_receipt_apply_details.trade_num,oil_receipt_apply_details.trade_money,oil_receipt_apply_details.use_fanli_money,oil_receipt_apply_details.trades_id,oil_supplier_receipt_return_unit.unit,(
    CASE
      WHEN `oil_type_no`.receipt_oil_name IS NULL
      OR `oil_type_no`.receipt_oil_name = '' THEN `oil_type_no`.`oil_no`
      ELSE `oil_type_no`.receipt_oil_name
    END
  ) AS oil_receipt_name
FROM
  `oil_receipt_apply_details`
  LEFT JOIN `oil_trades` ON `oil_trades`.`trades_id` = `oil_receipt_apply_details`.`trades_id`
  LEFT JOIN `oil_type_no` ON `oil_type_no`.`oil_no` = `oil_trades`.`oil_name`
  LEFT JOIN oil_supplier_receipt_return_unit ON oil_supplier_receipt_return_unit.supplier_id = oil_trades.supplier_id AND oil_supplier_receipt_return_unit.oil_name = '".$oil_type."' 
  WHERE
  `oil_receipt_apply_details`.`receipt_apply_id` = ".$receipt_apply_id." AND oil_supplier_receipt_return_unit.unit = '".$unit."' and if(length(`oil_type_no`.receipt_oil_name) > 0,`oil_type_no`.receipt_oil_name,`oil_type_no`.`oil_no`) = '".$oil_name."'";

        if($flag == 1){
            $sql .= " AND oil_receipt_apply_details.trade_num < 0 order by oil_receipt_apply_details.trade_num asc ";
        }elseif($flag == 2){
            $sql .= " AND oil_receipt_apply_details.trade_num >= 0 order by oil_receipt_apply_details.trade_num desc ";
        }else{
            if(count($excep_id) > 0) {
                $sql .= " and oil_receipt_apply_details.trades_id not in (" . implode(",", $excep_id) . ") ";
            }
        }
        $sql .= " limit ".$num;
        if($flag == 2){
            // echo $sql;
        }
        Log::error('getGroupApplyDetails:', [$sql],'receiptSplitSql_');
        return Capsule::connection()->select($sql);
    }


    static function checkDiff($apply_id = 0)
    {
        $sql = "SELECT
  *
FROM
  (
    (
      SELECT
        sum(trade_num) AS c_num,
        sum(trade_money) AS c_money,
        sum(use_fanli_money) AS c_fanli,
        count(
            case
              WHEN trade_num < 0 or trade_money < 0 then 1
            end
          ) as num1
      FROM
        oil_receipt_apply_details_combine
      WHERE
        receipt_apply_id = ".$apply_id."
    ) AS c
    ,
      (
        SELECT
          sum(trade_num) AS d_num,
          sum(trade_money) AS d_money,
          sum(use_fanli_money) AS d_fanli
        FROM
          oil_receipt_apply_details
        WHERE
          receipt_apply_id = ".$apply_id."
      ) AS d
  )";

  Log::error('checkDiff:', [$sql],'receiptSplitSql_');
        return Capsule::connection()->select($sql);
    }
}