<?php
/**
 * oil_gas_assign_app
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/04/16
 * Time: 11:12:00
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilGasAssignApp extends \Framework\Database\Model
{
    protected $table = 'oil_gas_assign_app';

    protected $guarded = ["id"];

    protected $fillable = ['no','no_type','from_org_id','into_org_id','from_orgcode','from_orgname','into_orgcode','into_orgname','apply_time','money','status','data_from','creator_id','other_creator_id','other_creator','last_operator','remark','createtime','updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function FromOrg()
    {
        return $this->belongsTo('Models\OilOrg','from_org_id','id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function ToOrg()
    {
        return $this->belongsTo('Models\OilOrg','into_org_id','id');
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By no
        if (isset($params['no']) && $params['no'] != '') {
            $query->where('no', 'like', '%'.$params['no'].'%');
        }

        //Search By no_type
        if (isset($params['no_type']) && $params['no_type'] != '') {
            $query->where('no_type', '=', $params['no_type']);
        }

        //Search By org_id
        if (isset($params['from_org_id']) && $params['from_org_id'] != '') {
            $query->where('from_org_id', '=', $params['from_org_id']);
        }

        //Search By into_org_id
        if (isset($params['into_org_id']) && $params['into_org_id'] != '') {
            $query->where('into_org_id', '=', $params['into_org_id']);
        }

        //Search By from_orgcode
        if (isset($params['from_orgcode']) && $params['from_orgcode'] != '') {
            if (isset($params['org_flag']) && $params['org_flag']) {
                $query->where('from_orgcode', 'like', $params['from_orgcode'].'%');
            }else{
                $query->where('from_orgcode', '=', $params['from_orgcode']);
            }
        }

        //Search By from_orgname
        if (isset($params['from_orgname']) && $params['from_orgname'] != '') {
            $query->where('from_orgname', '=', $params['from_orgname']);
        }

        //Search By into_orgcode
        if (isset($params['into_orgcode']) && $params['into_orgcode'] != '') {
            $query->where('into_orgcode', '=', $params['into_orgcode']);
        }

        //Search By into_orgname
        if (isset($params['into_orgname']) && $params['into_orgname'] != '') {
            $query->where('into_orgname', '=', $params['into_orgname']);
        }

        //Search By app_time
        if (isset($params['apply_timeGe']) && $params['apply_timeGe'] != '') {
            $query->where('apply_time', '>=', substr($params['apply_timeGe'],0,10));
        }

        if (isset($params['apply_timeLe']) && $params['apply_timeLe'] != '') {
            $query->where('apply_time', '<=', substr($params['apply_timeLe'],0,10)." 23:59:59");
        }

        //Search By money
        if (isset($params['money']) && $params['money'] != '') {
            $query->where('money', '=', $params['money']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By data_from
        if (isset($params['data_from']) && $params['data_from'] != '') {
            $query->where('data_from', '=', $params['data_from']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By other_creator_id
        if (isset($params['other_creator_id']) && $params['other_creator_id'] != '') {
            $query->where('other_creator_id', '=', $params['other_creator_id']);
        }

        //Search By other_creator
        if (isset($params['other_creator']) && $params['other_creator'] != '') {
            $query->where('other_creator', '=', $params['other_creator']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By is_test
        if (isset($params['is_test']) && $params['is_test'] != '') {
            $orgId = OilOrg::getByIsTestLike($params['is_test']);
            $query->whereIn('from_org_id', $orgId);
        }

        return $query;
    }

    /**
     * oil_gas_assign_app 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        //$sqlObj = OilGasAssignApp::Filter($params);

        if(isset($params['sortColumns'])){
            $_sortColumns = explode(" ", $params['sortColumns']);
            $orderField = $_sortColumns[0] == 'corp_name' ? "convert(corp_name using gbk) " : $_sortColumns[0];
            $orderType = $_sortColumns[1];
        }else{
            $orderField = 'createtime';
            $orderType = 'desc';
        }
        $sqlObj = OilGasAssignApp::Filter($params)->with(
            [
                'FromOrg'   =>  function($query){
                    $query->select('id','is_test');
                }
            ]
        )->orderBy($orderField,$orderType);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        } elseif (isset($params['skip']) && isset($params['take'])) {
            $data = $sqlObj
                ->skip(intval($params['skip']))
                ->take(intval($params['take']))
                ->get();
        } elseif (isset($params['count']) && intval($params['count']) == 1) {
            $data = $sqlObj->count();
        } else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }
        return $data;
    }

    /**
     * oil_gas_assign_app 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilGasAssignApp::find($params['id']);
    }
    

    /**
     * oil_gas_assign_app 详情查询 for update lock
     * @param array $params
     * @return object
     */
    static public function getByIdForUpdate(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilGasAssignApp::where('id','=', $params['id'])->lockForUpdate()->first();
    }

    /**
     * oil_gas_assign_app 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilGasAssignApp::create($params);
    }

    /**
     * oil_gas_assign_app 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilGasAssignApp::find($params['id'])->update($params);
    }

    /**
     * oil_gas_assign_app 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilGasAssignApp::destroy($params['ids']);
    }

    /**
     * 生成单号
     * @param $head 编号首字母
     * 编号 + 年月日 + 当日序号最大值+1
     * @example CZ15122400001
     * @param $head
     * @return string
     */
    static public function createNo($head = '')
    {
        $no = "";
        if(!empty($head))
        {
            $no = $head.date('ymd');

            $result = OilGasAssignApp::where('no','like',$no.'%')->max(no);

            if($result)
            {
                $no .= sprintf("%05d",(substr($result,-5) + 1));
            }else{
                $no .= '00001';
            }
        }
        return $no;
    }

    /**
     * 根据付款机构id对冻结金额求和
     * @param null $from_org_id
     * @return mixed
     */
    static public function sumMoneyByOrgIdNotFinished($from_org_id = NULL)
    {
        return OilGasAssignApp::where("from_org_id","=",$from_org_id)->where('status','=',0)->sum("money");
    }

    /**
     * 获取资金来源机构的可使用现金余额
     * @param $from_orgcode
     * @return float
     */
    static  public function getMoneyFromOrgUseCash($from_orgcode){

        $orgsFrom = \Models\OilOrg::getByOrgcode($from_orgcode);
        if(!$orgsFrom){
            throw new \RuntimeException('不存在机构id，请联系管理员',2);
        }
        $from_org_id = $orgsFrom->id;

        //获取公司卡账户冻结金额
        $gasLockMoney = OilGasAssignApp::sumMoneyByOrgIdNotFinished($from_org_id);
        //获取分配资金账户冻结金额
        $assignLockMoney = \Models\OilAccountAssign::sumLockMoney(['org_id'=>$from_org_id]);

        //获取资金账户相关信息
        $account = \Models\OilAccountMoney::getByOrgId(['org_id'=>$from_org_id]);

        //计算可分配余额
        $remain = floatval($account->money) - (floatval($gasLockMoney) + floatval($assignLockMoney->sumMoneyTotal)) - (floatval($account->cash_fanli_remain) - floatval($assignLockMoney->sumFanliTotal));


        return $remain;
    }

    /**
     * getByFromCode 获取未审核通过的公司卡充值申请单money之和
     * @param array $params
     * @return mixed
     */
    static public function getByFromCode(array $params)
    {
        \helper::argumentCheck(['fromOrgcode'],$params);

        return OilGasAssignApp::where('from_orgcode','=',$params['fromOrgcode'])
            ->where('status','=',0)
            ->where('money','>',0)
            ->sum('money');
    }
    /**
     * getByFromCode 获取未审核通过的公司卡转账申请单money之和
     * @param array $params
     * @return mixed
     */
    static public function getTransferMoney(array $params)
    {
        \helper::argumentCheck(['fromOrgcode'],$params);

        return OilGasAssignApp::where('from_orgcode','=',$params['fromOrgcode'])
            ->where('status','=',0)
            ->where('money','>',0)
            ->sum('money');
    }

    /**
     * 根据into_orgcode 获取money之和
     */
    static public function getSumMoneyByIntoOrgcode(array $params)
    {
        \helper::argumentCheck(['into_orgcode'],$params);

        return OilGasAssignApp::whereIn('into_orgcode',$params['into_orgcode'])
            ->where('status','=',1)
            ->groupBy('into_orgcode')
            ->select(Capsule::connection()->raw('into_orgcode,sum(money) as money'))
            ->get()->toArray();
    }


}