<?php
/**
 * 调用接口日志表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:16
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class GspApiLogs extends \Framework\Database\Model
{
    protected $table = 'gsp_api_logs';

    protected $guarded = ["id"];

    protected $fillable = ['method','app_key','args_data','args_timestamp','args_sign','request_time','is_responded','response_time','response','createtime','updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By method
        if (isset($params['method']) && $params['method'] != '') {
            $query->where('method', '=', $params['method']);
        }

        //Search By app_key
        if (isset($params['app_key']) && $params['app_key'] != '') {
            $query->where('app_key', '=', $params['app_key']);
        }

        //Search By args_data
        if (isset($params['args_data']) && $params['args_data'] != '') {
            $query->where('args_data', '=', $params['args_data']);
        }

        //Search By args_timestamp
        if (isset($params['args_timestamp']) && $params['args_timestamp'] != '') {
            $query->where('args_timestamp', '=', $params['args_timestamp']);
        }

        //Search By args_sign
        if (isset($params['args_sign']) && $params['args_sign'] != '') {
            $query->where('args_sign', '=', $params['args_sign']);
        }

        //Search By request_time
        if (isset($params['request_time']) && $params['request_time'] != '') {
            $query->where('request_time', '=', $params['request_time']);
        }

        //Search By is_responded
        if (isset($params['is_responded']) && $params['is_responded'] != '') {
            $query->where('is_responded', '=', $params['is_responded']);
        }

        //Search By response_time
        if (isset($params['response_time']) && $params['response_time'] != '') {
            $query->where('response_time', '=', $params['response_time']);
        }

        //Search By response
        if (isset($params['response']) && $params['response'] != '') {
            $query->where('response', '=', $params['response']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }


        return $query;
    }

    /**
     * 调用接口日志表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = GspApiLogs::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 调用接口日志表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspApiLogs::find($params['id']);
    }

    /**
     * 调用接口日志表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return GspApiLogs::create($params);
    }

    /**
     * 调用接口日志表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspApiLogs::find($params['id'])->update($params);
    }

    /**
     * 调用接口日志表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return GspApiLogs::destroy($params['ids']);
    }




}