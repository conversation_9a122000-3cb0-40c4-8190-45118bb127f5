<?php
/**
 * 开票额度
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/02/06
 * Time: 14:35:41
 */
namespace Models;
use Framework\Helper;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilReceiptQuota extends \Framework\Database\Model
{
    protected $table = 'oil_receipt_quota';

    public $incrementing = FALSE;

    protected $guarded = ["id"];
    protected $fillable = ["id",'org_id','orgroot', 'pay_company_id','org_total','company_total','operator','last_apply_time','status',
        'createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    public function Org()
    {
        return $this->belongsTo('Models\\OilOrg','org_id','id');
    }


    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By ne_id 不等于
        if (isset($params['ne_id']) && $params['ne_id'] != '') {
            $query->where('id', '!=', $params['ne_id']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By orgroot
        if (isset($params['orgroot']) && $params['orgroot'] != '') {
            $query->where('orgroot', '=', $params['orgroot']);
        }

        //Search By pay_company_id
        if (isset($params['pay_company_id']) && $params['pay_company_id'] != '') {
            $query->where('pay_company_id', '=', $params['pay_company_id']);
        }

        //Search By org_total
        if (isset($params['org_total']) && $params['org_total'] != '') {
            $query->where('org_total', '=', $params['org_total']);
        }

        //Search By company_total
        if (isset($params['company_total']) && $params['company_total'] != '') {
            $query->where('company_total', '=', $params['company_total']);
        }

        //Search By operator
        if (isset($params['operator']) && $params['operator'] != '') {
            $query->where('operator', '=', $params['operator']);
        }

        //Search By last_apply_time
        if (isset($params['last_apply_time']) && $params['last_apply_time'] != '') {
            $query->where('last_apply_time', '=', $params['last_apply_time']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 开票额度 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilReceiptQuota::Filter($params)
            ->select(Capsule::connection()->raw("oil_receipt_quota.*,oil_org.org_name,oil_pay_company.company_name"))
            ->leftJoin('oil_org','oil_org.id','=','oil_receipt_quota.org_id')
            ->leftJoin('oil_pay_company','oil_pay_company.id','=','oil_receipt_quota.pay_company_id');
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * @title 修改额度
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param array $params
     * @returns
     * []
     * @returns
     */
    static public function updateQuota(array $params)
    {
        $info = self::where('org_id',$params['org_id'])->where('pay_company_id',$params['pay_company_id'])->first();

        if(!$info){
            throw new \RuntimeException('开票额度没有维护',2);
        }

        $info->update(['company_total'=>$info->company_total - $params['receipt_amount']]);
        self::where('orgroot',substr($params['org_code'],0,6))->update([
            'org_total'=>$info->org_total - $params['receipt_amount'],
            'last_apply_time'=>\helper::nowTime()
        ]);
    }

    /**
     * 开票额度 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptQuota::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptQuota::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * @title 获取单条记录
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getSingleRecord(array $params)
    {
        return self::Filter($params)->first();
    }

    /**
     * 开票额度 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        global $app;
        $params['operator'] = $app->myAdmin->true_name;
        $params['id'] = Helper::uuid();

        return OilReceiptQuota::create($params);
    }

    /**
     * 开票额度 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptQuota::find($params['id'])->update($params);
    }

    /**
     * 开票额度 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilReceiptQuota::destroy($params['ids']);
    }




}