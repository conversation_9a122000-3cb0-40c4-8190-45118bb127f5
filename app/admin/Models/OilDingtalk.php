<?php
/**
 * oil_dingtalk
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/06/24
 * Time: 03:03:59
 */

namespace Models;

use Framework\Database\Model;
use Fuel\Defines\AlarmDingTalkType;
use Fuel\Defines\NotifySubscribe;
use Fuel\Defines\NotifyType;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilDingtalk extends Model
{
    protected $table = 'oil_dingtalk';
    
    protected $guarded = ["id"];
    
    protected $fillable = ['uid', 'phone', 'nickname', 'email', 'type', 'type_ext', 'subscribe', 'status', 'creator_id', 'updator_id', 'createtime', 'updatetime'];
    
    public function getFillAble()
    {
        return $this->fillable;
    }
    
    public function CreateUser()
    {
        return $this->belongsTo('Models\GspSysUsers', 'creator_id', 'id');
    }
    
    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }
        
        //Search By uid
        if (isset($params['uid']) && $params['uid'] != '') {
            $query->where('uid', '=', $params['uid']);
        }
        
        //Search By phone
        if (isset($params['phone']) && $params['phone'] != '') {
            $query->where('phone', 'like', '%' . $params['phone'] . '%');
        }
        
        //Search By nickname
        if (isset($params['nickname']) && $params['nickname'] != '') {
            $query->where('nickname', 'like', '%' . $params['nickname'] . '%');
        }
        
        //Search By email
        if (isset($params['email']) && $params['email'] != '') {
            $query->where('email', 'like', '%' . $params['email'] . '%');
        }
        
        //Search By type
        if (isset($params['type']) && $params['type'] != '') {
            $query->where('type', '=', $params['type']);
        }
        
        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }
        
        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }
        
        //Search By updator_id
        if (isset($params['updator_id']) && $params['updator_id'] != '') {
            $query->where('updator_id', '=', $params['updator_id']);
        }
        
        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }
        
        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }
        
        
        return $query;
    }
    
    /**
     * oil_dingtalk 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilDingtalk::Filter($params)->with([
            'CreateUser' => function ($query) {
                $query->select('id', 'true_name');
            }
        ]);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy("createtime", "desc")->paginate($params['limit'], ['*'], 'page', $params['page']);
        }
        
        if (count($data) > 0) {
            foreach ($data as &$v) {
                $type_ext = $v->type_ext ? explode(',', $v->type_ext) : '';
                $type_extArr = [];
                foreach ($type_ext as $item) {
                    $typeExtType = AlarmDingTalkType::getById($item);
                    if ($typeExtType) {
                        $type_extArr[] = $typeExtType;
                    }
                    
                }
                $v->type_ext_text = implode(',', $type_extArr);
                
                $subscribe = $v->subscribe ? explode(',', $v->subscribe) : '';
                $subscribeArr = [];
                foreach ($subscribe as $item) {
                    $subscribeTypeInfo = NotifySubscribe::getById($item);
                    if ($subscribeTypeInfo) {
                        $subscribeArr[] = $subscribeTypeInfo;
                    }
                }
                
                $v->subscribe_text = implode(',', $subscribeArr);
            }
        }
        
        return $data;
    }
    
    /**
     * oil_dingtalk 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilDingtalk::find($params['id']);
    }
    
    /**
     * oil_dingtalk 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        $userInfo = \Framework\Session::get('userInfo');
        $params['creator_id'] = isset($params['creator_id']) && $params['creator_id'] ? $params['creator_id'] : $userInfo->id;
        $params['type_ext'] = isset($params['type_ext']) && $params['type_ext'] ? json_encode($params['type_ext']) : [];
        $params['subscribe'] = isset($params['subscribe']) && $params['subscribe'] ? json_encode($params['subscribe']) : [];
        
        return OilDingtalk::create($params);
    }
    
    /**
     * oil_dingtalk 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilDingtalk::find($params['id'])->update($params);
    }
    
    /**
     * oil_dingtalk 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);
        
        return OilDingtalk::destroy($params['ids']);
    }
    
    /**
     * getByType
     * @param $type
     * @return mixed
     * <AUTHOR>
     * @since ${DATE}
     */
    static public function getByType($type)
    {
        return OilDingtalk::where("type", "=", $type)->where("status", "=", 1)->pluck("phone")->toArray();
    }
    
    
}