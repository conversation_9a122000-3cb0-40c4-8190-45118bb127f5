<?php
/**
 * gas 订单
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:19
 */

namespace Models;

use Framework\Cache;
use Framework\Log;
use Fuel\Defines\OilCom;
use Illuminate\Database\Capsule\Manager as Capsule;

class GasOrderNew extends \Framework\Database\Model
{
    protected $connection = "gas_only_read";

    protected $table = 'gas_order_new';

	protected $guarded = ["order_id"];

	protected $fillable = [
		'order_id', 'order_channel', 'trade_mode', 'order_status', 'station_id', 'station_code', 'card_no',
        'create_time', 'update_time','payment_id','pay_time','third_order_id','history_id','real_oil_num'
	];

	public function getFillAble()
	{
		return $this->fillable;
	}

	public function getConnection()
    {
        return $this->connection;
    }

    public function getTable()
    {
        return $this->table;
    }

	/**
	 * 聚集查询
	 * @param $query
	 * @param $params
	 * @return $query
	 */
	public function scopeFilter($query, $params)
	{
	    //Search By id
		if (isset($params['id']) && $params['id'] != '') {
			$query->where('id', '=', $params['id']);
		}

        if (isset($params['station_code']) && $params['station_code'] != '') {
            if (is_array($params['station_code'])) {
                $query->whereIn('station_code', $params['station_code']);
            } else {
                $query->where('station_code', '=', $params['station_code']);
            }
        }

        //order_channel
        if (isset($params['order_channelNin']) && $params['order_channelNin'] != '') {
            $query->whereNotIn('order_channel', $params['order_channelNin']);
        }

        //Search By create_time
        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('create_time', '>=', $params['createtimeGe']);
        }

        //Search By create_time
        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $query->where('create_time', '<=', $params['createtimeLe']);
        }

        //Search By pay_time
        if (isset($params['pay_timeGe']) && $params['pay_timeGe'] != '') {
            $query->where('pay_time', '>=', $params['pay_timeGe']);
        }

        //Search By pay_time
        if (isset($params['pay_timeLe']) && $params['pay_timeLe'] != '') {
            $query->where('pay_time', '<=', $params['pay_timeLe']);
        }

		return $query;
	}

	/**
	 * 油站表 列表查询
	 * @param array $params
	 * @return array
	 */
	static public function getList(array $params)
	{
        $conn = (new GasOrderNew())->getConnection();
		$data = [];
		$params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
		$params['page'] = isset($params['page']) ? $params['page'] : 1;
        $obj = Capsule::connection($conn)->table((new GasOrderNew())->getTable());
		$sqlObj = (new GasOrderNew())->scopeFilter($obj,$params);

        $orderField = 'create_time';
        $orderType = 'desc';

		if (isset($params['_export']) && $params['_export'] == 1) {
			$data = $sqlObj->orderBy($orderField, $orderType)->get();
		} else {
			$data = $sqlObj->orderBy($orderField, $orderType)->paginate($params['limit'], ['*'], 'page', $params['page']);
		}
		return $data;
	}

	/**
	 * 油站表 详情查询
	 * @param array $params
	 * @return object
	 */
	static public function getById(array $params)
	{
		\helper::argumentCheck(['id'], $params);

		return self::find($params['id']);
	}

	/**
	 * 油站表 新增
	 * @param array $params
	 * @return mixed
	 */
	static public function add(array $params)
	{
		return self::create($params);
	}

	/**
	 * 油站表 编辑
	 * @param array $params
	 * @return mixed
	 */
	static public function edit(array $params)
	{
		\helper::argumentCheck(['id'], $params);

		return self::find($params['id'])->update($params);
	}

	/**
	 * 油站表 根据ids删除或批量删除
	 * @param array $params
	 * @return int
	 */
	static public function remove(array $params)
	{
		\helper::argumentCheck(['ids'], $params);

		return self::destroy($params['ids']);
	}

	static public function getStationByScope($params = [])
	{
	    $conn = (new GasOrderNew())->getConnection();
	    $fields = "id,station_code,station_name,city_code,provice_code,pcode";
        $sqlObj = Capsule::connection($conn)->table('gas_station')
            ->select(Capsule::raw($fields));

        $sqlObj = (new GasOrderNew())->scopeFilter($sqlObj, $params);
        $data = $sqlObj->get();
        return $data;
	}
}