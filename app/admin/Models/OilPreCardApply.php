<?php
/**
 * oil_pre_card_apply
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/07/11
 * Time: 16:54:57
 */
namespace Models;
use Framework\Log;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilPreCardApply extends \Framework\Database\Model
{
    protected $table = 'oil_pre_card_apply';

    protected $guarded = ["id"];
    protected $fillable = ['org_id','qr_uuid','oil_com','driver_name','driver_tel','truck_no','status','is_del','data_from','remark','audit_time','createtime','updatetime','last_operator_id','last_operator'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By ids
        if (isset($params['id_arr']) && $params['id_arr'] != '') {
            $query->whereIn('oil_pre_card_apply.id', $params['id_arr']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('oil_pre_card_apply.org_id', '=', $params['org_id']);
        }

        //Search By org_idIn
        if (isset($params['orgCodeIn']) && $params['orgCodeIn'] != '') {
            $query->whereIn('oil_org.orgcode', $params['orgCodeIn']);
        }

        //Search By qr_uuid
        if (isset($params['qr_uuid']) && $params['qr_uuid'] != '') {
            $query->where('qr_uuid', '=', $params['qr_uuid']);
        }

        //Search By driver_name
        if (isset($params['driver_name']) && $params['driver_name'] != '') {
            $query->where('driver_name', '=', $params['driver_name']);
        }

        //Search By driver_tel
        if (isset($params['driver_tel']) && $params['driver_tel'] != '') {
            $query->where('driver_tel', '=', $params['driver_tel']);
        }

        //Search By truck_no
        if (isset($params['truck_no']) && $params['truck_no'] != '') {
            $query->where('truck_no', '=', $params['truck_no']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('oil_pre_card_apply.status', '=', $params['status']);
        }

        //Search By templateStatus
        if (isset($params['templateStatus']) && $params['templateStatus'] != '') {
            $query->where('oil_card_apply_template.status', '=', $params['templateStatus']);
        }

        //Search By templateStatus
        if (isset($params['is_del']) && $params['is_del'] >= 0) {
            $query->where('oil_pre_card_apply.is_del', '=', $params['is_del']);
        }

        //Search By data_from
        if (isset($params['data_from']) && $params['data_from'] != '') {
            $query->where('data_from', '=', $params['data_from']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By audit_time
        if (isset($params['audit_time']) && $params['audit_time'] != '') {
            $query->where('audit_time', '=', $params['audit_time']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By last_operator_id
        if (isset($params['last_operator_id']) && $params['last_operator_id'] != '') {
            $query->where('last_operator_id', '=', $params['last_operator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        return $query;
    }

    /**
     * oil_pre_card_apply 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilPreCardApply::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }


    /**
     * oil_pre_card_apply 待审核列表查询
     * @param array $params
     * @return array
     */
    static public function getJoinList(array $params,$fields = "")
    {
        $data = [];

        //Capsule::connection()->enableQueryLog();
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilPreCardApply::Filter($params)
            ->leftJoin('oil_card_apply_template','oil_card_apply_template.qr_uuid','=','oil_pre_card_apply.qr_uuid')
            ->select(Capsule::connection()->raw( $fields) );

        if (isset($params['count']) && $params['count'] == 1) {
            return $sqlObj->count();
        } elseif (isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('oil_pre_card_apply.createtime', 'desc')->get();
        } elseif (isset($params['skip']) && isset($params['take'])) {
            $data = $sqlObj->skip(intval($params['skip']))->take(intval($params['take']))->get();
        } else {
            $data = $sqlObj->orderBy('oil_pre_card_apply.createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }
        //$sql = Capsule::connection()->getQueryLog();
        //Log::error("sss".var_export($sql,true),[],"JoinList_");
        return self::formatData($data);
    }

    static public function formatData($data)
    {
        foreach ($data as &$v) {
            $v->_createtime = date("Y.m.d H:i",strtotime($v->createtime));
        }
        return $data;
    }

    /**
     * oil_pre_card_apply 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilPreCardApply::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilPreCardApply::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 悲观锁查询手机号办卡记录
     * @param array $params
     * @return object
     */
    static public function getByTelAndOilComLock(array $params)
    {
        \helper::argumentCheck(['driver_tel','org_id'],$params);

        return OilPreCardApply::lockForUpdate()->
                where('driver_tel',$params['driver_tel'])
                //->where('qr_uuid',$params['qr_uuid'])
                ->where("oil_com",$params['oil_com'])
                ->where("is_del",0)
                ->where('org_id',$params['org_id'])
                ->first();
    }

    /**
     * oil_pre_card_apply 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilPreCardApply::create($params);
    }

    /**
     * oil_pre_card_apply 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilPreCardApply::find($params['id'])->update($params);
    }

    /**
     * oil_pre_card_apply 编辑
     * @param array $params
     * @return mixed
     */
    static public function editByIds(array $params,$updataData)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilPreCardApply::whereIn("id",$params['ids'])->update($updataData);
    }

    /**
     * oil_pre_card_apply 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilPreCardApply::destroy($params['ids']);
    }

    /**
     * 获取自动审核的数据
     * @param array $params
     * @return int
     */
    static public function getCardListByOrg(array $params)
    {
        $fields = "oil_pre_card_apply.id,oil_org.orgcode";
        $sqlObj = OilPreCardApply::Filter($params)
            ->leftJoin('oil_org','oil_org.id','=','oil_pre_card_apply.org_id')
            ->select(Capsule::connection()->raw( $fields) );
            $data = $sqlObj->get();
        return $data;
    }
}