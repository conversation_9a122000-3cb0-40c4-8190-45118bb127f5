<?php
/**
 * oil_trades_out
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2017/02/07
 * Time: 12:20:18
 */
namespace Models;

use Framework\Log;
use Fuel\Defines\CardFrom;
use Fuel\Defines\ConsumeType;
use Fuel\Defines\OilCom;
use Fuel\Defines\TradesType;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilTradesOut extends \Framework\Database\Model
{
    protected $table = 'oil_trades_out';


    protected $fillable = ['id'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        //Search By id
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $query->where('oil_org.orgcode', '=', $params['orgcode']);
        }

        //Search By id
        if (isset($params['orgcodelk']) && $params['orgcodelk'] != '') {
            $query->where('oil_org.orgcode', 'like', $params['orgcodelk'].'%');
        }

        //Search By id
        if (isset($params['orgcodeIn']) && $params['orgcodeIn'] != '') {
            $query->whereIn('oil_org.orgcode', explode(',',$params['orgcodeIn']));
        }

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('oil_card_vice_trades.id', '=', $params['id']);
        }

        //Search By api_id
        if (isset($params['api_id']) && $params['api_id'] != '') {
            $query->where('oil_card_vice_trades.api_id', '=', $params['api_id']);
        }

        //Search By vice_no
        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $query->where('oil_card_vice_trades.vice_no', '=', $params['vice_no']);
        }

        //Search By vice_noIn
        if (isset($params['vice_noIn']) && $params['vice_noIn'] != '') {
            $query->whereIn('oil_card_vice_trades.vice_no', $params['vice_noIn']);
        }

        //Search By trade_time
        if (isset($params['timeGe']) && $params['timeGe'] != '') {
            $query->where('oil_card_vice_trades.trade_time', '>', $params['timeGe']);
        }

        //Search By tradetimeGt
        if (isset($params['tradetimeGt']) && $params['tradetimeGt'] != '') {
            $query->where('oil_card_vice_trades.trade_time', '>', $params['tradetimeGt']);
        }

        //Search By tradetimeGe
        if (isset($params['tradetimeGe']) && $params['tradetimeGe'] != '') {
            $query->where('oil_card_vice_trades.trade_time', '>=', $params['tradetimeGe'] . ' 00:00:00');
        }

        //Search By tradetimeLe
        if (isset($params['tradetimeLe']) && $params['tradetimeLe'] != '') {
            $query->where('oil_card_vice_trades.trade_time', '<=', $params['tradetimeLe'] . ' 23:59:59');
        }

        //Search By tradetimeGe
        if (isset($params['trade_timeGe']) && $params['trade_timeGe'] != '') {
            $query->where('oil_card_vice_trades.trade_time', '>=', $params['trade_timeGe']);
        }

        //Search By tradetimeLe
        if (isset($params['trade_timeLe']) && $params['trade_timeLe'] != '') {
            $query->where('oil_card_vice_trades.trade_time', '<=', $params['trade_timeLe']);
        }

        //Search By tradetimeGe
        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('oil_card_vice_trades.createtime', '>=', $params['createtimeGe'] . ' 00:00:00');
        }

        //Search By tradetimeLe
        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $query->where('oil_card_vice_trades.createtime', '<=', $params['createtimeLe'] . ' 23:59:59');
        }

        //Search By trade_time
        if (isset($params['trade_time']) && $params['trade_time'] != '') {
            $query->where('oil_card_vice_trades.trade_time', '=', $params['trade_time']);
        }

        //Search By trade_typeList
        if (isset($params['tradeTypeList']) && $params['tradeTypeList'] != '') {
            $query->whereIn('oil_card_vice_trades.trade_type', $params['tradeTypeList']);
        }

        //Search By oil_name
        if (isset($params['oil_name']) && $params['oil_name'] != '') {
            $query->where('oil_card_vice_trades.oil_name', '=', $params['oil_name']);
        }

        //Search By trade_money
        if (isset($params['trade_money']) && $params['trade_money'] != '') {
            $query->where('oil_card_vice_trades.trade_money', '=', $params['trade_money']);
        }

        //Search By use_fanli_money
        if (isset($params['use_fanli_money']) && $params['use_fanli_money'] != '') {
            $query->where('oil_card_vice_trades.use_fanli_money', '=', $params['use_fanli_money']);
        }

        //Search By trade_price
        if (isset($params['trade_price']) && $params['trade_price'] != '') {
            $query->where('oil_card_vice_trades.trade_price', '=', $params['trade_price']);
        }

        //Search By trade_num
        if (isset($params['trade_num']) && $params['trade_num'] != '') {
            $query->where('oil_card_vice_trades.trade_num', '=', $params['trade_num']);
        }

        //Search By trade_jifen
        if (isset($params['trade_jifen']) && $params['trade_jifen'] != '') {
            $query->where('oil_card_vice_trades.trade_jifen', '=', $params['trade_jifen']);
        }

        //Search By balance
        if (isset($params['balance']) && $params['balance'] != '') {
            $query->where('oil_card_vice_trades.balance', '=', $params['balance']);
        }

        //Search By receipt_remain
        if (isset($params['receipt_remain']) && $params['receipt_remain'] != '') {
            $query->where('oil_card_vice_trades.receipt_remain', '=', $params['receipt_remain']);
        }

        //Search By trade_place
        if (isset($params['trade_place']) && $params['trade_place'] != '') {
            $query->where('oil_card_vice_trades.trade_place', 'like', '%' . $params['trade_place'] . '%');
        }

        //Search By regions_id
        if (isset($params['regions_id']) && $params['regions_id'] != '') {
            $query->where('oil_card_vice_trades.regions_id', '=', $params['regions_id']);
        }

        //Search By regions_name
        if (isset($params['regions_name']) && $params['regions_name'] != '') {
            $query->where('oil_card_vice_trades.regions_name', '=', $params['regions_name']);
        }

        //Search By fetch_time
        if (isset($params['fetch_time']) && $params['fetch_time'] != '') {
            $query->where('oil_card_vice_trades.fetch_time', '=', $params['fetch_time']);
        }

        //Search By is_fanli
        if (isset($params['is_fanli']) && $params['is_fanli'] != '') {
            $query->where('oil_card_vice_trades.is_fanli', '=', $params['is_fanli']);
        }

        //Search By main_no
        if (isset($params['main_no']) && $params['main_no'] != '') {
            $query->where('oil_card_vice_trades.main_no', '=', $params['main_no']);
        }

        //Search By card_owner
        if (isset($params['card_owner']) && $params['card_owner'] != '') {
            $query->where('oil_card_vice_trades.card_owner', '=', $params['card_owner']);
        }

        //Search By oil_com
        if (isset($params['oil_com']) && $params['oil_com'] != '') {
            $query->where('oil_card_vice_trades.oil_com', '=', $params['oil_com']);
            $query->whereIn('oil_card_vice_trades.oil_com', explode(',',$params['oil_com']));
        }

        //Search By active_region
        if (isset($params['active_region']) && $params['active_region'] != '') {
            $query->where('oil_card_vice_trades.active_region', '=', $params['active_region']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('oil_card_vice_trades.org_id', '=', $params['org_id']);
        }

        //Search By org_name
        if (isset($params['org_name']) && $params['org_name'] != '') {
            $query->where('oil_card_vice_trades.org_name', '=', $params['org_name']);
        }

        //Search By consume_type
        if (isset($params['consume_type']) && $params['consume_type'] != '') {
            $query->where('oil_card_vice_trades.consume_type', '=', $params['consume_type']);
        }

        //Search By truck_no
        if (isset($params['truck_no']) && $params['truck_no'] != '') {
            $query->whereIn('oil_card_vice.truck_no', explode(',',$params['truck_no']));
        }

        //Search By fanli_no
        if (isset($params['fanli_no']) && $params['fanli_no'] != '') {
            $query->where('oil_card_vice_trades.fanli_no', '=', $params['fanli_no']);
        }

        //Search By fanli_noIn
        if (isset($params['fanli_noIn']) && $params['fanli_noIn'] != '') {
            $query->whereIn('oil_card_vice_trades.fanli_no', $params['fanli_noIn']);
        }

        //Search By fanli_money
        if (isset($params['fanli_money']) && $params['fanli_money'] != '') {
            $query->where('oil_card_vice_trades.fanli_money', '=', $params['fanli_money']);
        }

        //Search By fanli_jifen
        if (isset($params['fanli_jifen']) && $params['fanli_jifen'] != '') {
            $query->where('oil_card_vice_trades.fanli_jifen', '=', $params['fanli_jifen']);
        }

        //Search By policy_id
        if (isset($params['policy_id']) && $params['policy_id'] != '') {
            $query->where('oil_card_vice_trades.policy_id', '=', $params['policy_id']);
        }

        //Search By fanli_way
        if (isset($params['fanli_way']) && $params['fanli_way'] != '') {
            $query->where('oil_card_vice_trades.fanli_way', '=', $params['fanli_way']);
        }

        //Search By qz_drivername
        if (isset($params['qz_drivername']) && $params['qz_drivername'] != '') {
            $query->where('oil_card_vice_trades.qz_drivername', '=', $params['qz_drivername']);
        }

        //Search By qz_drivertel
        if (isset($params['qz_drivertel']) && $params['qz_drivertel'] != '') {
            $query->where('oil_card_vice_trades.qz_drivertel', '=', $params['qz_drivertel']);
        }

        //Search By createtime
        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('oil_card_vice_trades.createtime', '>=', $params['createtimeGe']);
        }

        //Search By createtime
        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $query->where('oil_card_vice_trades.createtime', '<=', $params['createtimeLe']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('oil_card_vice_trades.updatetime', '=', $params['updatetime']);
        }

        //Search By updatetimeGt
        if (isset($params['updatetimeGt']) && $params['updatetimeGt'] != '') {
            $query->where('oil_card_vice_trades.updatetime', '>', $params['updatetimeGt']);
        }

        //Search By updatetimeLe
        if (isset($params['updatetimeLe']) && $params['updatetimeLe'] != '') {
            $query->where('oil_card_vice_trades.updatetime', '<=', $params['updatetimeLe']);
        }

        if (isset($params['org_id_list']) && is_array($params['org_id_list'])) {
            $query->whereIn('oil_card_vice_trades.org_id', $params['org_id_list']);
        }

        //Search By vice_no
        if (isset($params['oil_comIn']) && $params['oil_comIn'] != '') {
            $query->whereIn('oil_card_vice_trades.oil_com', $params['oil_comIn']);
        }

        //Search By card_from
        if (isset($params['card_from']) && $params['card_from'] != '') {
            $query->whereIn('oil_card_vice_trades.card_from', explode(',',$params['card_from']));
        }

        //Search By card_fromIn
        if (isset($params['card_fromIn']) && $params['card_fromIn'] != '') {
            $query->whereIn('oil_card_vice_trades.card_from',$params['card_fromIn']);
        }

        //Search By oil_type
        if (isset($params['oil_type']) && $params['oil_type'] != '') {
            $query->where('oil_type_no.oil_type', $params['oil_type']);
        }
        //Search By org_id
        if (isset($params['orgcode']) && $params['orgcode'] != '' && isset($params['org_flag'])) {
            $query->where('oil_org.orgcode', 'like', $params['orgcode'] . '%');
        }
        if (isset($params['orgcode']) && $params['orgcode'] != '' && !isset($params['org_flag'])) {
            $query->where('oil_org.orgcode', '=', $params['orgcode']);
        }
        //Search By consume_region
        if (isset($params['consume_region']) && $params['consume_region'] != '') {
            $query->where('oil_station.regions_id', $params['consume_region']);
        }

        //Search By is_test
        if (isset($params['is_test']) && $params['is_test'] != '' ) {
            $query->where('oil_org.is_test', '=', $params['is_test']);
        }

        //Search By trade_place_provice_code
        if (isset($params['trade_place_provice_code']) && $params['trade_place_provice_code'] != '' ) {
            $query->where('oil_card_vice_trades.trade_place_provice_code', 'like', $params['trade_place_provice_code'] . '%');
        }

        //Search By is_test
        if (isset($params['trade_place_city_code']) && $params['trade_place_city_code'] != '' ) {
            $query->where('oil_card_vice_trades.trade_place_city_code', 'like', $params['trade_place_city_code'] . '%');
        }

        //Search By trade_place_provice_name
        if (isset($params['trade_place_provice_name']) && $params['trade_place_provice_name'] != '' ) {
            $query->where('oil_card_vice_trades.trade_place_provice_name', 'like', $params['trade_place_provice_name'] . '%');
        }

        //Search By trade_place_city_name
        if (isset($params['trade_place_city_name']) && $params['trade_place_city_name'] != '' ) {
            $query->where('oil_card_vice_trades.trade_place_city_name', 'like', $params['trade_place_city_name'] . '%');
        }
        return $query;
    }

    static private function getWhere(array $params)
    {
        $where = "1";
        if(isset($params['orgcode']) && $params['orgcode'] && isset($params['org_flag'])){
            $where .= " AND oo.orgcode LIKE '".$params['orgcode']."%'";
        }

        if(isset($params['orgcode']) && $params['orgcode'] && !isset($params['org_flag'])){
            $where .= " AND oo.orgcode = '".$params['orgcode']."'";
        }

        //Search By id
        if (isset($params['orgcodelk']) && $params['orgcodelk'] != '') {
            $where .= " AND oo.orgcode like '".$params['orgcode']."%'";
        }

        //Search By id
        if (isset($params['orgcodeIn']) && $params['orgcodeIn'] != '') {
            $orgArr = explode(',',$params['orgcodeIn']);
            $where .= " AND oo.orgcode In ('".implode("','",$orgArr)."')";
        }

        if(isset($params['vice_no']) && $params['vice_no']){
            $where .= " AND ocvt.vice_no LIKE '%".$params['vice_no']."%'";
        }

        if(isset($params['main_no']) && $params['main_no']){
            $where .= " AND ocvt.main_no LIKE '%".$params['main_no']."%'";
        }

        if(isset($params['active_region']) && $params['active_region']){
            $where .= " AND ocvt.active_region = ".$params['active_region'];
        }

        if (isset($params['oil_com']) && $params['oil_com'] != '') {
            $oilcomArr = explode(',',$params['oil_com']);
            $where .= " AND ocvt.oil_com In ('".implode("','",$oilcomArr)."')";
        }

        if (isset($params['card_from']) && $params['card_from'] != '') {
            $cardFromArr = explode(',',$params['card_from']);
            $where .= " AND ocvt.card_from In ('".implode("','",$cardFromArr)."')";
        }

        if(isset($params['tradetimeGe']) && $params['tradetimeGe']){
            $where .= " AND ocvt.trade_time >= '".$params['tradetimeGe']." 00:00:00" . "'";
        }

        if(isset($params['tradetimeLe']) && $params['tradetimeLe']){
            $where .= " AND ocvt.trade_time <= '".$params['tradetimeLe']." 23:59:59" . "'";
        }

        if(isset($params['oil_type']) && $params['oil_type']){
            $where .= " AND oil_type_no.oil_type = '".$params['oil_type']."'";
        }

        if(isset($params['trade_place']) && $params['trade_place']){
            $where .= " AND ocvt.trade_place LIKE '%".$params['trade_place']."%'";
        }

        if(isset($params['createtimeGe']) && $params['createtimeGe']){
            $where .= " AND oto.createtime >= '".$params['createtimeGe']." 00:00:00" . "'";
        }

        if(isset($params['createtimeLe']) && $params['createtimeLe']){
            $where .= " AND oto.createtime <= '".$params['createtimeLe']." 23:59:59" . "'";
        }

        //Search By tradetimeGe
        if (isset($params['trade_timeGe']) && $params['trade_timeGe'] != '') {
            $where .= " AND ocvt.trade_time >= '".$params['trade_timeGe']."'";
        }

        //Search By tradetimeLe
        if (isset($params['trade_timeLe']) && $params['trade_timeLe'] != '') {
            $where .= " AND ocvt.trade_time <= '".$params['trade_timeLe']."'";
        }

        //Search By truck_no
        if (isset($params['truck_no']) && $params['truck_no'] != '') {
            $orgArr = explode(',',$params['truck_no']);
            $where .= " AND ocv.truck_no In ('".implode("','",$orgArr)."')";
        }

        return $where;
    }

    /**
     * oil_trades_out 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $where = OilTradesOut::getWhere($params);
        $sql = "SELECT
org_root.org_name as root_name,ocvt.regions_name,op.province as active_region_name,ocvt.org_name,ocvt.id,ocvt.balance,ocvt
.vice_no,ocvt.trade_type,ocvt.trade_time,ocvt.trade_money,ocvt.oil_name,ocvt.trade_place,ocvt.oil_com,ocvt.main_no,oto.createtime,
ocvt.trade_num,ocvt.consume_type,ocv.driver_tel as qz_drivertel,ocv.driver_name as qz_drivername,ocv.truck_no
from
oil_trades_out oto
LEFT JOIN oil_card_vice_trades ocvt on ocvt.id = oto.id
LEFT JOIN oil_card_vice ocv on ocv.vice_no = ocvt.vice_no
LEFT JOIN oil_org oo on oo.id = ocvt.org_id
LEFT JOIN oil_org org_root on org_root.orgcode = SUBSTR(oo.orgcode FROM 1 FOR 6)
LEFT JOIN oil_provinces op on op.id = ocvt.active_region
LEFT JOIN oil_type_no on oil_type_no.oil_no = ocvt.oil_name
WHERE $where
ORDER BY ocvt.trade_time DESC";

        Log::debug('tradeout:sql:'.$sql,[],'tradeout');

        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = Capsule::connection()->select($sql);
        } else {
            //total
            $info = Capsule::connection()->select("SELECT COUNT(*) as total FROM oil_trades_out oto
                LEFT JOIN oil_card_vice_trades ocvt on ocvt.id = oto.id
                LEFT JOIN oil_org oo on oo.id = ocvt.org_id
                LEFT JOIN oil_org org_root on org_root.orgcode = SUBSTR(oo.orgcode FROM 1 FOR 6)
                LEFT JOIN oil_provinces op on op.id = ocvt.active_region
                LEFT JOIN oil_type_no on oil_type_no.oil_no = ocvt.oil_name
                WHERE $where");
            $total = $info[0]->total;

            $from = ($params['page'] - 1) * $params['limit'];
            $record = [];
            if ($total > 0) {
                $sql = $sql . " limit " . $from . ', ' . $params['limit'];
                $record = Capsule::connection()->select($sql);
            }
            $data = (object)[
                'per_page'     => $params['limit'],
                'current_page' => $params['page'],
                'data'         => $record,
                'from'         => $from,
                'to'           => $from + $params['limit'],
                'total'        => $total
            ];
        }

        return $data;
    }

    /**
     * oil_trades_out 列表查询
     * @param array $params
     * @return array
     */
    static public function getListByGroup(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $sqlObj = OilTradesOut::Filter($params)->leftJoin('oil_card_vice_trades','oil_card_vice_trades.id','=','oil_trades_out.id')
            ->leftJoin('oil_org','oil_org.id','=','oil_card_vice_trades.org_id')
            ->leftJoin('oil_card_vice','oil_card_vice.vice_no','=','oil_card_vice_trades.vice_no')
            ->select('oil_card_vice_trades.vice_no','oil_card_vice_trades.card_from','oil_card_vice.truck_no',Capsule::raw('count(oil_card_vice_trades.vice_no) as dubiousNum'),
                'oil_org.orgcode','oil_org.org_name','oil_card_vice_trades.oil_com','oil_card_vice.driver_tel as qz_drivertel',
                'oil_card_vice.driver_name as qz_drivername')
            ->groupBy('oil_card_vice_trades.vice_no','oil_card_vice_trades.org_id');

        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = self::getList($params);
            if($data){
                foreach ($data as $k=>&$v){
                    $v->oil_com = OilCom::getOilComById($v->oil_com);
                    $v->consume_type = ConsumeType::getById($v->consume_type);
                    $v->_card_from = CardFrom::getByIdFromG7s($v->card_from);
                }
            }
        } else {
            $data = $sqlObj->orderBy('oil_card_vice_trades.trade_time', 'asc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * oil_trades_out 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilTradesOut::find($params['id']);
    }

    static public function getByIds(array $ids)
    {
        return OilTradesOut::whereIn('id', $ids)->pluck('id');
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilTradesOut::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_trades_out 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilTradesOut::create($params);
    }

    /**
     * oil_trades_out 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilTradesOut::find($params['id'])->update($params);
    }

    /**
     * oil_trades_out 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilTradesOut::destroy($params['ids']);
    }

    /**
     * @title
     * @desc
     * @version 1.0.0
     * <AUTHOR>
     * @package Models
     * @since
     * @params
     * @param array $params
     * @return int
     * @returns
     * {}
     * @returns
     */
    static public function statisticOutTrades(array $params)
    {
        //无用功能
        return 0;
        $outCount = isset($params['count']) ? $params['count'] : 4;
        $minDate = isset($params['minDate']) ? $params['minDate'] : date("Y-m-d", (time() - 15*24*3600))." 00:00:00";
        $maxDate = isset($params['maxDate']) ? $params['maxDate'] : date("Y-m-d")." 23:59:59";
        $oilTypes = OilCom::getNotGssType();

        $sql = "SELECT
GROUP_CONCAT(id) as ids,count(*) as total
FROM
oil_card_vice_trades
WHERE trade_type in (".TradesType::getJiFenAndCashSelfTradeTypeArr(true)."
)
AND trade_time > '" . $minDate . "' AND trade_time <= '".$maxDate."' AND trade_money > 0 AND oil_com in (" . implode(',', $oilTypes) . ")
GROUP BY trade_place,vice_no,DATE_FORMAT(trade_time,'%Y-%m-%d') 
HAVING total >= ".intval($outCount);
        //'加油','IC卡消费','积分加油','油品脱机消费','油品联机消费','优惠油品脱机消费','撬装加油','撬装加油撤销','现金加油','消费','优惠消费'
        $record = Capsule::connection()->select($sql);

        $idArr = [];
        $total = 0;

        if ($record) {
            foreach ($record as $v) {
                if ($v->ids) {
                    $_idArr = explode(",", $v->ids);
                    $idArr = array_merge($idArr, $_idArr);
                }
                $total += $v->total;
            }

            if ($idArr) {
                $idArr = array_unique($idArr);

                //查询出现有的oil_trades_out中是否已存在这些id，如果存在则不对其进行修改
                $existRecords = OilTradesOut::getByIds($idArr);

                if($existRecords){
                    $existRecords = $existRecords->toArray();
                }

                $insertSqlArr = [];
                $page = 0;
                foreach ($idArr as $v) {
                    if(in_array($v, $existRecords)){
                        continue;
                    }
                    if (count($insertSqlArr[$page]) >= 1000) {
                        $page++;
                    }
                    if ($v) {
                        $insertSqlArr[$page][] = "INSERT INTO oil_trades_out (id,createtime) VALUES (" . $v . ", NOW())";
                    }
                }
                if ($insertSqlArr) {
                    foreach ($insertSqlArr as $v) {
                        $insertSql = implode(";", $v);
                        Capsule::connection()->getPdo()->exec($insertSql);
                    }

                }
            }
        }

        return $total;
    }

}
