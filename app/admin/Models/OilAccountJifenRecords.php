<?php
/**
 * 机构积分账户流水表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:17
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilOrg as OilOrg;

class OilAccountJifenRecords extends \Framework\Database\Model
{
    protected $table = 'oil_account_jifen_records';

    protected $guarded = ["id"];

    protected $fillable = ['jifen_id','org_id','main_id','trade_type','jifen','after_jifen','no_type','no','remark','createtime','updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 积分账户
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function AccountJifen()
    {
        return $this->belongsTo('Models\OilAccountJifen','jifen_id','id');
    }

    /**
     * 所属机构
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function Org()
    {
        return $this->belongsTo('Models\OilOrg','org_id','id');
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By jifen_id
        if (isset($params['jifen_id']) && $params['jifen_id'] != '') {
            $query->where('jifen_id', '=', $params['jifen_id']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By orgcode
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $orgIds = OilOrg::select(Capsule::connection()->raw('GROUP_CONCAT(id) as org_ids'))->whereIn('orgcode',explode(',',$params['orgcode']))->first();
            $query->whereIn('org_id', explode(',',$orgIds->org_ids));
        }

        //Search By orgcode_lk
        if (isset($params['orgcode_lk']) && $params['orgcode_lk'] != '') {
            $orgIds = OilOrg::select(Capsule::connection()->raw('GROUP_CONCAT(id) as org_ids'))->where('orgcode','LIKE',$params['orgcode_lk'].'%')->first();
            $query->whereIn('org_id', explode(',',$orgIds->org_ids));
        }

        //Search By main_id
        if (isset($params['main_id']) && $params['main_id'] != '') {
            $query->where('main_id', '=', $params['main_id']);
        }

        //Search By trade_type
        if (isset($params['trade_type']) && $params['trade_type'] != '') {
            $query->where('trade_type', '=', $params['trade_type']);
        }

        //Search By jifen
        if (isset($params['jifen']) && $params['jifen'] != '') {
            $query->where('jifen', '=', $params['jifen']);
        }

        //Search By after_jifen
        if (isset($params['after_jifen']) && $params['after_jifen'] != '') {
            $query->where('after_jifen', '=', $params['after_jifen']);
        }

        //Search By no_type
        if (isset($params['no_type']) && $params['no_type'] != '') {
            $query->where('no_type', '=', $params['no_type']);
        }

        //Search By no
        if (isset($params['no']) && $params['no'] != '') {
            $query->where('no', '=', $params['no']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By createtime
        if (isset($params['query_time_ge']) && $params['query_time_ge'] != '') {
            $query->where('oil_account_jifen_records.createtime', '>=', $params['query_time_ge']);
        }

        //Search By createtime
        if (isset($params['query_time_le']) && $params['query_time_le'] != '') {
            $query->where('oil_account_jifen_records.createtime', '<=', $params['query_time_le']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }


        return $query;
    }

    /**
     * 机构积分账户流水表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilAccountJifenRecords::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 获取账户操作记录
     * @param array $params
     * @return array
     */
    static public function getAccountRecords(array $params)
    {

        if(isset($params['page_size']) && $params['page_size']){
            $params['limit'] = isset($params['page_size']) ? $params['page_size'] : 10;
            $params['page'] = isset($params['page_no']) ? $params['page_no'] : 1;
        }else{
            $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
            $params['page'] = isset($params['page']) ? $params['page'] : 1;
        }

        $data = [];
//        Capsule::connection()->enableQueryLog();
        $sqlObj = OilAccountJifenRecords::Filter($params)
            ->with([
                'AccountJifen',
                'Org'
            ]);

        if(isset($params['sortColumns'])){
            $_sortColumns = explode(" ", $params['sortColumns']);
            $orderField = $_sortColumns[0];
            $orderType = $_sortColumns[1];
        }else{
            $orderField = 'createtime';
            $orderType = 'desc';
        }

        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy($orderField,$orderType)->get();
        }else{
            $data = $sqlObj->orderBy($orderField,$orderType)->paginate($params['limit'],['*'],'page',$params['page']);
        }
//        Framework\Log::dataLog(var_export(Capsule::connection()->getQueryLog(),true),'aaa');

        //数据整理
        foreach($data as $v){
            $v->account_no = '积分账户['.$v->AccountJifen->account_no.']';
            $v->org_name = $v->Org->org_name;
            $v->account_remain = $v->after_jifen;
            if($v->no_type == 'JF'){
                $v->no_type = '充值';
            }else if($v->no_type == 'ZZ'){
                $v->no_type = '转账';
            }else if($v->no_type == 'FP'){
                $v->no_type = '分配';
            }else if($v->no_type == 'FL'){
                $v->no_type = '返利';
            }

            if($v->trade_type == 1){
                $v->income_money = $v->jifen;
                $v->spend_money = '';
            }else if($v->trade_type == '-1'){
                $v->income_money = '';
                $v->spend_money = sprintf("%.2f", $v->jifen * $v->trade_type);
            }
        }
        return $data;
    }

    /**
     * 机构积分账户流水表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountJifenRecords::find($params['id']);
    }

    static public function getByIds(array $ids)
    {
        return OilAccountJifenRecords::whereIn('id', $ids)->get();
    }

    /**
     * 机构积分账户流水表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilAccountJifenRecords::create($params);
    }

    /**
     * 机构积分账户流水表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountJifenRecords::find($params['id'])->update($params);
    }

    /**
     * 机构积分账户流水表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilAccountJifenRecords::destroy($params['ids']);
    }

    static public function getAccountJifenRecord( array $params )
    {
        $connection = 'online_only_read';
        if (API_ENV == 'dev') {
            $connection = "";
        }
        $field = "oil_account_jifen_records.id,oil_account_jifen_records.trade_type,oil_account_jifen_records.createtime,
        oil_account_jifen_records.no_type,oil_account_jifen_records.no,oil_account_jifen_records.jifen,
        oil_account_jifen_records.after_jifen,oil_account_jifen_records.remark_work remark";
        //Capsule::connection($connection)->enableQueryLog();
        $sqlObj = Capsule::connection($connection)->table('oil_account_jifen_records')
            ->leftJoin('oil_org', 'oil_account_jifen_records.org_id','=','oil_org.id')
            ->leftJoin('oil_account_jifen', 'oil_account_jifen.id','=','oil_account_jifen_records.jifen_id')
            ->where('oil_org.is_del',"=",0)
            ->select(Capsule::Raw($field));
        if(isset($params['account_no']) && $params['account_no'] != ''){
            $sqlObj->where('oil_account_jifen.account_no','=',$params['account_no']);
        }
        if(isset($params['createtimeGe']) && $params['createtimeGe'] != ''){
            $sqlObj->where('oil_account_jifen_records.createtime','>=',$params['createtimeGe']);
        }
        if(isset($params['createtimeLe']) && $params['createtimeLe'] != ''){
            $sqlObj->where('oil_account_jifen_records.createtime','<=',$params['createtimeLe']);
        }
        if(isset($params['orgcode']) && $params['orgcode'] != ''){
            $sqlObj->where('oil_org.orgcode','like',$params['orgcode']."%");
        }
        if(isset($params['trade_type']) && $params['trade_type'] != ''){
            $sqlObj->where('oil_account_jifen_records.trade_type','=',$params['trade_type']);
        }
        if(isset($params['no_type']) && $params['no_type'] != ''){
            $sqlObj->where('oil_account_jifen_records.no_type','=',$params['no_type']);
        }
        if(isset($params['no']) && $params['no'] != ''){
            $sqlObj->where('oil_account_jifen_records.no','=',$params['no']);
        }
        //是否g7s展示-积分账户
        if(isset($params['show_g7s']) && $params['show_g7s'] != ''){
            $sqlObj->where('oil_account_jifen_records.show_g7s','=',$params['show_g7s']);
        }
        $data = $sqlObj->orderBy("oil_account_jifen_records.createtime","desc")->get();
        //$sql = Capsule::connection($connection)->getQueryLog();
        //print_r($sql);exit;
        return self::formatData($data);
    }

    static public function formatData(&$data)
    {
        $type = \Fuel\Defines\NoTypeStatus::getJfAll();
        foreach ($data as $v) {
            $v->no_type = isset($type[$v->no_type]) ? $type[$v->no_type] : '';
            unset($v->id);
            unset($v->trade_type);
            $v->money = $v->jifen;
            $v->after_money = $v->after_jifen;
        }
        return $data;
    }



}