<?php
/**
 * 油品副卡日志表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/01/23
 * Time: 15:48:39
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCardViceLog extends \Framework\Database\Model
{
    protected $table = 'oil_card_vice_log';

    protected $guarded = ["id"];
    protected $fillable = ['vice_no','truck_no','driver_tel','org_id','org_id_fanli','vice_password','gsporg_id','user_id','bind_status','remark','remark_work','status','last_operator','createtime','initial_data'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By vice_no
        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $query->where('vice_no', '=', $params['vice_no']);
        }

        //Search By truck_no
        if (isset($params['truck_no']) && $params['truck_no'] != '') {
            $query->where('truck_no', '=', $params['truck_no']);
        }

        //Search By driver_tel
        if (isset($params['driver_tel']) && $params['driver_tel'] != '') {
            $query->where('driver_tel', '=', $params['driver_tel']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By org_id_fanli
        if (isset($params['org_id_fanli']) && $params['org_id_fanli'] != '') {
            $query->where('org_id_fanli', '=', $params['org_id_fanli']);
        }

        //Search By vice_password
        if (isset($params['vice_password']) && $params['vice_password'] != '') {
            $query->where('vice_password', '=', $params['vice_password']);
        }

        //Search By gsporg_id
        if (isset($params['gsporg_id']) && $params['gsporg_id'] != '') {
            $query->where('gsporg_id', '=', $params['gsporg_id']);
        }

        //Search By user_id
        if (isset($params['user_id']) && $params['user_id'] != '') {
            $query->where('user_id', '=', $params['user_id']);
        }

        //Search By bind_status
        if (isset($params['bind_status']) && $params['bind_status'] != '') {
            $query->where('bind_status', '=', $params['bind_status']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By remark_work
        if (isset($params['remark_work']) && $params['remark_work'] != '') {
            $query->where('remark_work', '=', $params['remark_work']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By initial_data
        if (isset($params['initial_data']) && $params['initial_data'] != '') {
            $query->where('initial_data', '=', $params['initial_data']);
        }

        return $query;
    }

    /**
     * 油品副卡日志表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCardViceLog::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 油品副卡日志表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardViceLog::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardViceLog::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 油品副卡日志表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCardViceLog::create($params);
    }

    /**
     * 油品副卡日志表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardViceLog::find($params['id'])->update($params);
    }

    /**
     * 油品副卡日志表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCardViceLog::destroy($params['ids']);
    }




}