<?php
/**
 * oil_operator_day_trades
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2022/11/14
 * Time: 10:01:30
 */

namespace Models;

use Fuel\Defines\TradesType;
use Illuminate\Database\Capsule\Manager as Capsule;
use Jobs\ExportReportDataJob;

class OilOperatorDayTrades extends \Framework\Database\Model
{
    protected $table = 'oil_operator_day_trades';

    protected $guarded = ["id"];
    protected $fillable = ['trade_create_day', 'up_operator_id', 'down_operator_id',
        'oil_base_id', 'mac_amount', 'mac_price', 'trade_money', 'trade_price', 'trade_num',
        'up_fanli_fee', 'down_fanli_fee', 'profit_fee', 'operator_money', 'discount_money',
        'operator_price', 'is_open_invoice', 'receipt_remain', 'batch_no', 'createtime', 'updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By trade_create_day
        if (isset($params['trade_create_day']) && $params['trade_create_day'] != '') {
            $query->where('trade_create_day', '=', $params['trade_create_day']);
        }

        if (isset($params['trade_create_Ge']) && $params['trade_create_Ge']) {
            $query->where('trade_create_day', '>=', $params['trade_create_Ge']);

        }

        if (isset($params['trade_create_Lt']) && $params['trade_create_Lt']) {
            $query->where('trade_create_day', '<', $params['trade_create_Lt']);
        }

        //Search By up_operator_id
        if (isset($params['up_operator_id']) && $params['up_operator_id'] != '') {
            $query->where('up_operator_id', '=', $params['up_operator_id']);
        }

        //Search By down_operator_id
        if (isset($params['down_operator_id']) && $params['down_operator_id'] != '') {
            $query->where('down_operator_id', '=', $params['down_operator_id']);
        }

        //Search By oil_base_id
        if (isset($params['oil_base_id']) && $params['oil_base_id'] != '') {
            $query->where('oil_base_id', '=', $params['oil_base_id']);
        }

        if (isset($params['oil_base_idIn']) && $params['oil_base_idIn'] != '') {
            $query->whereIn('oil_base_id', $params['oil_base_idIn']);
        }

        //Search By mac_amount
        if (isset($params['mac_amount']) && $params['mac_amount'] != '') {
            $query->where('mac_amount', '=', $params['mac_amount']);
        }

        //Search By mac_price
        if (isset($params['mac_price']) && $params['mac_price'] != '') {
            $query->where('mac_price', '=', $params['mac_price']);
        }

        //Search By trade_money
        if (isset($params['trade_money']) && $params['trade_money'] != '') {
            $query->where('trade_money', '=', $params['trade_money']);
        }

        //Search By trade_price
        if (isset($params['trade_price']) && $params['trade_price'] != '') {
            $query->where('trade_price', '=', $params['trade_price']);
        }

        //Search By trade_num
        if (isset($params['trade_num']) && $params['trade_num'] != '') {
            $query->where('trade_num', '=', $params['trade_num']);
        }

        //Search By up_fanli_fee
        if (isset($params['up_fanli_fee']) && $params['up_fanli_fee'] != '') {
            $query->where('up_fanli_fee', '=', $params['up_fanli_fee']);
        }

        //Search By down_fanli_fee
        if (isset($params['down_fanli_fee']) && $params['down_fanli_fee'] != '') {
            $query->where('down_fanli_fee', '=', $params['down_fanli_fee']);
        }

        //Search By profit_fee
        if (isset($params['profit_fee']) && $params['profit_fee'] != '') {
            $query->where('profit_fee', '=', $params['profit_fee']);
        }

        //Search By operator_money
        if (isset($params['operator_money']) && $params['operator_money'] != '') {
            $query->where('operator_money', '=', $params['operator_money']);
        }

        //Search By discount_money
        if (isset($params['discount_money']) && $params['discount_money'] != '') {
            $query->where('discount_money', '=', $params['discount_money']);
        }

        //Search By operator_price
        if (isset($params['operator_price']) && $params['operator_price'] != '') {
            $query->where('operator_price', '=', $params['operator_price']);
        }

        //Search By is_open_invoice
        if (isset($params['is_open_invoice']) && $params['is_open_invoice'] != '') {
            $query->where('is_open_invoice', '=', $params['is_open_invoice']);
        }

        if (isset($params['is_open_invoiceNeq']) && $params['is_open_invoiceNeq'] != '') {
            $query->where('is_open_invoice', '!=', $params['is_open_invoiceNeq']);
        }

        //Search By receipt_remain
        if (isset($params['receipt_remain']) && $params['receipt_remain'] != '') {
            $query->where('receipt_remain', '=', $params['receipt_remain']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        if (isset($params['batch_no_ge']) && $params['batch_no_ge'] != '') {
            $query->where('batch_no', '>=', $params['batch_no_ge']);
        }

        return $query;
    }

    /**
     * oil_operator_day_trades 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilOperatorDayTrades::Filter($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * oil_operator_day_trades 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilOperatorDayTrades::find($params['id']);
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilOperatorDayTrades::lockForUpdate()->where('id', $params['id'])->first();
    }

    /**
     * oil_operator_day_trades 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilOperatorDayTrades::create($params);
    }

    /**
     * oil_operator_day_trades 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilOperatorDayTrades::find($params['id'])->update($params);
    }

    /**
     * oil_operator_day_trades 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilOperatorDayTrades::destroy($params['ids']);
    }

    static public function count($params = [])
    {
        //Capsule::connection()->enableQueryLog();
        $sqlObj = OilOperatorDayTrades::Filter($params);
        $data = $sqlObj->count();
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);
        return $data;
    }

    static public function info($params = [],$isOrder = false)
    {
        $obj = OilOperatorDayTrades::Filter($params);
        if( $isOrder ){
            $obj = $obj->orderBy("trade_create_day", "desc");
        }
        return $obj->first();
    }

    static public function getCanInvoce($params = [])
    {
        //Capsule::connection()->enableQueryLog();
        $sqlObj = OilOperatorDayTrades::Filter($params);
        $sqlObj->selectRaw("batch_no,
	min( trade_create_day ) as min_time,
	max( trade_create_day ) as max_time");
        $data = $sqlObj->groupBy("batch_no")->orderBy("batch_no", "asc")->get();
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        return $data;
    }


    static public function receiptQutoa($params = [])
    {
        $sql = "SELECT
	up_operator_id,
	down_operator_id,
	oil_base_id,
	sum( trade_num ) AS trade_num,
	sum( trade_money ) AS trade_money,
	sum( down_fanli_fee ) AS down_fanli_fee,
	sum( discount_money ) as discount_money,
	sum( profit ) AS profit
FROM
	(
	SELECT
		up_operator_id,
		down_operator_id,
	IF
		( oil_base_id IN ( 9, 10, 20 ), 21, oil_base_id ) oil_base_id,
		sum( trade_num - if(is_open_invoice != 1,trade_num,0) ) AS trade_num,
		sum( trade_money - if(is_open_invoice != 1,trade_money,0) ) AS trade_money,
		sum( down_fanli_fee - if(is_open_invoice != 1,down_fanli_fee,0) ) AS down_fanli_fee,
		sum( discount_money - if(is_open_invoice != 1,discount_money,0) ) as discount_money,
		sum( profit_fee -  if(is_open_invoice != 1,profit_fee,0) ) AS profit
	FROM
		`oil_operator_day_trades` 
	WHERE
	    1 and oil_base_id > 0 ";

        if (isset($params['trade_create_Ge']) && $params['trade_create_Ge']) {
            //todo 需要特殊处理大连传入时分秒
            $sql .= " and trade_create_day >= '" . substr($params['trade_create_Ge'], 0, 10)."'";
        }

        if (isset($params['trade_create_Lt']) && $params['trade_create_Lt']) {
            //todo 需要特殊处理大连传入时分秒
            $sql .= " and trade_create_day < '" . substr($params['trade_create_Lt'], 0, 10) . "'";
        }

        //Search By up_operator_id
        if (isset($params['up_operator_id']) && $params['up_operator_id'] != '') {
            $sql .= " and up_operator_id = " . $params['up_operator_id'];
        }

        //Search By down_operator_id
        if (isset($params['down_operator_id']) && $params['down_operator_id'] != '') {
            $sql .= " and down_operator_id = " . $params['down_operator_id'];
        }

        if (isset($params['oil_base_idIn']) && $params['oil_base_idIn'] != '') {
            $sql .= " and oil_base_id in (" . implode(",", $params['oil_base_idIn']) . ")";
        }
        $sql .= "	GROUP BY
		`up_operator_id`,
		`down_operator_id`,
		`oil_base_id` 
	) a 
GROUP BY
	`up_operator_id`,
	`down_operator_id`,
	`oil_base_id`";
        $data = Capsule::connection()->select($sql);
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        return $data;
    }

    /**
     * Desc: 剩余当天的数据
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 28/12/22 下午4:07
     * @param array $params
     * @return array
     */
    static public function receiptDayQutoa($params = [])
    {
        $sql = "SELECT
	t.up_operator_id,
	t.down_operator_id,
	LEFT ( t.trade_createtime, 10 ) AS 'trade_create_day',
	n.oil_base_id,
	sum( t.mac_amount ) AS mac_amount,
	sum( t.trade_num ) AS trade_num,
	sum( t.trade_money ) AS trade_money,
	sum( t.mac_amount ) / sum( t.real_oil_num ) AS mac_price,
	sum( t.trade_money ) / sum( t.real_oil_num ) AS trade_price,
	sum( r.final_straight_down_rebate + r.final_after_rebate + r.mark_rebate ) AS 'up_fanli',
	sum( r.down_cal_rebate ) AS 'down_fanli',
	sum( t.mac_amount - t.trade_money ) AS 'down_direct_fanli' 
FROM
	oil_trades AS t
	LEFT JOIN oil_card_vice_trade_rebate AS r ON r.trade_id = t.trades_id
	LEFT JOIN oil_type_no AS n ON t.oil_name = n.oil_no 
WHERE
	t.trade_type IN ( ".TradesType::getCashAndSelfOilTradeTypeArr(true,true)." ) ";

        if (isset($params['trade_create_Ge']) && $params['trade_create_Ge']) {
            //todo 需要特殊处理大连传入时分秒
            $sql .= " and t.trade_createtime > '" .$params['trade_create_Ge']."'";
        }

        if (isset($params['trade_create_Lt']) && $params['trade_create_Lt']) {
            //todo 需要特殊处理大连传入时分秒
            $sql .= " and t.trade_createtime < '" . substr($params['trade_create_Lt'], 0, 10) . "'";
        }

        //Search By up_operator_id
        if (isset($params['up_operator_id']) && $params['up_operator_id'] != '') {
            $sql .= " and t.up_operator_id = " . $params['up_operator_id'];
        }

        //Search By down_operator_id
        if (isset($params['down_operator_id']) && $params['down_operator_id'] != '') {
            $sql .= " and t.down_operator_id = " . $params['down_operator_id'];
        }

        if (isset($params['oil_base_idIn']) && $params['oil_base_idIn'] != '') {
            $sql .= " and n.oil_base_id in (" . implode(",", $params['oil_base_idIn']) . ")";
        }

        $exceipt = (new ExportReportDataJob())->exceiptOrg;
        $sql .=" AND t.top_orgcode NOT IN ('".implode("','",$exceipt)."')
    GROUP BY
	t.up_operator_id,
	t.down_operator_id,
	LEFT ( t.trade_createtime, 10 );";
        $data = Capsule::connection()->select($sql);
        //$sql = Capsule::connection()->getQueryLog();
        print_r($sql);
        return $data;
    }

    static public function getUpDownOperatorId()
    {
        $sql = "select up_operator_id,down_operator_id from oil_operator_day_trades where oil_base_id > 0 GROUP BY up_operator_id,down_operator_id;";
        return Capsule::connection('slave')->select($sql);
    }

}