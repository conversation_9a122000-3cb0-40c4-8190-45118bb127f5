<?php
/**
 * oil_credit_payments
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/07/30
 * Time: 12:00:04
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCreditPayments extends \Framework\Database\Model
{
    protected $table = 'oil_credit_payments';

    protected $guarded = ["id"];
    protected $fillable = ['api_id','bill_no','product_code','credit_product','orgcode','org_name','bill_begin_time','bill_end_time','bills_num','bills_total_fee','payback_time','arrive_fee','receive_num','receive_total_fee','bill_status','remark','createtime','updatetime','last_operator_id','last_operator'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By api_id
        if (isset($params['api_id']) && $params['api_id'] != '') {
            $query->where('api_id', '=', $params['api_id']);
        }

        //Search By bill_no
        if (isset($params['bill_no']) && $params['bill_no'] != '') {
            $query->where('bill_no', '=', $params['bill_no']);
        }

        //Search By product_code
        if (isset($params['product_code']) && $params['product_code'] != '') {
            $query->where('product_code', '=', $params['product_code']);
        }

        //Search By credit_product
        if (isset($params['credit_product']) && $params['credit_product'] != '') {
            $query->where('credit_product', '=', $params['credit_product']);
        }

        //Search By orgcode
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $query->where('orgcode', '=', $params['orgcode']);
        }

        //Search By org_name
        if (isset($params['org_name']) && $params['org_name'] != '') {
            $query->where('org_name', '=', $params['org_name']);
        }

        //Search By bill_begin_time
        if (isset($params['bill_begin_time']) && $params['bill_begin_time'] != '') {
            $query->where('bill_begin_time', '=', $params['bill_begin_time']);
        }

        //Search By day_start_name
        if (isset($params['day_start_name']) && $params['day_start_name'] != '') {
            $query->where('bill_begin_time', '>=', $params['day_start_name']);
        }

        //Search By day_end_name
        if (isset($params['day_end_name']) && $params['day_end_name'] != '') {
            $query->where('bill_begin_time', '<=', $params['day_end_name']." 23:59:59");
        }

        //Search By bill_end_time
        if (isset($params['bill_end_time']) && $params['bill_end_time'] != '') {
            $query->where('bill_end_time', '=', $params['bill_end_time']);
        }

        //Search By bills_num
        if (isset($params['bills_num']) && $params['bills_num'] != '') {
            $query->where('bills_num', '=', $params['bills_num']);
        }

        //Search By bills_total_fee
        if (isset($params['bills_total_fee']) && $params['bills_total_fee'] != '') {
            $query->where('bills_total_fee', '=', $params['bills_total_fee']);
        }

        //Search By payback_time
        if (isset($params['payback_time']) && $params['payback_time'] != '') {
            $query->where('payback_time', '=', $params['payback_time']);
        }

        //Search By arrive_fee
        if (isset($params['arrive_fee']) && $params['arrive_fee'] != '') {
            $query->where('arrive_fee', '=', $params['arrive_fee']);
        }

        //Search By receive_num
        if (isset($params['receive_num']) && $params['receive_num'] != '') {
            $query->where('receive_num', '=', $params['receive_num']);
        }

        //Search By receive_total_fee
        if (isset($params['receive_total_fee']) && $params['receive_total_fee'] != '') {
            $query->where('receive_total_fee', '=', $params['receive_total_fee']);
        }

        //Search By bill_status
        if (isset($params['bill_status']) && $params['bill_status'] != '') {
            $query->where('bill_status', '=', $params['bill_status']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By last_operator_id
        if (isset($params['last_operator_id']) && $params['last_operator_id'] != '') {
            $query->where('last_operator_id', '=', $params['last_operator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        return $query;
    }

    /**
     * oil_credit_payments 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCreditPayments::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }elseif(isset($params['_count']) && $params['_count'] == 1){
            return $sqlObj->count();
        }elseif (isset($params['skip']) && isset($params['take'])) {
            $data = $sqlObj->orderBy('createtime', 'desc')->skip(intval($params['skip']))->take(intval($params['take']))->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }
        foreach ($data as $key => &$val){
            $val->remark = $val->remark ? $val->remark : "";
            $val->bill_begin_time = date("Y-m-d",strtotime($val->bill_begin_time));
            $val->bill_end_time = date("Y-m-d",strtotime($val->bill_end_time));
            switch ($val->bill_status){
                case 1:
                    $val->status_txt = "已确认";
                    break;
                default:
                    $val->status_txt = "未确认";
            }
        }

        return $data;
    }

    /**
     * oil_credit_payments 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCreditPayments::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCreditPayments::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByApiIdLock(array $params)
    {
        \helper::argumentCheck(['api_id','product_name'],$params);

        return OilCreditPayments::lockForUpdate()->where('api_id',$params['api_id'])->where('credit_product',$params['product_name'])->first();
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getDataByOrg(array $params)
    {
        \helper::argumentCheck(['orgcode','bill_time','product_code'],$params);

        return OilCreditPayments::where('orgcode',$params['orgcode'])->where('bill_begin_time',$params['bill_time'])->where('product_code',$params['product_code'])->first();
    }

    /**
     * oil_credit_payments 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCreditPayments::create($params);
    }

    /**
     * oil_credit_payments 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCreditPayments::find($params['id'])->update($params);
    }

    /**
     * oil_credit_payments 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCreditPayments::destroy($params['ids']);
    }


    /**
     * 生成订单号
     * @param array $params
     * @return mixed
     */
    static public function createBillNo($flag = "HK")
    {
        $microTime = microtime();
        $microArr  = explode(" ", $microTime);
        $str       = $flag . date("ymds", time());
        $str       .= substr($microArr[0], 3, 5);
        $str .= sprintf('%02d', rand(0, 99));

        $exist = OilCreditPayments::where("bill_no", "=", $str)->lockForUpdate()->first();
        if ($exist) {
            self::createOrderNo($flag);
        }

        return $str;
    }

}