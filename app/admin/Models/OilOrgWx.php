<?php
/**
 * oil_org_wx
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/06/15
 * Time: 09:46:59
 */
namespace Models;
use Framework\Config;
use Illuminate\Database\Capsule\Manager as Capsule;
use Framework\Log;

class OilOrgWx extends \Framework\Database\Model
{
    protected $table = 'oil_org_wx';

    protected $guarded = ["id"];

    protected $fillable = ['orgcode','userid','open_id','nickname','avatar','sex','province','city','org_id','status','is_del','createtime','updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By orgcode
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $query->where('orgcode', '=', $params['orgcode']);
        }

        //Search By userid
        if (isset($params['userid']) && $params['userid'] != '') {
            $query->where('userid', '=', $params['userid']);
        }

        //Search By open_id
        if (isset($params['open_id']) && $params['open_id'] != '') {
            $query->where('open_id', '=', $params['open_id']);
        }

        //Search By nickname
        if (isset($params['nickname']) && $params['nickname'] != '') {
            $query->where('nickname', '=', $params['nickname']);
        }

        //Search By avatar
        if (isset($params['avatar']) && $params['avatar'] != '') {
            $query->where('avatar', '=', $params['avatar']);
        }

        //Search By sex
        if (isset($params['sex']) && $params['sex'] != '') {
            $query->where('sex', '=', $params['sex']);
        }

        //Search By province
        if (isset($params['province']) && $params['province'] != '') {
            $query->where('province', '=', $params['province']);
        }

        //Search By city
        if (isset($params['city']) && $params['city'] != '') {
            $query->where('city', '=', $params['city']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By is_del
        if (isset($params['is_del']) && $params['is_del'] != '') {
            $query->where('is_del', '=', $params['is_del']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }


        return $query;
    }

    /**
     * oil_org_wx 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilOrgWx::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_org_wx 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgWx::find($params['id']);
    }

    /**
     * oil_org_wx 详情查询
     * @param array $params
     * @return object
     */
    static public function getByOpenId(array $params)
    {
        \helper::argumentCheck(['open_id'],$params);

        return OilOrgWx::where('open_id','=',$params['open_id'])
            ->where('org_id','=',$params['org_id'])
            ->first();
    }

    /**
     * oil_org_wx 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilOrgWx::create($params);
    }

    /**
     * oil_org_wx 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgWx::find($params['id'])->update($params);
    }

    /**
     * oil_org_wx 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilOrgWx::destroy($params['ids']);
    }

    /**
     * tempdelete
     */
    static public function tempRemove(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgWx::where('id','=',$params['id'])->update(['is_del'=>2]);
    }

    /**
     * getByOrgcode and stats eq 2
     */
    static public function getOpenIdByOrgId(array $params)
    {
        $data = null;
        \helper::argumentCheck(['org_id'],$params);
        $list = OilOrgWx::where('org_id','=',$params['org_id'])
            ->where('status','=',2)
            ->get();
        if($list){
            foreach($list as $v){
                $data[] = $v->open_id;
            }
        }
        return $data;
    }

    /**
     * 发送变动通知
     */
    static public function sendNotify(array $params)
    {
        //验证
        \helper::argumentCheck(['org_id','cash_type','money','blance_money'],$params);

        //获得哦喷的openid
        $openIds = OilOrgWx::getOpenIdByOrgId(['org_id'=>$params['org_id']]);
        Log::error('$openIds', [$openIds],'oilOrgWx');
        if($openIds){
            $account_type = isset($params['account_type']) && $params['account_type'] ? $params['account_type'] : '现金账户';
            $remark = $params['remark'] ? $params['remark'] : "";

            //发送
            foreach($openIds as $v){
                Log::error('$v', [$v],'oilOrgWx');
                try{
                    $res = \Fuel\Request\GasClient::post([
                        'data'   => [
                            'templateId'    =>  Config::get('wechatNotify.templateMessages.balanceChange'),
                            'openIds'   => [$v],
                            'data'  =>  [
                                'first'  => "尊敬的G7油品用户，您的\"".$account_type."\"发生以下变动\n",
                                'keyword1'    =>  date("Y-m-d H:i:s"),
                                'keyword2' =>  $params['cash_type'],
                                'keyword3' =>  '￥'.$params['money'],
                                'keyword4'  =>  '￥'.$params['blance_money']."\n",
                                'remark'    =>  $remark
                            ]
                        ],
                        'method' => 'gas.api.sendTemplateMsgByOpenIds'
                    ]);
                }catch (\Exception $e){
                    Log::error('sendNotify'.strval($e), [$v],'oilOrgWx');
                }

            }
//            $res = \Fuel\Request\GasClient::post([
//                'data'   => [
//                    'templateId'    =>  Config::get('wechatNotify.templateMessages.balanceChange'),
//                    'openIds'   => $openIds,
//                    'data'  =>  [
//                        'first'  => "尊敬的G7油品用户，您的\"".$account_type."\"发生以下变动\n",
//                        'keyword1'    =>  date("Y-m-d H:i:s"),
//                        'keyword2' =>  $params['cash_type'],
//                        'keyword3' =>  '￥'.$params['money'],
//                        'keyword4'  =>  '￥'.$params['blance_money']."\n",
//                        'remark'    =>  $remark
//                    ]
//                ],
//                'method' => 'gas.api.sendTemplateMsgByOpenIds'
//            ]);

            Log::notice('sendNotify', [$res,$openIds],'oilOrgWx');
        }

        return true;

    }

    /**
     * 发送余额不足通知
     */
    static public function sendMoneyLackNotify(array $params)
    {
        //验证
        \helper::argumentCheck(['org_id','blance_money'],$params);

        //获得openid
        $openids = OilOrgWx::getOpenIdByOrgId(['org_id'=>$params['org_id']]);

        if($openids){
            //发送
            $res = \Fuel\Request\GasClient::post([
                'data'   => [
                    'templateId'    =>  Config::get('wechatNotify.templateMessages.balanceLack'),
                    'openIds'   =>  $openids,
                    'data'  =>  [
                        'first' => "尊敬的G7油品用户，您的账户余额已不足\n",
                        'keyword2'  =>  date("Y-m-d H:i:s")."\n",
                        'keyword1'  =>  '￥'.$params['blance_money'],
                        'remark'    => '为了不影响您的正常使用，请及时充值.'
                    ]
                ],
                'method' => 'gas.api.sendTemplateMsgByOpenIds'
            ]);

            Log::debug('weixin-sendMoneyLackNotify',['result'=>$res,'params'=>$params],'oilOrgWx');

        }

        return true;

    }

    /**
     * 发送首次托管完成通知
     */
    static public function sendFinishNotify(array $params)
    {
        //验证
        \helper::argumentCheck(['account_no','oil_com','org_id'],$params);

        //获得openid
        $openids = OilOrgWx::getOpenIdByOrgId(['org_id'=>$params['org_id']]);

        if($openids){
            //发送
            $res = \Fuel\Request\GasClient::post([
                'data'   => [
                    'templateId'    =>  Config::get('wechatNotify.templateMessages.customerFinish'),
                    'openIds'   =>  $openids,
                    'data'  =>  [
                        'first' => "尊敬的用户，您的托管账号".$params['account_no']."下的油卡已同步完成！为了油卡数据更加准确，快去用油管理系统-油品管理-卡片管理绑卡吧！\n",
                        'keyword2'  =>  $params['oil_com']."托管有卡同步\n",
                        'keyword1'  =>  date("Y-m-d H:i:s"),
                        'remark'    => '如有问题，可随时联系在线客服为您解答！'
                    ]
                ],
                'method' => 'gas.api.sendTemplateMsgByOpenIds'
            ]);

            Log::debug('weixin-sendMoneyLackNotify',['result'=>$res,'params'=>$params],'oilOrgWx');

        }

        return true;

    }

    /**
     * 单个openId发送
     * @param $openIds
     * @param callable|NULL $callBack
     * @return null
     */
    static public function sendNotifyOneByOne($openIds, callable $callBack = NULL)
    {
        $data = null;
        if($openIds){
            foreach($openIds as $v){
                if($callBack){
                    $callBack($v);
                }
            }
        }

        return $data;
    }

    /**
     * 按照openId删除
     * @param null $openId
     * @return null
     */
    static public function removeByOpenId($openId = NULL)
    {
        $data = NULL;
        if($openId){
            $data = OilOrgWx::where('open_id', '=', $openId)->delete();
        }

        return $data;
    }

}