<?php
/**
 * 油品机构收卡地址
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:18
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilOrgAddr extends \Framework\Database\Model
{
    protected $table = 'oil_org_addr';

    protected $guarded = ["id"];

    protected $fillable = ['org_id','name','mobile','province_id','province_name','city_id','city_name','district_id',
        'district_name','address','addr_zip','remark','creator_id','last_operator','other_operator_id','other_operator',
        'createtime','updatetime','is_del','other_creator_id','other_creator','card_default','invoice_default','gos_id'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By name
        if (isset($params['name']) && $params['name'] != '') {
            $query->where('name', '=', $params['name']);
        }

        //Search By mobile
        if (isset($params['mobile']) && $params['mobile'] != '') {
            $query->where('mobile', '=', $params['mobile']);
        }

        //Search By province_jd
        if (isset($params['province_jd']) && $params['province_jd'] != '') {
            $query->where('province_jd', '=', $params['province_jd']);
        }

        //Search By province_name
        if (isset($params['province_name']) && $params['province_name'] != '') {
            $query->where('province_name', '=', $params['province_name']);
        }

        //Search By city_id
        if (isset($params['city_id']) && $params['city_id'] != '') {
            $query->where('city_id', '=', $params['city_id']);
        }

        //Search By city_name
        if (isset($params['city_name']) && $params['city_name'] != '') {
            $query->where('city_name', '=', $params['city_name']);
        }

        //Search By district_id
        if (isset($params['district_id']) && $params['district_id'] != '') {
            $query->where('district_id', '=', $params['district_id']);
        }

        //Search By district_name
        if (isset($params['district_name']) && $params['district_name'] != '') {
            $query->where('district_name', '=', $params['district_name']);
        }

        //Search By address
        if (isset($params['address']) && $params['address'] != '') {
            $query->where('address', 'like', '%'.$params['address'].'%');
        }

        //Search By addr_zip
        if (isset($params['addr_zip']) && $params['addr_zip'] != '') {
            $query->where('addr_zip', '=', $params['addr_zip']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By other_operator_id
        if (isset($params['other_operator_id']) && $params['other_operator_id'] != '') {
            $query->where('other_operator_id', '=', $params['other_operator_id']);
        }

        //Search By other_operator
        if (isset($params['other_operator']) && $params['other_operator'] != '') {
            $query->where('other_operator', '=', $params['other_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By is_del
        if (isset($params['is_del']) && $params['is_del'] != '') {
            $query->where('is_del', '=', $params['is_del']);
        }

        //Search By other_creator_id
        if (isset($params['other_creator_id']) && $params['other_creator_id'] != '') {
            $query->where('other_creator_id', '=', $params['other_creator_id']);
        }

        //Search By other_creator
        if (isset($params['other_creator']) && $params['other_creator'] != '') {
            $query->where('other_creator', '=', $params['other_creator']);
        }

        //Search By card_default
        if (isset($params['card_default']) && $params['card_default'] != '') {
            $query->where('card_default', '=', $params['card_default']);
        }

        //Search By invoice_default
        if (isset($params['invoice_default']) && $params['invoice_default'] != '') {
            $query->where('invoice_default', '=', $params['invoice_default']);
        }


        return $query;
    }

    /**
     * 油品机构收卡地址 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        
        Capsule::connection()->enableQueryLog();
        $sqlObj = OilOrgAddr::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->where('is_del','=',0)->orderBy('createtime','desc')->get();
        }else{
            $data = $sqlObj->where('is_del','=',0)->orderBy('createtime','desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 油品机构收卡地址 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgAddr::find($params['id']);
    }

    /**
     * 油品机构收卡地址 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilOrgAddr::create($params);
    }

    /**
     * 油品机构收卡地址 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgAddr::find(intval($params['id']))->update($params);

//        return OilOrgAddr::find($params['id'])->update($params);
    }

    /**
     * 油品机构收卡地址 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilOrgAddr::destroy($params['ids']);
    }

    /**
     * 假删
     * @param array $params
     * @return int
     */
    static public function tempRemove(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgAddr::find($params['id'])->update(['is_del'=>1]);
    }

    /**
     * 修改油卡邮递地址为默认
     *
     * @param array $params
     * @return bool
     */
    static function updateDefaultForCard(array $params)
    {
        \helper::argumentCheck(['id','org_id'],$params);
        OilOrgAddr::where('id','=',$params['id'])->where('org_id','=',$params['org_id'])->update(['card_default'=>1]);
        OilOrgAddr::where('id','!=',$params['id'])->where('org_id','=',$params['org_id'])->update(['card_default'=>0]);

        return true;
    }

    /**
     * 修改发票邮递地址为默认
     *
     * @param array $params
     * @return bool
     */
    static public function updateDefaultForInvoice(array $params)
    {
        \helper::argumentCheck(['id','org_id'],$params);
        OilOrgAddr::where('id','=',$params['id'])->where('org_id','=',$params['org_id'])->update(['invoice_default'=>1]);
        OilOrgAddr::where('id','!=',$params['id'])->where('org_id','=',$params['org_id'])->update(['invoice_default'=>0]);

        return true;
    }
    
    /**
     * 根据条件取信息
     * @param array $params
     * @return object
     */
    static public function getInfoByFilter(array $params)
    {
        return OilOrgAddr::Filter($params)->first();
    }
}