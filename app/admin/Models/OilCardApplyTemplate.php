<?php
/**
 * oil_card_apply_template
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/07/11
 * Time: 16:54:42
 */
namespace Models;
use Fuel\Defines\OilCom;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCardApplyTemplate extends \Framework\Database\Model
{
    protected $table = 'oil_card_apply_template';

    protected $guarded = ["id"];
    protected $fillable = ['qr_uuid','name','oil_com','org_id','org_id_fanli','account_no','paylimit','day_top','oil_top',
        'month_top','set_passwd','vice_password','status','data_from','createtime','updatetime','creator_id','creator_name',
        'last_operator_id','last_operator','able_transfer'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By qr_uuid
        if (isset($params['qr_uuid']) && $params['qr_uuid'] != '') {
            $query->where('qr_uuid', '=', $params['qr_uuid']);
        }

        //Search By name
        if (isset($params['name']) && $params['name'] != '') {
            $query->where('name', '=', $params['name']);
        }

        //Search By oil_com
        if (isset($params['oil_com']) && $params['oil_com'] != '') {
            $query->where('oil_com', '=', $params['oil_com']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By org_id_fanli
        if (isset($params['org_id_fanli']) && $params['org_id_fanli'] != '') {
            $query->where('org_id_fanli', '=', $params['org_id_fanli']);
        }

        //Search By paylimit
        if (isset($params['paylimit']) && $params['paylimit'] != '') {
            $query->where('paylimit', '=', $params['paylimit']);
        }

        //Search By day_top
        if (isset($params['day_top']) && $params['day_top'] != '') {
            $query->where('day_top', '=', $params['day_top']);
        }

        //Search By oil_top
        if (isset($params['oil_top']) && $params['oil_top'] != '') {
            $query->where('oil_top', '=', $params['oil_top']);
        }

        //Search By month_top
        if (isset($params['month_top']) && $params['month_top'] != '') {
            $query->where('month_top', '=', $params['month_top']);
        }

        //Search By set_passwd
        if (isset($params['set_passwd']) && $params['set_passwd'] != '') {
            $query->where('set_passwd', '=', $params['set_passwd']);
        }

        //Search By vice_password
        if (isset($params['vice_password']) && $params['vice_password'] != '') {
            $query->where('vice_password', '=', $params['vice_password']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By data_from
        if (isset($params['data_from']) && $params['data_from'] != '') {
            $query->where('data_from', '=', $params['data_from']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By creator_name
        if (isset($params['creator_name']) && $params['creator_name'] != '') {
            $query->where('creator_name', '=', $params['creator_name']);
        }

        //Search By last_operator_id
        if (isset($params['last_operator_id']) && $params['last_operator_id'] != '') {
            $query->where('last_operator_id', '=', $params['last_operator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        return $query;
    }

    /**
     * oil_card_apply_template 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCardApplyTemplate::Filter($params);
        if (isset($params['count']) && $params['count'] == 1) {
            return $sqlObj->count();
        } elseif (isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        } elseif (isset($params['skip']) && isset($params['take'])) {
            $data = $sqlObj->skip(intval($params['skip']))->take(intval($params['take']))->get();
        } else {
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }
        //格式化数据
        $data = self::formatData($data);

        return $data;
    }

    /**
     * @title   格式化数据
     * @desc
     * @version
     * @level   1
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param $data
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static private function formatData($data)
    {
        foreach ($data as &$v) {
            $v->_oil_com = OilCom::getById($v->oil_com)['name'];
            $v->status_name = $v->status == 1 ? '正常' : '作废';
            $v->passwd_txt = $v->set_passwd == 2 ? "免密" : $v->vice_password;
            $v->uuid = $v->qr_uuid;
            $v->id = null;
            $v->qr_uuid = null;
            $orgInfo = OilOrg::getById(["id"=>$v->org_id]);
            $v->org_name = $orgInfo->org_name;
            $fanliOrgInfo = OilOrg::getById(['id'=>$v->org_id_fanli]);
            $v->fanli_org_name = $fanliOrgInfo->org_name;
        }
        return $data;
    }

    /**
     * oil_card_apply_template 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardApplyTemplate::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardApplyTemplate::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByUuidLock(array $params)
    {
        \helper::argumentCheck(['uuid'],$params);

        return OilCardApplyTemplate::lockForUpdate()->where('qr_uuid',$params['uuid'])->first();
    }

    /**
     * oil_card_apply_template 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCardApplyTemplate::create($params);
    }

    /**
     * oil_card_apply_template 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardApplyTemplate::find($params['id'])->update($params);
    }

    /**
     * oil_card_apply_template 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCardApplyTemplate::destroy($params['ids']);
    }




}