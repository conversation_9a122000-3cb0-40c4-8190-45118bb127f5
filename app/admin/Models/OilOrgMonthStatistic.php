<?php
/**
 * oil_org_month_statistic
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/11/14
 * Time: 10:18:56
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilOrgMonthStatistic extends \Framework\Database\Model
{
    protected $table = 'oil_org_month_statistic';

    protected $guarded = ["id"];
    protected $fillable = ['org_id', 'orgcode', 'org_name', 'dates', 'charge_total', 'assign_total', 'total_trade_money', 'total_trade_num', 'total_counts',
        'oilcom_data', 'no_trades_total', 'attachment', 'createtime', 'updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By orgcode
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $query->where('orgcode', '=', $params['orgcode']);
        }

        //Search By org_name
        if (isset($params['org_name']) && $params['org_name'] != '') {
            $query->where('org_name', '=', $params['org_name']);
        }

        //Search By dates
        if (isset($params['dates']) && $params['dates'] != '') {
            $query->where('dates', '=', $params['dates']);
        }

        //Search By charge_total
        if (isset($params['charge_total']) && $params['charge_total'] != '') {
            $query->where('charge_total', '=', $params['charge_total']);
        }

        //Search By assign_total
        if (isset($params['assign_total']) && $params['assign_total'] != '') {
            $query->where('assign_total', '=', $params['assign_total']);
        }

        //Search By total_trade_money
        if (isset($params['total_trade_money']) && $params['total_trade_money'] != '') {
            $query->where('total_trade_money', '=', $params['total_trade_money']);
        }

        //Search By total_trade_num
        if (isset($params['total_trade_num']) && $params['total_trade_num'] != '') {
            $query->where('total_trade_num', '=', $params['total_trade_num']);
        }

        //Search By total_counts
        if (isset($params['total_counts']) && $params['total_counts'] != '') {
            $query->where('total_counts', '=', $params['total_counts']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * oil_org_month_statistic 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilOrgMonthStatistic::Filter($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * oil_org_month_statistic 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilOrgMonthStatistic::find($params['id']);
    }

    /**
     * oil_org_month_statistic 详情查询
     * @param array $params
     * @return object
     */
    static public function getByLikeOrgCodeMonth(array $params)
    {
        \helper::argumentCheck(['orgcode', 'month'], $params);
        $params['month'] = date('Ym', strtotime($params['month']));

        return OilOrgMonthStatistic::where('orgcode', 'like', $params['orgcode'] . '%')->where('dates', '=', $params['month'])->get();
    }

    /**
     * oil_org_month_statistic 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilOrgMonthStatistic::create($params);
    }

    /**
     * oil_org_month_statistic 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilOrgMonthStatistic::find($params['id'])->update($params);
    }

    /**
     * oil_org_month_statistic 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilOrgMonthStatistic::destroy($params['ids']);
    }


    /**
     * updateByOrgCodeMonth
     * @param array $params
     * @return mixed
     * <AUTHOR>
     * @since ${DATE}
     */
    static public function updateByOrgCodeMonth(array $params)
    {
        if (!isset($params['orgcode']) || !isset($params['dates'])) {
            throw new \RuntimeException('orgcode Or dates does not exist!', 2);
        }

        return OilOrgMonthStatistic::where('orgcode', '=', $params['orgcode'])->where('dates', '=', $params['dates'])->update($params);
    }

    /**
     * updateByOrgIdMonth
     * @param array $params
     * @return mixed
     * <AUTHOR>
     * @since ${DATE}
     */
    static public function updateByOrgIdMonth(array $params)
    {
        if (!isset($params['org_id']) || !isset($params['org_id'])) {
            throw new \RuntimeException('org_id Or dates does not exist!', 2);
        }

        return OilOrgMonthStatistic::where('org_id', '=', $params['org_id'])->where('dates', '=', $params['dates'])->update($params);
    }

    /**
     * getByOrgCodeMonth
     * @param array $params
     * @return mixed
     * <AUTHOR>
     * @since ${DATE}
     */
    static public function getByOrgCodeMonth(array $params)
    {
        if (!isset($params['orgcode']) || !isset($params['month'])) {
            throw new \RuntimeException('orgcode Or month does not exist!', 2);
        }

        return OilOrgMonthStatistic::where('orgcode', '=', $params['orgcode'])->where('dates', '=', $params['month'])->first();
    }

    /**
     * isExistDate
     * @param array $params
     * @return mixed
     * <AUTHOR>
     * @since ${DATE}
     */
    static public function isExistDate(array $params)
    {
        return OilOrgMonthStatistic::where('dates', '=', $params['month'])->first();
    }

    /**
     * 获取统计年月
     */
    static public function getStatisticDate()
    {
        return OilOrgMonthStatistic::select('dates')->distinct()->orderBy('dates','desc')->get();
    }
}