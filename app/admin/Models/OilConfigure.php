<?php
/**
 * 系统设置
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2017/08/30
 * Time: 16:15:01
 */

namespace Models;

use Illuminate\Database\Capsule\Manager as Capsule;

class OilConfigure extends \Framework\Database\Model
{
    protected $table = 'oil_configure';

    protected $guarded  = ["id"];
    protected $fillable = ['name', 'sys_key', 'sys_value', 'createuser', 'updateuser', 'createtime', 'updatetime', 'status'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By name
        if (isset($params['name']) && $params['name'] != '') {
            $query->where('name', '=', $params['name']);
        }

        //Search By sys_key
        if (isset($params['sys_key']) && $params['sys_key'] != '') {
            if(is_array($params['sys_key'])){
                $query->whereIn('sys_key',$params['sys_key']);
            }else{
                $query->where('sys_key', '=', $params['sys_key']);
            }
        }

        //Search By sys_value
        if (isset($params['sys_value']) && $params['sys_value'] != '') {
            $query->where('sys_value', '=', $params['sys_value']);
        }

        //Search By createuser
        if (isset($params['createuser']) && $params['createuser'] != '') {
            $query->where('createuser', '=', $params['createuser']);
        }

        //Search By updateuser
        if (isset($params['updateuser']) && $params['updateuser'] != '') {
            $query->where('updateuser', '=', $params['updateuser']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 系统设置 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page']  = isset($params['page']) ? $params['page'] : 1;
        $sqlObj          = OilConfigure::Filter($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * 系统设置 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilConfigure::find($params['id']);
    }

    /**
     * @title 系统设置 详情查询
     * @desc
     * @version
     * @level   1
     * <AUTHOR>
     * @returns
     */
    static public function getBySysKey($key)
    {
        $data = OilConfigure::where('sys_key', $key)->first();

        return $data ? $data->sys_value : null;
    }

    /**
     * @title 系统设置 详情查询
     * @desc
     * @version
     * @level   1
     * <AUTHOR>
     * @returns
     */
    static public function getSysInfo(array $params)
    {
        $data = OilConfigure::Filter($params)->first();

        return $data;
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilConfigure::lockForUpdate()->where('id', $params['id'])->first();
    }

    /**
     * 系统设置 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilConfigure::create($params);
    }

    /**
     * 系统设置 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilConfigure::find($params['id'])->update($params);
    }

    /**
     * 系统设置 编辑
     * @param array $params
     * @return mixed
     */
    static public function editbyKey(array $params)
    {
        \helper::argumentCheck(['sys_key'], $params);

        return OilConfigure::where('sys_key', $params['sys_key'])->update($params);
    }

    /**
     * 系统设置 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilConfigure::destroy($params['ids']);
    }

    public static function getByName($name)
    {
        return self::where("name", $name)->where("sys_value", 1)->pluck("sys_key","id")->toArray();
    }

    //根据名称，获取配置列表
    public static function getConfByName($params = [],$isMap = true)
    {
        $result = [];
        $data = self::Filter($params)->selectRaw("id,sys_value,sys_key")->get();
        if($isMap) {
            foreach ($data as $_val) {
                $result[$_val->id] = $_val->sys_value;
            }
        }else{
            $result = $data;
        }
        return $result;
    }

    public static function getByPluckField($params,$field)
    {
        return self::Filter($params)->pluck($field)->toArray();
    }

}