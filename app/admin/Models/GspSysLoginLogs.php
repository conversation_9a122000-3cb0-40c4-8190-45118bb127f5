<?php
/**
 * 系统登录日志表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:16
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class GspSysLoginLogs extends \Framework\Database\Model
{
    protected $table = 'gsp_sys_login_logs';

    protected $guarded = ["id"];

    protected $fillable = ['user_id','login_ip','createtime','updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By user_id
        if (isset($params['user_id']) && $params['user_id'] != '') {
            $query->where('user_id', '=', $params['user_id']);
        }

        //Search By login_ip
        if (isset($params['login_ip']) && $params['login_ip'] != '') {
            $query->where('login_ip', '=', $params['login_ip']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }


        return $query;
    }

    /**
     * 系统登录日志表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = GspSysLoginLogs::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 系统登录日志表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspSysLoginLogs::find($params['id']);
    }

    /**
     * 系统登录日志表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return GspSysLoginLogs::create($params);
    }

    /**
     * 系统登录日志表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspSysLoginLogs::find($params['id'])->update($params);
    }

    /**
     * 系统登录日志表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return GspSysLoginLogs::destroy($params['ids']);
    }




}