<?php
/**
 * oil_card_supplyer
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/11/26
 * Time: 10:07:05
 */
namespace Models;
use Fuel\Defines\StationOperatorsConf;
use Illuminate\Database\Capsule\Manager as Capsule;

/**
 * Class OilStationOperators
 * @package Models
 * @property $no_used_order_invoice
 * @property $is_second_check
 * @property $operators_code
 */
class OilStationOperators extends \Framework\Database\Model
{
    protected $table = 'oil_station_operators';

    protected $guarded = ["id"];
    protected $fillable = ['operators_name','station_name','operators_code','status','createtime','updatetime',
        'is_show','short_name','slogan','audio_url','is_second_check','black_qrcode','is_self_operated', 'no_used_order_invoice'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By supplyer_name
        if (isset($params['operators_name']) && $params['operators_name'] != '') {
            $query->where('operators_name', '=', $params['operators_name']);
        }

        //Search By supplyer_name_like
        if (isset($params['supplyer_name_like']) && $params['supplyer_name_like'] != '') {
            $query->where('operators_name', 'like', '%'.$params['supplyer_name_like'].'%');
        }

        //Search By operators_code
        if (isset($params['operators_code']) && $params['operators_code'] != '') {
            $query->where('operators_code', '=', $params['operators_code']);
        }

        //Search By operators_code
        if (isset($params['operators_code_lk']) && $params['operators_code_lk'] != '') {
            $query->where('operators_code', 'like', '%'.$params['operators_code_lk'].'%');
        }


        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By status
        if (isset($params['is_show']) && $params['is_show'] != '') {
            $query->where('is_show', '=', $params['is_show']);
        }

        //Search By is_second_check
        if (isset($params['is_second_check']) && $params['is_second_check'] != '') {
            $query->where('is_second_check', '=', $params['is_second_check']);
        }

        if (! empty($params['no_used_order_invoice'])) {
            $query->where('no_used_order_invoice', '=', $params['no_used_order_invoice']);
        }

        //Search By is_self_operated
        if (isset($params['is_self_operated']) && $params['is_self_operated'] != '') {
            $query->where('is_self_operated', '=', $params['is_self_operated']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * oil_card_supplyer 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilStationOperators::Filter($params)->whereNotNull('operators_code');
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('createtime', 'desc')->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        foreach ($data as &$item){
            $item->status_txt = $item->status == 1 ? '正常' : '异常';
            $item->show_txt = $item->is_show == 1 ? '展示' : '不展示';
            $item->check_txt = $item->is_second_check == 1 ? '是' : '否';
            $item->is_self_operated_val = $item->is_self_operated == 1 ? '是' : '否';
            $item->no_used_order_invoice_val = $item->no_used_order_invoice == StationOperatorsConf::NO_USED_ORDER_INVOICE_YES ? '是' : '否';
        }
        return $data;
    }

    /**
     * oil_card_supplyer 详情查询
     * @param array $params
     * @return OilStationOperators
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilStationOperators::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilStationOperators::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_card_supplyer 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilStationOperators::create($params);
    }

    /**
     * oil_card_supplyer 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilStationOperators::find($params['id'])->update($params);
    }

    /**
     * oil_card_supplyer 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilStationOperators::destroy($params['ids']);
    }
    static public function batchAdd($data)
    {
        return self::insert($data);
    }

    static public function getOperatorMap(array $params,$key,$val)
    {
        return OilStationOperators::where('status',1)->pluck($val,$key)->toArray();
    }

    static public function getOperatorMapNew($key,$val)
    {
        return OilStationOperators::pluck($val,$key)->toArray();
    }

    static public function getOperatByName($name)
    {
        return self::where('operators_name',$name)->where('status',1)->first();
    }

    static public function getInfoByFilter($params)
    {
        return self::Filter($params)->first();
    }

    static public function getInfoByStationName($name)
    {
        return self::where('station_name',$name)->first();
    }

    /**
     * 批量更新
     * @param       $tableName
     * @param array $apiData
     * @return bool|int
     */
    static public function batchEdit($apiData = [])
    {
        //批量入库
        $data = FALSE;
        if ($apiData) {
            $batchSqlArr = [];
            foreach ($apiData as $v) {
                if (!isset($v['where'])) {
                    throw new \RuntimeException('where条件缺失', 6);
                }
                $fieldStr = '';
                $where    = '';
                foreach ($v as $key => $val) {
                    if ($key != 'where') {
                        if ($key != 'createtime') {
                            if($val === `NULL`){
                                $fieldStr .= $fieldStr ? ",`" . $key . "`=NULL" : "`" . $key . "`=NULL";
                            }else {
                                $fieldStr .= $fieldStr ? ",`" . $key . "`='$val'" : "`" . $key . "`='$val'";
                            }
                        }
                    } else
                        $where = $val;
                }
                $batchSqlArr[] = "UPDATE oil_station_operators SET $fieldStr WHERE $where";
            }
            $batchSql = implode(";", $batchSqlArr);
            $data = Capsule::connection()->getPdo()->exec($batchSql);
        }

        return $data;
    }

    static public function getStationSupplierList(array $params=[])
    {
        Capsule::connection()->enableQueryLog();

        $sqlObj = self::select(Capsule::connection()->raw("id, operators_code as pcode, concat(operators_code, ' ', operators_name) pname, operators_name,createtime"))
            ->where('status',1);
        if(isset($params['id']) && $params['id']){
            $sqlObj->where('id',trim($params['id']));
        }elseif(isset($params['pcode']) && $params['pcode']){
            $sqlObj->where('operators_code',trim($params['pcode']));
        }elseif(isset($params['keyword']) && $params['keyword']){
            $sqlObj->where(function($query) use($params){
                $query->where('operators_code','like',trim($params['keyword'].'%'))
                    ->orWhere('operators_name','like','%'.trim($params['keyword']).'%');
            });
        }

        return $sqlObj->orderBy('createtime', 'desc')->take(50)->get();
    }

    static public function getShowQrCodePcode()
    {
        $condition['status'] = 1;
        $condition['is_show'] = 1;
        $condition['is_second_check'] = 1;
        $sqlObj = OilStationOperators::Filter($condition)->whereNotNull('operators_code');
        $data = $sqlObj->orderBy('createtime', 'asc')->get();
        $res = [];
        if(count($data) > 0){
            foreach ($data as $_item){
                $res[] = $_item->operators_code;
            }
        }
        return $res;
    }

    /**
     * 取字段值
     * @param array $params
     * @param $pluckField
     * @return array
     */
    static public function getPluckFields(array $params, $pluckField)
    {
        return self::Filter($params)->pluck($pluckField)->toArray();
    }

    /**
     * 取字段值 返回第一个值
     * @param array $params
     * @param $pluckField
     * @return mixed
     */
    static public function getResField(array $params, $pluckField)
    {
        $res = self::Filter($params)->pluck($pluckField)->toArray();

        return !empty($res[0]) ? $res[0] : '';
    }

    public static function oilOperatorSug(array $params){
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $data = OilStationOperators::Filter($params)
            ->select(["oil_station_supplier.operator_id as operator_id","oil_station_operators.*"])
            ->leftJoin('oil_supplier_relation','oil_supplier_relation.code','=', 'oil_station_operators.operators_code')
            ->leftJoin('oil_station_supplier', 'oil_station_supplier.id','=','oil_supplier_relation.supplier_id')
            ->dataRange('oil_station_supplier')
            ->paginate($params['limit'],['*'],'page',$params['page']);
//            ->get();
        return $data;
    }
}
