<?php
/**
 * oil_crm_org
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/09/26
 * Time: 14:16:24
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCrmOrg extends \Framework\Database\Model
{
    protected $table = 'oil_crm_org';

    protected $guarded = ["id"];
    protected $fillable = ['accountnumber','name','orgcode','cdc_id','companycode','aplownerid','aplowneridname','saleEmail',
        'oil_centercode','oil_centername','external_cross_sale','oil_cross_sale','oil_cross_wordno','oil_cross_saleEmail',
        'oil_cross_centercode','oil_cross_centername','lastupdatetime','new_productline','totalcount',
        'new_department','new_area','new_fossilsupporter','new_accountgroup','new_whetherproxy','sign_aplowneridname','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By accountnumber
        if (isset($params['accountnumber']) && $params['accountnumber'] != '') {
            $query->where('accountnumber', '=', $params['accountnumber']);
        }

        //Search By name
        if (isset($params['name']) && $params['name'] != '') {
            $query->where('name', '=', $params['name']);
        }

        //Search By cdc_id
        if (isset($params['cdc_id'])) {
            if (is_array($params['cdc_id']) && ! empty($params['cdc_id'])) {
                $query->whereIn('cdc_id', $params['cdc_id']);
            } elseif ($params['cdc_id'] != '') {
                $query->where('cdc_id', '=', $params['cdc_id']);
            }
        }

        //Search By companycode
        if (isset($params['companycode']) && $params['companycode'] != '') {
            $query->where('companycode', '=', $params['companycode']);
        }

        //Search By aplownerid
        if (isset($params['aplownerid']) && $params['aplownerid'] != '') {
            $query->where('aplownerid', '=', $params['aplownerid']);
        }

        //Search By aplowneridname
        if (isset($params['aplowneridname']) && $params['aplowneridname'] != '') {
            $query->where('aplowneridname', '=', $params['aplowneridname']);
        }

        //Search By saleEmail
        if (isset($params['saleEmail']) && $params['saleEmail'] != '') {
            $query->where('saleEmail', '=', $params['saleEmail']);
        }

        //Search By oil_centercode
        if (isset($params['oil_centercode']) && $params['oil_centercode'] != '') {
            $query->where('oil_centercode', '=', $params['oil_centercode']);
        }

        //Search By oil_centername
        if (isset($params['oil_centername']) && $params['oil_centername'] != '') {
            $query->where('oil_centername', '=', $params['oil_centername']);
        }

        //Search By external_cross_sale
        if (isset($params['external_cross_sale']) && $params['external_cross_sale'] != '') {
            $query->where('external_cross_sale', '=', $params['external_cross_sale']);
        }

        //Search By oil_cross_sale
        if (isset($params['oil_cross_sale']) && $params['oil_cross_sale'] != '') {
            $query->where('oil_cross_sale', '=', $params['oil_cross_sale']);
        }

        //Search By oil_cross_wordno
        if (isset($params['oil_cross_wordno']) && $params['oil_cross_wordno'] != '') {
            $query->where('oil_cross_wordno', '=', $params['oil_cross_wordno']);
        }

        //Search By oil_cross_saleEmail
        if (isset($params['oil_cross_saleEmail']) && $params['oil_cross_saleEmail'] != '') {
            $query->where('oil_cross_saleEmail', '=', $params['oil_cross_saleEmail']);
        }

        //Search By oil_cross_centercode
        if (isset($params['oil_cross_centercode']) && $params['oil_cross_centercode'] != '') {
            $query->where('oil_cross_centercode', '=', $params['oil_cross_centercode']);
        }

        //Search By oil_cross_centername
        if (isset($params['oil_cross_centername']) && $params['oil_cross_centername'] != '') {
            $query->where('oil_cross_centername', '=', $params['oil_cross_centername']);
        }

        //Search By lastupdatetime
        if (isset($params['lastupdatetime']) && $params['lastupdatetime'] != '') {
            $query->where('lastupdatetime', '=', $params['lastupdatetime']);
        }

        //Search By new_productline
        if (isset($params['new_productline']) && $params['new_productline'] != '') {
            $query->where('new_productline', '=', $params['new_productline']);
        }

        //Search By totalcount
        if (isset($params['totalcount']) && $params['totalcount'] != '') {
            $query->where('totalcount', '=', $params['totalcount']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * oil_crm_org 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCrmOrg::Filter($params)->leftJoin('oil_org','oil_org.crm_id','=','oil_crm_org.cdc_id')
        ->selectRaw('oil_crm_org.*,oil_org.orgcode');
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        $data = self::formatData($data);

        return $data;
    }

    static private function formatData($data)
    {
        foreach ($data as &$v){
            $v->_new_accountgroup = !$v->new_accountgroup ? $v->new_accountgroup : ($v->new_accountgroup == 10 ? '个人' : '企业');
            $v->_new_whetherproxy = !$v->new_whetherproxy ? $v->new_whetherproxy : ($v->new_whetherproxy == 1 ? '是' : '否');
            $v->company_attribute = self::getCompanyAttribute($v->new_accountgroup,$v->new_whetherproxy,$v->aplowneridname);
        }

        return $data;
    }

    /**
     * 计算客户属性
     * @param $new_accountgroup
     * @param $new_whetherproxy
     * @param $aplowneridname
     * @return string
     */
    static private function getCompanyAttribute($new_accountgroup,$new_whetherproxy,$aplowneridname)
    {
       /* 常规：企业客户+有销售归属+非代理商客户；
        * 代理商：企业客户+有销售归属+代理商客户；
        * 公海池：企业客户+公海池归属+非代理商客户；
        * 个人：个人客户+有销售归属+非代理商客户；
        * 其他：企业客户+公海池归属+代理商客户（不存在）、个人客户+公海池归属+非代理商客户（不存在）、个人客户+公海池归属+代理商客户（不存在）、个人客户+有销售归属+代理商客户（不确定是否有这样的数据）
        */
        $company_arrtibutes = '其他';

        if($new_accountgroup == 20 && $aplowneridname != '结算公海池' && $new_whetherproxy == 1){
            $company_arrtibutes = '代理商';
        }

        if($new_accountgroup == 20 && $aplowneridname != '结算公海池' && $new_whetherproxy == 2){
            $company_arrtibutes = '常规';
        }

        if($new_accountgroup == 20 && $aplowneridname == '结算公海池' && $new_whetherproxy == 1){
            $company_arrtibutes = '其他';
        }

        if($new_accountgroup == 20 && $aplowneridname == '结算公海池' && $new_whetherproxy == 2){
            $company_arrtibutes = '公海池';
        }

        if($new_accountgroup == 10 && $aplowneridname != '结算公海池' && $new_whetherproxy == 1){
            $company_arrtibutes = '其他';
        }

        if($new_accountgroup == 10 && $aplowneridname != '结算公海池' && $new_whetherproxy == 2){
            $company_arrtibutes = '个人';
        }

        return $company_arrtibutes;
    }

    /**
     * oil_crm_org 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCrmOrg::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCrmOrg::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_crm_org 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCrmOrg::create($params);
    }

    /**
     * oil_crm_org 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCrmOrg::find($params['id'])->update($params);
    }

    /**
     * oil_crm_org 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCrmOrg::destroy($params['ids']);
    }

    static public function getByOrgId($org_id)
    {
        $saleInfo = null;
        $org_info = OilOrg::getById(['id'=>$org_id]);
        if($org_info){

//            if(substr($org_info->orgcode,0,6) == '201XW3'){
//                //如果是无车，子集对子集
//                $saleInfo = OilCrmOrg::where('orgcode',$org_info->orgcode)->first();
//            }else{
//                //非无车去顶级和crm匹配
//                $saleInfo = OilCrmOrg::where('orgcode',substr($org_info->orgcode,0,6))->first();
//            }

            //2020423调整为按照crmid进行关联
            $saleInfo = OilCrmOrg::where('cdc_id',$org_info->crm_id)->first();
            
            
        }

        return $saleInfo;
    }


}