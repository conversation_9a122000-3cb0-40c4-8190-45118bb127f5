<?php
/**
 * oil_masking_source
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/12/27
 * Time: 17:44:04
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilMaskingSource extends \Framework\Database\Model
{
    protected $table = 'oil_masking_source';

    protected $guarded = ["id"];
    protected $fillable = ['source_name','source_control','is_masking','creator_id','creator_name','createtime','last_operator_id','last_operator','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By source_name
        if (isset($params['source_name']) && $params['source_name'] != '') {
            $query->where('source_name', '=', $params['source_name']);
        }

        //Search By source_control
        if (isset($params['source_control']) && $params['source_control'] != '') {
            $query->where('source_control', '=', $params['source_control']);
        }

        //Search By is_masking
        if (isset($params['is_masking']) && $params['is_masking'] != '') {
            $query->where('is_masking', '=', $params['is_masking']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By creator_name
        if (isset($params['creator_name']) && $params['creator_name'] != '') {
            $query->where('creator_name', '=', $params['creator_name']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By last_operator_id
        if (isset($params['last_operator_id']) && $params['last_operator_id'] != '') {
            $query->where('last_operator_id', '=', $params['last_operator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * oil_masking_source 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilMaskingSource::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_masking_source 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilMaskingSource::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilMaskingSource::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_masking_source 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilMaskingSource::create($params);
    }

    /**
     * oil_masking_source 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilMaskingSource::find($params['id'])->update($params);
    }

    /**
     * oil_masking_source 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilMaskingSource::destroy($params['ids']);
    }




}