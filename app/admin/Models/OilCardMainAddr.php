<?php
/**
 * 卡务收件地址
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/05/22
 * Time: 15:41:19
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCardMainAddr extends \Framework\Database\Model
{
    protected $table = 'oil_card_main_addr';

    protected $guarded = ["id"];
    protected $fillable = ['addr_flag','addr_name','addr_mobile','address','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By addr_flag
        if (isset($params['addr_flag']) && $params['addr_flag'] != '') {
            $query->where('addr_flag', '=', $params['addr_flag']);
        }

        //Search By addr_name
        if (isset($params['addr_name']) && $params['addr_name'] != '') {
            $query->where('addr_name', '=', $params['addr_name']);
        }

        //Search By addr_mobile
        if (isset($params['addr_mobile']) && $params['addr_mobile'] != '') {
            $query->where('addr_mobile', '=', $params['addr_mobile']);
        }

        //Search By address
        if (isset($params['address']) && $params['address'] != '') {
            $query->where('address', '=', $params['address']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 卡务收件地址 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCardMainAddr::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        if($data){
            foreach ($data as &$val)
            {
                $val->subname = $val->addr_flag.'|'.$val->addr_name.'|'.$val->addr_mobile.'|'.$val->address;
            }
        }

        return $data;
    }

    /**
     * 卡务收件地址 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardMainAddr::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardMainAddr::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 卡务收件地址 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCardMainAddr::create($params);
    }

    /**
     * 卡务收件地址 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardMainAddr::find($params['id'])->update($params);
    }

    /**
     * 卡务收件地址 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCardMainAddr::destroy($params['ids']);
    }


    static public function getIdMapName($field = 'addr_flag')
    {
        return OilCardMainAddr::lists($field, 'id')->toArray();
    }



}