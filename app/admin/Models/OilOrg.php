<?php
/**
 * 油品机构表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:18
 */
namespace Models;
use Framework\Cache;
use Fuel\Defines\AssignType;
use Fuel\Defines\OrgDefine;
use Fuel\Defines\SingleDayCeil;
use Fuel\Defines\CardFrom;
use Fuel\Defines\TradesType;
use Fuel\Defines\OrgStatus;
use Fuel\Defines\ExclusiveCustom;
use Fuel\Service\OrgConfigService;
use Illuminate\Database\Capsule\Manager as Capsule;
use \Framework\Database\Model;
use Framework\Log;

class OilOrg extends Model
{
    protected $table = 'oil_org';

    protected $guarded = ["id"];

    protected $fillable = ['orgcode','status','org_name','sub_org_name','creator_id','is_test','exclusive_custom','last_operator','open_mode',
        'operators_id','single_day_ceil','is_recepit_nowtime','createtime','is_del','updatetime','other_creator_id','first_apply_card','first_apply_receipt',
        'other_creator','receipt_mode','belongto_saler','agent_orgcode','agent_time','is_register_carrier','register_time',
        'crm_id','first_charge_time','first_trade_time', 'latest_charge_time','latest_trade_time','belongto_saler',
        'belongto_saler_id','sys_user_id','sign_data_integrity','sign_receipt_mode','can_change_sign','is_system_orgcode','sign_match','is_receipt_white',"receipt_begin_time"];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function Operators()
    {
        return $this->belongsTo('Models\OilOperators','operators_id','id');
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('oil_org.id', '=', $params['id']);
        }

        if (isset($params['idList']) && $params['idList']) {
            $query->whereIn('oil_org.id',$params['idList']);
        }

        //Search By id
        if (isset($params['operators_id']) && $params['operators_id'] != '') {
            $query->where('operators_id', '=', $params['operators_id']);
        }

        //Search By id
        if (isset($params['is_recepit_nowtime']) && $params['is_recepit_nowtime'] != '') {
            $query->where('is_recepit_nowtime', '=', $params['is_recepit_nowtime']);
        }

        //Search By id
        if (isset($params['search_org_id']) && $params['search_org_id'] != '') {
            $query->where('id', '=', $params['search_org_id']);
        }

        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('oil_org.id', '=', $params['org_id']);
        }
        //Search By orgcode
        if (isset($params['orgcode'])) {
            if (is_array($params['orgcode']) && !empty($params['orgcode'])) {
                $query->whereIn('orgcode', $params['orgcode']);
            } elseif($params['orgcode'] != '') {
                $query->where('orgcode', '=', $params['orgcode']);
            }
        }

        //Search By crm_id
        if (isset($params['crm_id']) && $params['crm_id'] != '') {
            $query->where('crm_id', '=', $params['crm_id']);
        }

        //Search By open_mode
        if (isset($params['open_mode']) && $params['open_mode'] != '') {
            $query->where('open_mode', '=', $params['open_mode']);
        }

        //Search By orgcode
        if (isset($params['orgCodeLike']) && $params['orgCodeLike'] != '') {
            $query->where('orgcode', 'like', $params['orgCodeLike'].'%');
        }

        //Search By orgcodeIn
        if (isset($params['orgcodeIn']) && count($params['orgcodeIn']) > 0) {
            $query->whereIn('orgcode', $params['orgcodeIn']);
        }

        //Search By orgcode
        if (isset($params['search_orgcode']) && $params['search_orgcode'] != '') {
            $query->where('orgcode', 'like', '%'.$params['search_orgcode'].'%');
        }

        //Search By org_name
        if (isset($params['org_name']) && $params['org_name'] != '') {
            $query->where('org_name', '=', $params['org_name']);
        }

        //Search By orgcodeIn
        if (isset($params['orgnameIn']) && count($params['orgnameIn']) > 0) {
            $query->whereIn('org_name', $params['orgnameIn']);
        }

        //Search By org_name
        if (isset($params['sub_org_name']) && $params['sub_org_name'] != '') {
            $query->where('sub_org_name', 'like', '%'.$params['sub_org_name'].'%');
        }

        //Search By single_day_ceil
        if (isset($params['single_day_ceil']) && $params['single_day_ceil'] != '') {
            $query->where('single_day_ceil', '=', $params['single_day_ceil']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        if (! empty($params['createtime_neq_0'])) {
            $query->where('createtime', '!=', '0000-00-00 00:00:00');
        }

        //Search By is_del
        if ( isset($params['is_del']) ) {
            $query->where('oil_org.is_del', '=', $params['is_del']);
        }

        //Search By is_test
        if (isset($params['is_test']) && $params['is_test']) {
            $query->where('oil_org.is_test', '=', $params['is_test']);
        }

        //Search By is_test
        if (isset($params['exclusive_custom']) && $params['exclusive_custom']) {
            $query->where('oil_org.exclusive_custom', '=', $params['exclusive_custom']);
        }
        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By other_creator_id
        if (isset($params['other_creator_id']) && $params['other_creator_id'] != '') {
            $query->where('other_creator_id', '=', $params['other_creator_id']);
        }

        //Search By other_creator
        if (isset($params['other_creator']) && $params['other_creator'] != '') {
            $query->where('other_creator', '=', $params['other_creator']);
        }
        if (isset($params['orgcodeAll']) && $params['orgcodeAll'] != '') {
            $query->where('orgcode', 'like',  '%'.$params['orgcodeAll'].'%');
        }
        //Search By createtimeGe
        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('oil_org.createtime', '>=', $params['createtimeGe']);
        }

        //Search By createtimeLe
        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $query->where('oil_org.createtime', '<=', $params['createtimeLe']);
        }

        //Search By first_apply_cardGe
        if (isset($params['first_apply_cardGe']) && $params['first_apply_cardGe'] != '') {
            $query->where('oil_org.first_apply_card', '>=', $params['first_apply_cardGe']);
        }

        //Search By first_apply_cardLe
        if (isset($params['first_apply_cardLe']) && $params['first_apply_cardLe'] != '') {
            $query->where('oil_org.first_apply_card', '<=', $params['first_apply_cardLe']);
        }

        //Search By updatetimeGe
        if (isset($params['updatetimeGe']) && $params['updatetimeGe'] != '') {
            $query->where('oil_org.updatetime', '>=', $params['updatetimeGe']);
        }

        //Search By updatetimeLe
        if (isset($params['updatetimeLe']) && $params['updatetimeLe'] != '') {
            $query->where('oil_org.updatetime', '<=', $params['updatetimeLe']);
        }

        //Search By receipt_mode
        if (isset($params['receipt_mode']) && $params['receipt_mode'] != '') {
            $query->where('oil_org.receipt_mode', '=', $params['receipt_mode']);
        }

        //Search By is_first_receipt_apply
        if (isset($params['is_first_receipt_apply']) && $params['is_first_receipt_apply'] == 1) {
            $query->whereNotNull('oil_org.first_apply_receipt');
        } elseif (isset($params['is_first_receipt_apply']) && $params['is_first_receipt_apply'] == 2) {
            $query->whereNull('oil_org.first_apply_receipt');
        }

        //Search By belongto_saler
        if (isset($params['belongto_saler']) && $params['belongto_saler'] != '') {
            $query->where('oil_org.belongto_saler', 'like', '%'.$params['belongto_saler'].'%');
        }

        //Search By belongto_saler
        if (isset($params['belongto_saler_id']) && $params['belongto_saler_id'] != '') {
            $query->where('oil_org.belongto_saler_id', '=', $params['belongto_saler_id']);
        }

        //Search By agent_org
        if (isset($params['agent_org']) && $params['agent_org'] != '') {
            $query->where('oil_org.agent_orgcode', 'like', '%'.$params['agent_org'].'%');
        }

        //Search By is_register_carrier
        if (isset($params['is_register_carrier']) && $params['is_register_carrier'] != '') {
            $query->where('oil_org.is_register_carrier',$params['is_register_carrier']);
        }

        if (isset($params['orgcode_len_in']) && is_array($params['orgcode_len_in']) &&
            !empty($params['orgcode_len_in']) && ! empty($params['orgcode_len_in']['list'])) {
            $query->whereRaw("left(orgcode, ". $params['orgcode_len_in']['len'] .") in ('". implode("','", $params['orgcode_len_in']['list']) ."')");
        }

        //Search By sign_data_integrity
        if (isset($params['sign_data_integrity']) && $params['sign_data_integrity'] != '') {
            $query->where('oil_org.sign_data_integrity',$params['sign_data_integrity']);
        }

        //Search By sign_receipt_mode
        if (isset($params['sign_receipt_mode']) && $params['sign_receipt_mode'] != '') {
            $query->where('oil_org.sign_receipt_mode',$params['sign_receipt_mode']);
        }

        //Search By can_change_sign
        if (isset($params['can_change_sign']) && $params['can_change_sign'] != '') {
            $query->where('oil_org.can_change_sign',$params['can_change_sign']);
        }

        //is_system_orgcode:平台客户
        if (isset($params['is_system_orgcode']) && $params['is_system_orgcode']) {
            $query->where('oil_org.is_system_orgcode',$params['is_system_orgcode']);
        }


        return $query;
    }

    /**
     * 油品机构表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        Capsule::connection()->enableQueryLog();
        $sqlObj = OilOrg::Filter($params)
            ->leftJoin('oil_operators','oil_org.operators_id','=','oil_operators.id')
            ->select('oil_org.*','oil_operators.name as operators_name','oil_operators.merchantID')
            ->orderBy('createtime','asc');

        if(isset($params['count']) && $params['count'] == 1){
            return $sqlObj->count();
        }elseif(isset($params['take']) && isset($params['skip'])){
            $data = $sqlObj->skip(intval($params['skip']))->take(intval($params['take']))->get();
        }elseif(isset($params['_exportkey']) && $params['_exportkey'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * @title   获取顶级机构分页及下属机构数统计
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * mixed
     * @returns
     */
    static public function getOrgByPage(array $params)
    {
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        Capsule::connection()->enableQueryLog();
        $sqlObj = OilOrg::Filter($params)
            ->leftJoin('oil_operators','oil_org.operators_id','=','oil_operators.id')
            ->select('oil_org.*','oil_operators.name as operators_name');

        $sqlObj->orderBy('oil_org.orgcode');
        //"油品机构维护"列表支持按照运营商筛选
        $sqlObj->dataRange();

        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        } else if  (isset($params['skip']) && isset($params['take'])) {
            $data = $sqlObj->skip(intval($params['skip']))->take(intval($params['take']))->get();
            $data = self::formatOrgList($data);
        }elseif (isset($params['count']) && intval($params['count']) == 1) {
            $data = $sqlObj->count();
        } else{
            if(!isset($params['orgCodeLike']) || !$params['orgCodeLike']){
                $sqlObj->whereRaw('length(oil_org.orgcode) = 6');
            }else{
                if(isset($params['org_flag']) && $params['org_flag'] == 'true'){//列表查询包含本级机构
                    $sqlObj->where('orgcode',$params['orgCodeLike']);
                }else{
                    //只取下一级的子机构，取出下一级机构编码的位数
                    $len = self::getNextOrgcodeLength($params['orgCodeLike']);
                    $sqlObj->whereRaw('length(oil_org.orgcode) = '.$len);
                }
            }

            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }
//        $sql = Capsule::connection()->getQueryLog();
//        Log::info('$sql--'.var_export($sql,true));
        foreach ($data as $v){
            $v->sign_data_integrity_txt = isset(OrgStatus::$DataIntegrity[$v->sign_data_integrity]) ? OrgStatus::$DataIntegrity[$v->sign_data_integrity] : "";
            $v->sign_receipt_mode_txt = isset(OrgStatus::$DataReceipt[$v->sign_receipt_mode]) ? OrgStatus::$DataReceipt[$v->sign_receipt_mode] : "";
            $v->can_change_sign_txt = $v->can_change_sign == 10 ? "可以" : "不可以";
            $v->childNodesNum = self::where('orgcode','like',$v->orgcode.'%')->where('is_del',0)->count();//统计子机构数量
        }

        return $data;
    }

    /**
     * @title   抽出数据中的orgcode
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param      $data
     * @param bool $symbol
     * @return array
     * @returns
     * array
     * @returns
     */
    static public function formatOrgCodes($data,$symbol=\FALSE)
    {
        $orgCodes = [];

        if($data){
            foreach($data as $v){
                $orgCodes[] = $symbol ? "'".$v->orgcode."'" : $v->orgcode;
            }
        }

        return $orgCodes;
    }

    /**
     * @title   获取机构下的下属机构数量
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param $data
     * @param $orgCodes
     * @return mixed
     * @returns
     * mixed
     * @returns
     */
    static public function getChildrenCountsByOrgCodes($data,$orgCodes)
    {
        $record = OilOrg::whereRaw('substr(orgcode, 1, 6) in (\''.implode("','", $orgCodes).'\')')->where('is_del','0')
            ->pluck('id','orgcode');

        if($record){
            foreach($data as &$v){
                $v->total_num = 0;
                foreach($record as $key=>$item){
                    if(\strpos($key, $v->orgcode) !== \FALSE){
                        $v->total_num++;
                    }
                }
            }
        }

        return $record;
    }

    /**
     * 按分页获取机构编码
     * @param array $params
     * @return mixed
     */
    static public function getOrgCode(array $params)
    {
        \helper::argumentCheck(['page','limit'],$params);

//        Capsule::connection()->enableQueryLog();
        $data = OilOrg::select('orgcode','org_name')->where('is_del',0)->paginate($params['limit'],['*'],'page',$params['page'])->toArray();
//        Log::info(var_export(Capsule::connection()->getQueryLog(),true));

        return $data;
    }

    /**
     * 油品机构表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        $cacheKey = var_export(__METHOD__,true).$params['id'];
        $data = Cache::get($cacheKey);
        if(!$data || 1){
            $data = OilOrg::where('id',$params['id'])->where('is_del',0)->first();
            Cache::put($cacheKey,$data,2*60);
        }

        return $data;
    }

    /*
     * 缓存crm_org_map
     */
    static public function getCrmOrgMap()
    {
        $cacheKey = var_export(__METHOD__,true);
        $data = Cache::get($cacheKey);
        if(!$data || 1){
            $data = OilOrg::where('crm_id','!=','')
                ->where('is_del',0)
                ->where('status',1)
                ->where('is_test',1)
                ->pluck('crm_id','orgcode');
            Cache::put($cacheKey,$data,12*60*60);
        }

        return $data;
    }

    /**
     * 根据org_id获取顶级机构的id
     * @param array $params
     * @return mixed
     */
    static public function getTopIdById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        $orgInfo = OilOrg::getById($params);
        if($orgInfo === NULL){
            throw new \RuntimeException('机构不存在',2);
        }
        $orgInfo = OilOrg::getByOrgcode(substr($orgInfo->orgcode,0,6));

        return $orgInfo->id;
    }

    /**
     * 油品机构表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilOrg::create($params);
    }

    /**
     * 油品机构表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrg::find($params['id'])->update($params);
    }

    /**
     * 油品机构表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilOrg::destroy($params['ids']);
    }

    /**
     * Get Row By orgcode
     * @param $orgcode
     * @return mixed
     */
    static public function getByOrgcode($orgcode)
    {
        return OilOrg::where('orgcode','=',$orgcode)->where('is_del','=',0)->first();
    }

    static public function getByOrgcodeForLock($orgcode)
    {
        return OilOrg::lockForUpdate()->where('orgcode','=',$orgcode)->where('is_del','=',0)->first();
    }

    /**
     * Get Row By orgcode from cache
     * @param $orgcode
     * @return mixed
     */
    static public function getByCacheOrgcode($orgcode)
    {
        $cacheKey = var_export(__METHOD__,true).$orgcode;
        $data = Cache::get($cacheKey);
        if(!$data){
            $data = OilOrg::where('orgcode','=',$orgcode)->where('is_del','=',0)->first();
            Cache::put($cacheKey,$data,60*60);
        }
        return $data;
    }

    /**
     * 修改机构树内的运营商和手续费单日上限
     * @param array $params
     */
    static public function updateOperatorsAndSingleDayCeil(array $params)
    {
        $orgInfo = OilOrg::getById(['id'=>$params['id']]);
        //防止清空 operators_id
        $update_arr = [];
        if(isset($params['operators_id']) && $params['operators_id'] != $orgInfo->operators_id){
            $update_arr['operators_id'] = $params['operators_id'];
        }
        if(isset($params['single_day_ceil']) &&  $params['single_day_ceil'] != $orgInfo->single_day_ceil){
            $update_arr['single_day_ceil'] = $params['single_day_ceil'];
        }
        if(!empty($update_arr)) {
            OilOrg::where('orgcode','like',substr($orgInfo->orgcode,0,6).'%')->update($update_arr);
        }
    }

    /**
     * 若修改测试机构，则对整个机构树生效
     * @param array $params
     */
    static public function updateOrgIsTest(array $params)
    {
        $orgInfo = OilOrg::getById(['id'=>$params['id']]);
        if($params['is_test'] != $orgInfo->is_test){
            OilOrg::where('orgcode','like',substr($orgInfo->orgcode,0,6).'%')->update(['is_test'=>$params['is_test']]);
        }
    }

    /**
     * 根据机构编码修改机构
     * @param array $params
     * @return mixed
     */
    static public function updateByOrgcode(array $params)
    {
        \helper::argumentCheck(['orgcode'],$params);

        Log::info(var_export($params,true),[],'updateByOrgcode');

        return OilOrg::where('orgcode',$params['orgcode'])->update($params);
    }


    /**
     * @title   根据创建机构判断所属机构是否是本级或下级
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param $orgcode
     * @param $org_id
     * @return array
     * @returns
     * array
     * @returns
     */
    static public function getOrgBig($orgcode,$org_id)
    {
        return Capsule::connection()->select("SELECT orgcode FROM oil_org WHERE is_del = 0 AND orgcode LIKE '$orgcode%' AND id = $org_id");
    }
    /**
     * 若修改专属客服，则对整个机构树生效
     * @param array $params
     */
    static public function updateExclusiveCustom(array $params)
    {
        $orgInfo = OilOrg::getById(['id'=>$params['id']]);
        $updataData = [];
        if(isset($orgInfo->orgcode) && !empty($orgInfo->orgcode)){
            if (isset($params['exclusive_custom']) && !empty($params['exclusive_custom'])){
                $updataData['exclusive_custom'] = $params['exclusive_custom'];
            }
            if(isset($params['sys_user_id']) && !empty($params['sys_user_id'])){
                $updataData['sys_user_id'] = $params['sys_user_id'];
            }
            $orgRoot = substr($orgInfo->orgcode, 0, 6).'%';
            if(isset($params['belongto_saler_id']) && !empty($params['belongto_saler_id'])){
                $updataData['belongto_saler'] = $params['belongto_saler'];
                $updataData['belongto_saler_id'] = $params['belongto_saler_id'];
                if (in_array(substr($orgInfo->orgcode, 0, 6),OrgDefine::$wh_org_list)){
                    if(strlen($orgInfo->orgcode) >= 12) {
                        $orgRoot = substr($orgInfo->orgcode, 0, 12)."%";
                    }else{
                        $orgRoot = $orgInfo->orgcode;
                    }
                }
            }
            if(count($updataData) > 0) {
                OilOrg::where('orgcode', 'like', $orgRoot)->update($updataData);
            }
        }
    }
    /**
     * 若修改开票核算方式，则对整个机构树生效
     * @param array $params
     */
    static public function updateReceiptMode(array $params)
    {
        $orgInfo = OilOrg::getById(['id'=>$params['id']]);
        $ret = false;
        if(isset($orgInfo->orgcode) && !empty($orgInfo->orgcode) &&
            isset($params['receipt_mode']) && !empty($params['receipt_mode'])){
            $org_root = substr($orgInfo->orgcode,0,6);
            $ret = OilOrg::where('orgcode','like',$org_root.'%')->update(['receipt_mode'=>$params['receipt_mode']]);

            //更改机构的精细核算方式
            (new \Fuel\Service\OrgService())->checkSignReceipt(['orgcode'=>$org_root,'is_update_sign'=>1]);
        }

        return $ret;
    }

    /**
     * 更新当前机构首次或最近消费时间
     */
    static public function updateTradesTime( $org_id = "" )
    {
        if(empty($org_id)){
            return false;
        }
        $now = \helper::nowTime();
        OilOrg::where("id",$org_id)->whereNull("first_trade_time")->update(['first_trade_time'=>$now,'updatetime'=>$now]);
        OilOrg::where("id",$org_id)->whereRaw("(ISNULL(latest_trade_time) or UNIX_TIMESTAMP(latest_trade_time) < ".strtotime($now).")")->update(['latest_trade_time'=>$now,'updatetime'=>$now]);
    }

    /**
     * 更新当前机构首次或最近充值时间
     */
    static public function updateChargesTime( $org_id = "" )
    {
        if(empty($org_id)){
            return false;
        }
        $now = \helper::nowTime();
        OilOrg::where("id",$org_id)->whereNull("first_charge_time")->update(['first_charge_time'=>$now,'updatetime'=>$now]);
        OilOrg::where("id",$org_id)->whereRaw("(ISNULL(latest_charge_time) or UNIX_TIMESTAMP(latest_charge_time) < ".strtotime($now).")")->update(['latest_charge_time'=>$now,'updatetime'=>$now]);
    }

    /**
     * @title   Get Row By orgcode
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param $orgcode
     * @return mixed
     * @returns
     * mixed
     * @returns
     */
    static public function getByOrgcodeLike($orgcode)
    {
        return OilOrg::where('orgcode','LIKE',$orgcode.'%')->where('is_del','=',0)->pluck('id')->toArray();
    }

    /**
     * @title   通过机构id,获取机构
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param $orgcode
     * @return array
     * @returns
     * array
     * @returns
     */
    static public function getDetailOrgcode($orgcode)
    {
        return Capsule::connection()->select("SELECT id, concat(orgcode, ' ', org_name) org_name, orgcode, org_name orgname FROM oil_org WHERE is_del = 0 AND orgcode LIKE '$orgcode%'");
    }



    static public function getByIsTestLike($is_test)
    {
        return OilOrg::where('is_test','=',$is_test)->where('is_del','=',0)->pluck('id')->toArray();
    }

    static public function getByIds(array $ids)
    {
        return OilOrg::whereIn('id',$ids)->get();
    }

    /**
     * @title   获取指定机构的机构树所有id
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Models
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getIdList(array $params)
    {
        $orgDetail = self::getById(['id'=>$params['org_id']]);
        return OilOrg::Filter(['is_del'=>0,'orgcodeAll'=>substr($orgDetail->orgcode,0,6)])
            ->pluck('id')
            ->toArray();
    }

    static public function getOrgcodeList(array $ids)
    {
        return OilOrg::whereIn('id',$ids)->where('is_del',0)->pluck('orgcode')->toArray();
    }

    static public function getInferior(array $params)
    {
        return OilOrg::Filter($params)->where("is_del","=",0)->pluck('id')->toArray();
    }

    static public function getOneInfo(array $params)
    {
        return OilOrg::Filter($params)->first();
    }

    /**
     * @title   根据orgroots获取map
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param array  $orgCodes
     * @param string $value
     * @param string $key
     * @return mixed
     * @returns
     * mixed
     * @returns
     */
    static public function getByOrgCodesMap(array $orgCodes,$value='org_name',$key='orgcode')
    {
        return OilOrg::whereIn('orgcode',$orgCodes)->where('is_del',0)->lists($value,$key)->toArray();
    }

    static public function getByOrgIdArrMap(array $orgIdArr,$value='orgcode',$key='id')
    {
        return OilOrg::whereIn('id',$orgIdArr)->where('is_del',0)->lists($value,$key)->toArray();
    }

    /**
     * @title   根据orgroots获取单字段的数组值
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param array  $orgCodes
     * @param string $value
     * @return array
     * @returns
     * array
     * @returns
     */
    static public function getByOrgCodesFieldArr(array $orgCodes,$value='id')
    {
        if($orgCodes){
            return OilOrg::whereIn('orgcode',$orgCodes)->lists($value)->toArray();
        }else{
            return [];
        }

    }

    /**
     * @title   获取所有可用机构
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * mixed
     * @returns
     */
    static public function getAllOrgs(array $params=[],$month = '')
    {
        $object = OilOrg::Filter($params)->select('id as org_id','org_name','orgcode');
        $month && $object->where('latest_trade_time','>=',$month);
        return $object->where('is_del','0')->get()->toArray();
    }

    /**
     * @title   预处理数据中指定的orgcode字段，截取orgroot出来并返回
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param       $data
     * @param array $filedNameArr
     * @return array
     * @returns
     * array
     * @returns
     */
    static public function preOrgRoot($data,array $filedNameArr)
    {
        $orgRoots = [];
        if($data && $filedNameArr){
            foreach($data as $v){
                if(is_array($v)){
                    $v = (object)$v;
                }
                foreach($filedNameArr as $value){
                    if(isset($v->{$value})){
                        $str = $v->{$value};
                        $orgRoot = substr($str,0,6);
                        $orgRoots[$orgRoot] = $orgRoot;
                    }
                }
            }
        }

        return array_values($orgRoots);
    }

    /**
     * @param array $data
     * @param $orgRootInfo
     * @param array $filedNameArr
     * @return array
     */
    static public function convertOrgRoot2OrgName($data,$orgRootInfo,array $filedNameArr)
    {
        if($data && $filedNameArr){
            foreach($data as &$v){
                if(is_array($v)){
                    $v = (object)$v;
                }
                foreach($filedNameArr as $value){
                    $subName = '_'.$value;
                    $v->{$subName} = $v->{$value};
//                    $v->{$value} = isset($orgRootInfo[$v->{$value}]) ? $orgRootInfo[$v->{$value}] : $v->{$value};
                    if (isset($orgRootInfo[$v->{$value}])) {
                        $v->{$value} = $orgRootInfo[$v->{$value}];
                    } else {
                        if ($v->{$value} == $v->orgcode) {
                            $v->{$value} = $v->org_name;
                        } else {
                            $v->{$value} = self::getResField(['orgcode'=>$v->{$value}], 'org_name');
                        }
                    }
                }
            }
        }

        return $data;
    }

    /**
     * @title   查出顶级机构
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param $data
     * @return array
     * @returns
     * array
     * @returns
     */
    static public function handleData($data)
    {
        if($data){
            foreach($data as &$v){
                if(is_array($v)){
                    $v = (object)$v;
                }

                //txb 2018.7.6 展示全顶级机构
                if(isset($v->orgroot)){
                    if (substr($v->Org->orgcode, 0, 6) == OrgDefine::$wuCheOrgRoot) {
                        $v->_orgroot = substr($v->orgroot, 0, 10);
                    } else {
                        $v->_orgroot = substr($v->orgroot, 0, 6);
                    }
                }

                $v->_assign_type = AssignType::getById($v->assign_type);
                $v->assign_message = str_replace(',','-',$v->assign_message);
            }

            $orgRoots = OilOrg::preOrgRoot($data,['orgroot']);
            $orgRootInfo = OilOrg::getByOrgCodesMap($orgRoots);

            $data = OilOrg::convertOrgRoot2OrgName($data,$orgRootInfo,['orgroot']);
        }

        return $data;
    }


    /**
     * @title   根据机构名称查询机构信息
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param $orgName
     * @return mixed
     * @returns
     * mixed
     * @returns
     */
    static public function getByOrgName($orgName)
    {
        return OilOrg::where('org_name','=',$orgName)->where('is_del','=',0)->first();
    }

    /**
     * @title   根据机构名称查询机构信息
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param $orgName
     * @return mixed
     * @returns
     * mixed
     * @returns
     */
    static public function getByOrgNameAndOrgCode($orgName,$orgcode)
    {
        return OilOrg::where('org_name','=',$orgName)->where('orgcode','like',$orgcode.'%')->where('is_del','=',0)->first();
    }

    /**
     * @title   根据机构名称查询机构信息 from cache
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param $orgName
     * @return mixed
     * @returns
     * mixed
     * @returns
     */
    static public function getByCacheOrgName($orgName)
    {
        $cacheKey = var_export(__METHOD__,true).$orgName;
        $data = Cache::get($cacheKey);
        if(!$data){
            $data = OilOrg::where('org_name','=',$orgName)->where('is_del','=',0)->first();
            Cache::put($cacheKey,$data,60*60);
        }
        return $data;
    }

    /**
     * @title   获取机构运营商
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param $params
     * @return mixed
     * @returns
     * mixed
     * @returns
     */
    static public function getOrgOperator($params){
        $data = OilOrg::where('oil_org.id','=',$params)
            ->leftJoin('oil_operators','oil_org.operators_id','=','oil_operators.id')
            ->select('oil_org.operators_id','oil_operators.name as operators_name','oil_operators.company_code as company_code')
            ->first();
        return $data;
    }

    /**
     * @title   获取顶级机构
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @return array
     * @returns
     * array
     * @returns
     */
    static public function getTopOilOrg()
    {
        return Capsule::connection()->select('SELECT id, concat(orgcode, \' \', org_name) org_name, orgcode, org_name orgname FROM oil_org WHERE LENGTH(orgcode) = 6 AND is_del = 0');
    }

    /**
     * @title   获取各机构及其子级机构的数量
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param array $orgCodeList
     * @return array
     * @returns
     * array
     * @returns
     */
    static public function countOrgTree($orgCodeList = [])
    {
        $data = [];
        $where = '';
        if($orgCodeList){
            $orgCodes = [];
            foreach($orgCodes as $v){
                $orgCodes[] = "'".$v."'";
            }
            $orgs = implode(",",$orgCodes);
            $where = " AND a.orgcode in ($orgs)";
        }

        $sql = "SELECT
  a.orgcode AS orgcode,
  COUNT(*) AS total
FROM
  oil_org a
LEFT JOIN oil_org b ON b.orgcode LIKE CONCAT(a.orgcode, '%')
AND b.is_del = 0
WHERE
a.is_del = 0 $where
GROUP BY
  a.orgcode
ORDER BY
  a.orgcode";

        $result = Capsule::connection()->select($sql);
        if($result){
            foreach($result as $v){
                $data[$v->orgcode] = $v->total;
            }
        }

        return $data;
    }

    /**
     *
     * getLikeOrgCode
     * @param array $params
     * @return mixed
     * <AUTHOR>
     * @since ${DATE}
     */
    static public function getLikeOrgCodeForMap(array $params)
    {
        return OilOrg::where('orgcode','like',$params['orgcode'].'%')->where('is_del','=',0)->get();
    }

    static public function getByOrgCodes(array $orgCodes)
    {
        return self::whereIn('orgcode',$orgCodes)->where('is_del','0')->get();
    }

    /**
     * @title   根据机构统计月报表
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param array $params
     * @return array
     * @returns
     * array
     * @returns
     */
    static public function getOrgMonthMap(array $params)
    {
        //$orgcode = '2000BG'; //tmp

        $sql = "SELECT
	orgcode,
	org_name,
	charge_total,
	assign_total,
	sum(total_trade_money) as total_trade_money,
	sum(total_trade_num) as total_trade_num,
	sum(total_counts) as counts
FROM
	oil_org_month_statistic
WHERE
	orgcode like '".$params['orgcode']."%'
AND dates = '".$params['date']."'
GROUP BY
	orgcode";

        $record = Capsule::connection()->select($sql);

        return $record;
    }

    /**
     * 根据机构统计月报表
     */
    static public function getOrgMonthMap_bak(array $params)
    {
        $oilTradeType = \Fuel\Defines\TradesType::getTradesType(true);
        $firstday = isset($params['firstday']) ? $params['firstday'] : date('Y-m-01', strtotime('-1 month')).' 00:00:00';
        $lastday = isset($params['lastday']) ? $params['lastday'] : date('Y-m-d', strtotime("$firstday +1 month -1 day")).' 23:59:59';
        $orgcode = $params['orgcode'];
        //$orgcode = '2000BG'; //tmp

        $sql = "SELECT
	orgcode,
	org_name,
	charge_total,
	assign_total,
	sum(trade_money) as total_trade_money,
	sum(trade_num) as total_trade_num,
	count(*) as counts
FROM
	(
		SELECT
			oil_card_vice_trades.org_name,
			oil_org.orgcode,
			oil_card_vice_trades.trade_money,
			oil_card_vice_trades.trade_num,
			oil_account_money.assign_total,
			oil_account_money.charge_total
		FROM
			oil_card_vice_trades
		LEFT JOIN oil_org ON oil_card_vice_trades.org_id = oil_org.id
		LEFT JOIN oil_account_money ON oil_card_vice_trades.org_id = oil_account_money.id
		LEFT JOIN oil_type_no ON oil_card_vice_trades.oil_name = oil_type_no.oil_no
		WHERE
			LEFT (oil_org.orgcode, 6) = '".$orgcode."'
		AND oil_type_no.oil_type != 3
		AND oil_card_vice_trades.trade_type in (".$oilTradeType.")
		AND oil_card_vice_trades.trade_time > '".$firstday."'
		AND oil_card_vice_trades.trade_time < '".$lastday."'
	) a
GROUP BY
	orgcode";

        $record = Capsule::connection()->select($sql);

        return $record;
    }

    /**
     * @title   根据机构统计月报表
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param array $params
     * @return array
     * @returns
     * array
     * @returns
     */
    static public function getSingleOrgMonthMap(array $params)
    {
        $oilTradeType = TradesType::getTradesType(true);
        $month = isset($params['month']) ? date("Y-m",strtotime($params['month'])) : date('Y-m', strtotime('-1 month'));
        $orgcode = $params['orgcode'];
        $org_id = $params['org_id'];
        //$orgcode = '2000BG'; //tmp

        $sql = "SELECT
	a.org_id,a.orgcode,a.org_name,a.total_trade_money,a.total_trade_num,a.no_trades_total,a.no_trades_counts,
	a.counts,a.refundCount,
	b.assign_total,
	c.charge_total
FROM
	(
		SELECT
			oil_org.id AS org_id,
			oil_org.orgcode,
			oil_org.org_name,
			sum(
			  case when oil_type_no.oil_type != 3 then oil_card_vice_trades.trade_money else 0 END
			) AS total_trade_money,
			sum(
			    case when oil_type_no.oil_type != 3 then oil_card_vice_trades.trade_num else 0 END
			) AS total_trade_num,
			sum(
			    case when oil_type_no.oil_type != 3 then 1 else 0 END
			) AS counts,
			SUM(
            CASE
            WHEN oil_card_vice_trades.trade_money < 0 THEN
                1
            ELSE
                0
            END
        ) AS refundCount,
        sum(
				case when oil_type_no.oil_type = 3 then oil_card_vice_trades.trade_money else 0 END
			) AS no_trades_total,
        sum(
				case when oil_type_no.oil_type = 3 then 1 else 0 END
			) AS no_trades_counts
		FROM
			oil_card_vice_trades
		LEFT JOIN oil_org ON oil_card_vice_trades.org_id = oil_org.id
		LEFT JOIN oil_account_money ON oil_card_vice_trades.org_id = oil_account_money.id
		LEFT JOIN oil_type_no ON oil_card_vice_trades.oil_name = oil_type_no.oil_no
		WHERE
			oil_org.orgcode = '".$orgcode."'
		AND oil_card_vice_trades.trade_type in (".$oilTradeType.")
		AND oil_card_vice_trades.createtime like '".$month."%'
	) a
LEFT JOIN (
	SELECT
		org_id,
		sum(money_total) AS assign_total
	FROM
		oil_account_assign
	WHERE
		org_id = '".$org_id."'
	AND status = 1
	AND createtime like '".$month."%'
) b ON a.org_id = b.org_id
LEFT JOIN (
	SELECT
		org_id,
		sum(money) AS charge_total
	FROM
		oil_account_money_charge
	WHERE
		org_id = '".$org_id."'
	AND status = 1
	AND createtime like '".$month."%'
) c ON a.org_id = c.org_id";

        $record = Capsule::connection()->select($sql);

        return $record;
    }

    /**
     * @title   根据机构统的消费记录来源类型计月报表
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param array $params
     * @return array
     * @returns
     * array
     * @returns
     */
    static public function getSingleOrgMonthOilcomMap(array $params)
    {
        $oilTradeType = TradesType::getTradesType(true);
        $month = isset($params['month']) ? date("Y-m",strtotime($params['month'])) : date('Y-m', strtotime('-1 month'));
        $orgcode = $params['orgcode'];
        //$orgcode = '2000BG'; //tmp

        $sql = "SELECT
            oil_card_vice_trades.oil_com,
			oil_card_vice_trades.regions_name,
			sum(oil_card_vice_trades.trade_money) as trade_money,
			count(*) as counts,
            sum(
                CASE
                WHEN oil_type_no.oil_type != 3 THEN
                    oil_card_vice_trades.trade_money
                ELSE
                    0
                END
            ) AS cash_trade_money,
            sum(
                        case when oil_type_no.oil_type = 3 then oil_card_vice_trades.trade_money else 0 END
                    ) AS no_trade_money,
            sum(
                        case when oil_card_vice_trades.trade_type = '积分加油' then oil_card_vice_trades.trade_money else 0 END
                    ) AS jifen_trade_money
		FROM
			oil_card_vice_trades
		LEFT JOIN oil_org ON oil_card_vice_trades.org_id = oil_org.id
		LEFT JOIN oil_account_money ON oil_card_vice_trades.org_id = oil_account_money.id
		LEFT JOIN oil_type_no ON oil_card_vice_trades.oil_name = oil_type_no.oil_no
		WHERE
			oil_org.orgcode = '".$orgcode."'
		AND oil_card_vice_trades.trade_type in (".$oilTradeType.")
		AND oil_card_vice_trades.createtime like '".$month."%'
        GROUP BY oil_card_vice_trades.oil_com,oil_card_vice_trades.regions_name";

        $record = Capsule::connection()->select($sql);

        return $record;
    }

    /**
     * @title   机构统消费分析
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param array $params
     * @return array
     * @returns
     * array
     * @returns
     */
    static public function getOrgTradeAnalysis(array $params)
    {
        $oilTradeType = TradesType::getTradesType(true);
        $firstday = isset($params['firstday']) && $params['firstday'] ? $params['firstday'].' 00:00:00' : date('Y-m-01', strtotime('-1 month')).' 00:00:00';
        $lastday = isset($params['lastday']) && $params['lastday'] ? $params['lastday'].' 23:59:59' : date('Y-m-d', strtotime("$firstday +1 month -1 day")).' 23:59:59';
        $orgcode = $params['orgcode'];
        $whereViceNo = '';
        if(isset($params['tag_id']) && $params['tag_id']){
            $viceNo = OilViceTags::getViceNoByTagId(explode(',',$params['tag_id']));
            if($viceNo){
                $viceNo = array_unique($viceNo);
                $viceNo = implode(',',$viceNo);
                $whereViceNo = " AND oil_card_vice_trades.vice_no IN($viceNo)";
            }else{
                $whereViceNo = " AND oil_card_vice_trades.vice_no = ''";
            }
        }

        $sql = "SELECT
  orgcode,
  org_name,
  ROUND(total_trade_num / (counts - refundCount * 2), 2) AS avg_total_trade_num,
  ROUND(total_trade_money / (counts - refundCount * 2), 2) AS avg_total_trade_money,
  total_trade_money,
  total_trade_num,
  cards,
  counts - refundCount * 2 AS counts
FROM
  (
    SELECT
      count(*) AS cards,
      sum(total_trade_money) AS total_trade_money,
      sum(counts) AS counts,
      sum(total_trade_num) total_trade_num,
      org_name,
      orgcode,
      sum(refundCount) AS refundCount
    FROM
      (
        SELECT
          oil_card_vice_trades.vice_no,
          sum(
            oil_card_vice_trades.trade_money
          ) AS total_trade_money,
          oil_org.org_name,
          oil_org.orgcode,
          count(*) AS counts,
          sum(
            oil_card_vice_trades.trade_num
          ) AS total_trade_num,
        SUM(
            CASE
            WHEN oil_card_vice_trades.trade_money < 0 THEN
                1
            ELSE
                0
            END
        ) AS refundCount
        FROM
          oil_card_vice_trades
        LEFT JOIN oil_org ON oil_card_vice_trades.org_id = oil_org.id
        WHERE
          oil_org.orgcode LIKE '".$orgcode."%'
        AND oil_card_vice_trades.trade_time >= '".$firstday."'
        AND oil_card_vice_trades.trade_time <= '".$lastday."'
        AND oil_card_vice_trades.trade_type in (".$oilTradeType.")
        AND oil_org.is_del = 0
        $whereViceNo
        GROUP BY
          oil_org.orgcode,
          oil_card_vice_trades.vice_no
      ) a
    GROUP BY
      orgcode
  ) b";
        Log::debug('sqL：'.$sql,[],'oilOrg');

        $record = Capsule::connection()->select($sql);

        return $record;
    }

    /**
     * @title   通过条件过滤出指定字段数组
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param array $params
     * @param       $pluckField
     * @return mixed
     * @returns
     * mixed
     * @returns
     */
    static public function getPluckOrgIds(array $params, $pluckField)
    {
        return OilOrg::Filter($params)->pluck($pluckField)->toArray();
    }

    /*
     * 修改子集机构的sub_org_name
     */
    static public function updateSubOrgName(array $addArr,$mode = "")
    {
        \helper::argumentCheck(['orgcode'],$addArr);
        $subIds = [];
        Log::error("params,mode-=-=:".$mode,[$addArr],"update_sub_");
        if(isset($addArr['sub_org_name']) && $addArr['sub_org_name']){
            //获取机构id合集
            //G7WALLET-4967
            if( !in_array($mode,[1,2]) ){
                throw new \RuntimeException('开票方式不合法！', 2);
            }
            if($mode == 1){
                $subIds = OilOrg::getByOrgcodeLike(substr($addArr['orgcode'],0,6));
            }else{
                $subIds = OilOrg::getInferior(['orgcode'=>$addArr['orgcode']]);
            }
            if($subIds){
                OilOrg::whereIn('id',$subIds)->update([
                    'sub_org_name' => $addArr['sub_org_name']
                ]);
            }
        }

        return $subIds;
    }

    /**
     * @title   支持foss端机构检索
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @since
     * @params  type filedName required?
     * @param array $params
     * @returns
     * []
     * @returns
     */
    static public function getOilOrg(array $params=[])
    {
        Capsule::connection()->enableQueryLog();

        $orgList = ["20013Z","201XW3"];
        if( in_array(API_ENV,['pro','prod']) ){
            $orgList = ['201XW3','205C8M'];
        }
        $sqlObj = self::select(Capsule::connection()->raw("id, id as org_id, concat(orgcode, ' ', org_name) org_name, orgcode, org_name orgname,operators_id"))
            ->where('is_del',0);
        if (isset($params['is_rebate']) && $params['is_rebate']) {
            $sqlObj->where(function($query) use($params,$orgList){
                $query->whereRaw('length(orgcode) = 6')
                    ->orWhereRaw("left(orgcode, 6) in ('".implode("','",$orgList)."') and length(orgcode) = 10");
            });
        }

        //取所有先开后结的机构
        if(isset($params['is_first_open_receipt']) && $params['is_first_open_receipt']){
            $orgCodes = (new OrgConfigService())->getBigCustomerOrgCode();
            $orgCodes = $orgCodes ? $orgCodes : ['666666']; //如果无配置，默认一个不存在的机构
            if($orgCodes){
                //先开后结标识 去除先开后 is_first_open_receipt=1为圆通模式，2常规模式
                if ($params['is_first_open_receipt'] == 1) {
                    $sqlObj->whereIn("orgcode",$orgCodes);
                }elseif($params['is_first_open_receipt'] == 2){
                    $sqlObj->whereNotIn("orgcode",$orgCodes);
                }
            }
        }

        if(isset($params['id']) && $params['id']){
            $sqlObj->where('id',trim($params['id']));
        }elseif(isset($params['orgcode']) && $params['orgcode']){
            $sqlObj->where('orgcode',trim($params['orgcode']));
        }elseif(isset($params['keyword']) && $params['keyword']){
            $sqlObj->where(function($query) use($params,$orgList){
                $query->where('orgcode','like',trim($params['keyword'].'%'))
                    ->orWhere('org_name','like','%'.trim($params['keyword']).'%');
                $query->where(function($query) use($params,$orgList) {
                    if (isset($params['is_root']) && $params['is_root']) {
                        $query->whereRaw('length(orgcode) = 6')
                            ->orWhereRaw("left(orgcode, 6) in ('".implode("','",$orgList)."') and length(orgcode) = 10");
                    }
                });
            });
        }elseif(isset($params['is_root']) && $params['is_root']){
            $sqlObj->whereRaw('length(orgcode) = 6')
                ->orWhereRaw("left(orgcode, 6) in ('".implode("','",$orgList)."') and length(orgcode) = 10");
            //$sqlObj->whereRaw('length(orgcode) = 6');
        }

        //"油品机构维护"筛选「机构」支持按照运营商筛选
        $sqlObj->dataRange();
        return $sqlObj->orderBy('orgcode')->take(100)->get();
    }

    /**
     * @title  查询机构的返利总计
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @since
     * @params  type filedName required?
     * @param array $params
     * @returns
     * []
     * @returns
     */
    static public function getAllOilOrg(array $params=[])
    {
        $obj = Capsule::connection();
        $obj->statement("SET SESSION group_concat_max_len = 10240");
        if(isset($params['orgcode']) && !empty($params['orgcode'])) {
            $data = self::select($obj->raw("LEFT(orgcode,6) as orgroot,GROUP_CONCAT(id) as org_ids"))
                ->where('is_del', 0)->where("orgcode","like",$params['orgcode']."%")->groupby("orgroot")->get()->toArray();
        }else{
            $data = self::select($obj->raw("LEFT(orgcode,6) as orgroot,GROUP_CONCAT(id) as org_ids"))
                ->where('is_del', 0)->groupby("orgroot")->get()->toArray();
        }
        return $data;
    }

    /**
     * @title 根据orgcode获取机构账户及运营商
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param $account_no
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getOrgInfo($orgcodes)
    {
        return self::whereIn("oil_org.orgcode",$orgcodes)
            ->where("oil_org.is_del",0)
            ->leftJoin("oil_account_money",'oil_org.id' ,'=','oil_account_money.org_id')
            ->leftJoin("oil_operators",'oil_operators.id','=','oil_org.operators_id')
            ->select("oil_operators.name","oil_account_money.account_no","oil_operators.id as opertor_id","oil_org.id","oil_org.org_name","oil_org.orgcode")
            ->orderby("oil_org.id","desc")
            ->get()->toArray();
    }

    /**
     * 获取本级机构下的所有子级机构
     * @param $orgcode 机构编码
     * @param int $include_self 是否包括本级 0否 其他值是
     * @return array
     */
    static public function getSubOrgCodes($orgcode, $include_self=0)
    {
        if (!$orgcode || strlen($orgcode) < 6) {
            return [];
        }

        $res = OilOrg::where('orgcode','like',$orgcode.'%')->where('is_del','=',0)->pluck('orgcode')->toArray();
        if (!$include_self) {
            foreach ($res as $key => $val) {
                if ($val == $orgcode) {
                    unset($res[$key]);
                }
            }
        }

        return $res;
    }

    /**
     * 获取本级机构的所有父级机构
     * @param $orgcode
     * @param int $include_self 是否包括本级 0否 其他值是
     * @return array
     */
    static public function getParentOrgCodes($orgcode, $include_self=0)
    {
        if (!$orgcode || strlen($orgcode) < 6) {
            return [];
        }

        $orgcodes = [];
        $length = (strlen($orgcode) - 6) / 2;
        if ($length > 0) {
            foreach (range(1, $length) as $value){
                $orgcodes[] = substr($orgcode, 0, 6+($value-1)*2);
            }
        }

        if ($include_self) {
            array_push($orgcodes, $orgcode);
        }

        return $orgcodes;
    }

    /**
     * 获取父级机构列表
     * @param $orgcode 机构名称
     * @param int $include_self 是否包括本级
     * @param array $fields 要取的字段
     * @return mixed
     */
    static public function getParentsOrgList($orgcode, $include_self=0, $fields = ['id','orgcode','org_name'])
    {
        $orgcodes = self::getParentOrgCodes($orgcode, $include_self);
        $sqlObj = OilOrg::whereIn('orgcode', $orgcodes)->where('is_del', '=', 0);
        if (!empty($fields)) {
            $sqlObj->select($fields);
        }

        return $sqlObj->get()->toArray();
    }

    /**
     * 获取子级机构列表
     * @param $orgcode 机构名称
     * @param int $include_self 是否包括本级
     * @param array $fields 要取的字段
     * @return mixed
     */
    static public function getSubOrgList($orgcode, $include_self=0, $fields = ['id','orgcode','org_name'])
    {
        $orgcodes = self::getSubOrgCodes($orgcode, $include_self);
        $sqlObj = OilOrg::whereIn('orgcode', $orgcodes)->where('is_del', '=', 0);
        if (!empty($fields)) {
            $sqlObj->select($fields);
        }

        return $sqlObj->get()->toArray();
    }

    /**
     * 格式化机构列表
     * @param $data
     * @return mixed
     */
    static public function formatOrgList($data)
    {
        foreach ($data as &$v) {
            $v->receipt_mode_txt  = $v->receipt_mode == 2 ? '独立核算' : "集中核算";
            $v->_status           = OrgStatus::getById($v->status);
            $v->_exclusive_custom = ExclusiveCustom::getById($v->exclusive_custom);
            $v->_is_test          = $v->is_test == 1 ? '否' : '是';
            //$v->is_first_receipt_apply     = $v->first_apply_receipt ? '否' : '是';
            $v->_single_day_ceil = SingleDayCeil::getById($v->single_day_ceil);
            //txb 2018.6.13 查询机构下的客户Id
            $customerInfo = \Models\OilOrgCustomerRelation::getCustomerInfo(["org_id" => $v->id]);
            if (!empty($customerInfo)) {
                $v->customer_id = $customerInfo->customer_id;
            } else {
                $v->customer_id = "";
            }
            if ($v->agent_orgcode) {
                $cacheData = file_get_contents(APP_WWW_ROOT . "/../tmp/rootOrgCache.php");
                if (count($cacheData) > 0) {
                    $cacheJson = json_decode($cacheData, TRUE);
                    if (array_key_exists($v->agent_orgcode, $cacheJson)) {
                        $v->agent_org_txt = $v->agent_orgcode . " " . $cacheJson[$v->agent_orgcode];
                    }
                }
                if (empty($v->agent_org_txt)) {
                    $orgInfo          = OilOrg::getByOrgcode($v->agent_orgcode);
                    $v->agent_org_txt = $orgInfo->orgcode . " " . $orgInfo->org_name;
                }
            } else {
                $v->agent_org_txt = '无';
            }
            $_info = GspSysUsers::getById(['id'=>$v->sys_user_id]);
            if($_info){
                $v->staff_txt = $_info->true_name ? $_info->true_name : $_info->user_name;
            }
            $v->belongto_saler_txt = $v->belongto_saler ? $v->belongto_saler : '无';
            $v->is_system_orgcode_txt = isset(OrgStatus::$is_system_orgcode_map[$v->is_system_orgcode]) ? OrgStatus::$is_system_orgcode_map[$v->is_system_orgcode] : "";
        }

        return $data;
    }

    /*
     * 获取两个机构之间到机构关系
     */
    static public function getOrgTreePart($org_code_first,$org_code_end)
    {
        $cache_name = 'orgall'.$org_code_first.$org_code_end;

        $data = Cache::get($cache_name);
        if(!$data){
            $start_len = strlen($org_code_first);
            $end_len = strlen($org_code_end);
            $org_name_list = $org_code_list = [];
            if($end_len > $start_len){
                for($i = $end_len;$i>=$start_len;$i = $i-2){
                    $org_code_list[] = substr($org_code_end,0,$i);
                }
            }else{
                $org_code_list[] = $org_code_end;
            }

            if($org_code_list){
                foreach ($org_code_list as $orgcode){
                    $data = (new \UCenterSDK\User())->getOrgByCode(['orgcode'=>$orgcode]);
                    if($data){
                        $org_name_list[] = $data->name;
                    }
                }
            }

            if($org_name_list){
                $res_data = array_reverse($org_name_list);
            }else{
                $res_data = [];
            }

            $data = implode(' | ',$res_data);

            Cache::put($cache_name,$data,86400);
        }


        return $data;
    }

    /**
     * 取字段值 返回第一个值
     * @param array $params
     * @param $pluckField
     * @return mixed
     */
    public static function getResField(array $params, $pluckField)
    {
        $res = self::getPluckFields($params, $pluckField);

        return !empty($res[0]) ? $res[0] : '';
    }

    /**
     * 取字段值
     * @param array $params
     * @param $pluckField
     * @return array
     */
    static public function getPluckFields(array $params, $pluckField)
    {
        $res = self::Filter($params)->pluck($pluckField);

        return !$res ? [] : $res->toArray();
    }

    public static function getNextOrgcodeLength($orgcode)
    {
        if (!$orgcode) {
            return -1;
        }

        $ret = OilOrg::where('orgcode','like',$orgcode.'%')
            ->where('is_del','=',0)
            ->whereRaw('LENGTH(orgcode) > '.strlen($orgcode))
            ->orderByRaw('LENGTH(orgcode) asc')
            ->limit(1)
            ->pluck('orgcode')
            ->toArray();

        return !empty($ret[0]) ? strlen($ret[0]) : -1;
    }

    /**
     * @param $orgcode
     * @param int $include_self
     * @param array $exceptOrgCodes
     * @return array
     */
    static public function getSubOrgIdsByOrgcode($orgcode, $include_self=0, $exceptOrgCodes=[])
    {
        if (!$orgcode || strlen($orgcode) < 6) {
            return [];
        }

        $sqlObj = OilOrg::where('orgcode','like',$orgcode.'%')->where('is_del','=',0);
        if (!$include_self) {
            $sqlObj->where('orgcode', '!=', $orgcode);
        }
        if (!empty($exceptOrgCodes)) {
            $sqlObj->whereNotIn('orgcode', $exceptOrgCodes);
        }
        $orgIdsObj = $sqlObj->pluck('id');

        return !$orgIdsObj ? [] : $orgIdsObj->toArray();
    }

    public static function getListByFilter($params, $fields=['*'])
    {
        if (empty($params)) {
            return [];
        }

        $obj = OilOrg::Filter($params);

        if (! empty($params['groupBy'])) {
            foreach ($params['groupBy'] as $k) {
                $obj->groupBy($k);
            }
        }

        if (! empty($params['selectRaw'])) {
            $obj->selectRaw($params['selectRaw']);
        } else {
            $obj->select($fields);
        }

        return $obj->get()->toArray();
    }

    /**
     * 判断一批机构ID是否在同一个机构树上
     * @param $orgIds
     * @return bool
     */
    public static function isOrgOneTreeById($orgIds)
    {
        if (!$orgIds) {
            return false;
        }
        $isOneTree = false;
        $orgCodes = self::getPluckFields(['idList'=>$orgIds], 'orgcode');
        if (!empty($orgCodes)) {
            $compareTopOrgCode = substr($orgCodes[0], 0, 6);
            foreach ($orgCodes as $orgcode) {
                if (substr($orgcode, 0, 6) != $compareTopOrgCode) {
                    $isOneTree = true;
                }
            }
        }
        return $isOneTree;
    }

    /**
     * 判断一批机构编码是否在同一个机构树上
     * @param $orgcodes
     * @return bool
     */
    public static function isOrgOneTreeByOrgcodes($orgcodes)
    {
        $isOneTree = false;
        $compareTopOrgCode = substr(array_shift($orgcodes), 0, 6);
        foreach ($orgcodes as $orgcode) {
            if (substr($orgcode, 0, 6) != $compareTopOrgCode) {
                $isOneTree = true;
            }
        }
        return $isOneTree;
    }

    //获取全量顶级机构For换签
    static public function getOrgRoot($params = [])
    {
        $sql = "SELECT LEFT
		( orgcode, 6 ) AS root,
		GROUP_CONCAT( DISTINCT operators_id ) as operators_str,
		GROUP_CONCAT( DISTINCT receipt_mode ) AS mode_str,
		min(createtime) as min_time,
		group_concat(distinct can_change_sign ) as can_sign,
		group_concat(distinct sign_data_integrity ) as sign_data,
		group_concat(distinct sign_receipt_mode ) as sign_mode,
		group_concat(distinct sign_match ) as sign_match
	FROM
		oil_org 
	WHERE LEFT(orgcode,6) != '201XW3' and is_del = 0 and status = 1 and is_test = 1 ";

        if( isset($params['orgcode']) && !empty($params['orgcode']) ){
            $sql .= " and orgcode like '".$params['orgcode']."%'";
        }

        if($params['can_change_sign'] == 10){
            $sql .= " and sign_data_integrity = 10 and sign_receipt_mode in (10,20) and sign_match = 1";
        }
        if( $params['can_change_sign'] == 20 ){
            $sql .= " and (sign_data_integrity != 10 or sign_receipt_mode not in (10,20) or sign_match = 2)";
        }

        $sql .= " group by LEFT(orgcode,6);";
        return Capsule::connection("slave")->select($sql);
    }
}
