<?php
/**
 * oil_card_vice_snapshot
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/11/22
 * Time: 14:23:38
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCardViceSnapshot extends \Framework\Database\Model
{
    protected $table = 'oil_card_vice_snapshot';

    protected $guarded  = ["id"];
    protected $fillable = ["id","vice_id","vice_no","org_id","createtime","updatetime",];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        return $query;
    }

    /**
     * oil_card_vice_snapshot 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCardViceSnapshot::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_card_vice_snapshot 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardViceSnapshot::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardViceSnapshot::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_card_vice_snapshot 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCardViceSnapshot::create($params);
    }

    /**
     * oil_card_vice_snapshot 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardViceSnapshot::find($params['id'])->update($params);
    }

    /**
     * oil_card_vice_snapshot 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCardViceSnapshot::destroy($params['ids']);
    }

    static public function addVices(array $viceids)
    {
        if($viceids){
            $data = OilCardVice::whereIn('id',$viceids)->select('vice_no','org_id')->get();

            $insertArr = [];
            if($data){
                foreach ($data as $val){
                    $insertArr[] = ['vice_no'=>$val->vice_no,'org_id'=>$val->org_id,'createtime'=>\helper::nowTime()];
                }

                try{
                    Capsule::connection()->table('oil_card_vice_snapshot')->insert($insertArr);
                }catch (\Exception $e){

                }
            }
        }

        return TRUE;
    }

    static public function addByViceNos(array $viceNos)
    {
        if($viceNos){
            $data = OilCardVice::whereIn('vice_no',$viceNos)->select('vice_no','org_id')->get();

            $insertArr = [];
            if($data){
                foreach ($data as $val){
                    $insertArr[] = ['vice_no'=>$val->vice_no,'org_id'=>$val->org_id,'createtime'=>\helper::nowTime()];
                }

                try{
                    Capsule::connection()->table('oil_card_vice_snapshot')->insert($insertArr);
                }catch (\Exception $e){

                }
            }
        }

        return TRUE;
    }

    static public function searchViceNo(array $params)
    {
        $sqlObj = OilCardViceSnapshot::leftJoin('oil_org','oil_org.id','=','oil_card_vice_snapshot.org_id')
            ->where('oil_org.orgcode','like',$params['orgcode'].'%');

        if(isset($params['vice_no']) && $params['vice_no']){
            $sqlObj->where('oil_card_vice_snapshot.vice_no','like','%'.$params['vice_no'].'%');
        }

        $data = $sqlObj->select('oil_card_vice_snapshot.id','oil_card_vice_snapshot.vice_no')
        ->get();

        return $data;
    }


}