<?php
/**
 * 副卡分配记录表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2017/04/14
 * Time: 15:14:30
 */

namespace Models;

use Fuel\Defines\OilCom;
use Fuel\Request\dspClient;
use Illuminate\Database\Capsule\Manager as Capsule;
use Kafka\Log;

class OilViceAssign extends \Framework\Database\Model
{
    protected $table = 'oil_vice_assign';
    
    protected $guarded  = ["id"];
    protected $fillable = ['api_id', 'vice_no', 'assign_money', 'card_remain', 'card_owner', 'assign_jifen', 'trade_place', 'trade_status', 'trade_type', 'assign_time', 'fetch_time', 'createtime', 'updatetime', 'main_no'];
    
    public function getFillAble()
    {
        return $this->fillable;
    }
    
    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }
        
        //Search By api_id
        if (isset($params['api_id']) && $params['api_id'] != '') {
            $query->where('api_id', '=', $params['api_id']);
        }
        
        //Search By vice_no
        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $viceNoArr = explode('|', $params['vice_no']);
            if (count($viceNoArr) == 1) {
                $query->where('oil_vice_assign.vice_no', '=', $params['vice_no']);
            } else {
                $query->whereIn('oil_vice_assign.vice_no', $viceNoArr);
            }
        }
        
        if (isset($params['viceNo']) && $params['viceNo'] != '') {
            $query->where('oil_vice_assign.vice_no', $params['viceNo']);
        }
        
        //Search By card_from
        if (isset($params['card_fromIn']) && $params['card_fromIn'] != '') {
            $query->whereIn('oil_card_vice.card_from', $params['card_fromIn']);
        }
        
        //Search By oil_com
        if (isset($params['oil_comIn']) && $params['oil_comIn'] != '') {
            $query->whereIn('oil_card_vice.oil_com', $params['oil_comIn']);
        }
        
        //Search By main_no
        if (isset($params['main_no']) && $params['main_no'] != '') {
            $query->where('oil_card_main.main_no', 'like', '%' . $params['main_no'] . '%');
        }
        
        //Search By assign_money
        if (isset($params['assign_money']) && $params['assign_money'] != '') {
            $query->where('assign_money', '=', $params['assign_money']);
        }
        
        //Search By card_remain
        if (isset($params['card_remain']) && $params['card_remain'] != '') {
            $query->where('card_remain', '=', $params['card_remain']);
        }
        
        //Search By card_owner
        if (isset($params['card_owner']) && $params['card_owner'] != '') {
            $query->where('oil_vice_assign.card_owner', 'like', '%' . $params['card_owner'] . '%');
        }
        
        //Search By assign_jifen
        if (isset($params['assign_jifen']) && $params['assign_jifen'] != '') {
            $query->where('assign_jifen', '=', $params['assign_jifen']);
        }
        
        //Search By trade_place
        if (isset($params['trade_place']) && $params['trade_place'] != '') {
            $query->where('oil_vice_assign.trade_place', 'like', '%' . $params['trade_place'] . '%');
        }
        
        //Search By trade_status
        if (isset($params['trade_status']) && $params['trade_status'] != '') {
            $query->where('oil_vice_assign.trade_status', '=', $params['trade_status']);
        }
        
        //Search By trade_type
        if (isset($params['trade_type']) && $params['trade_type'] != '') {
            $query->where('oil_vice_assign.trade_type', '=', $params['trade_type']);
        }
        
        //Search By assign_time
        if (isset($params['assign_time']) && $params['assign_time'] != '') {
            $query->where('oil_vice_assign.assign_time', '=', $params['assign_time']);
        }
        
        //Search By assign_timeGe
        if (isset($params['assign_timeGe']) && $params['assign_timeGe'] != '') {
            $query->where('oil_vice_assign.assign_time', '>=', $params['assign_timeGe']);
        }
        //Search By assign_timeLe
        if (isset($params['assign_timeLe']) && $params['assign_timeLe'] != '') {
            $assign_timeLe = preg_match('/^\d{4}(\-|\/|.)\d{1,2}\1\d{1,2}$/', $params['assign_timeLe'], $matches) ? $params['assign_timeLe'] . ' 23:59:59' : $params['assign_timeLe'];
            $query->where('oil_vice_assign.assign_time', '<=', $assign_timeLe);
        }
        
        if (isset($params['assign_timeGt']) && $params['assign_timeGt'] != '') {
            $query->where('oil_vice_assign.assign_time', '>', $params['assign_timeGt']);
        }
        //Search By assign_timeLt
        if (isset($params['assign_timeLt']) && $params['assign_timeLt'] != '') {
            $query->where('oil_vice_assign.assign_time', '<', $params['assign_timeLt']);
        }
        
        //Search By assign_timeLne
        if (isset($params['assign_timeLne']) && $params['assign_timeLne'] != '') {
            $query->where('oil_vice_assign.assign_time', '<', $params['assign_timeLne']);
        }
        
        //Search By fetch_time
        if (isset($params['fetch_time']) && $params['fetch_time'] != '') {
            $query->where('oil_vice_assign.fetch_time', '=', $params['fetch_time']);
        }
        
        //Search By fetch_timeGe
        if (isset($params['fetch_timeGe']) && $params['fetch_timeGe'] != '') {
            $query->where('oil_vice_assign.fetch_time', '>=', $params['fetch_timeGe']);
        }
        //Search By fetch_timeLe
        if (isset($params['fetch_timeLe']) && $params['fetch_timeLe'] != '') {
            $query->where('oil_vice_assign.fetch_time', '<=', $params['fetch_timeLe']);
        }
        
        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }
        
        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $createtimeLe = preg_match('/^\d{4}(\-|\/|.)\d{1,2}\1\d{1,2}$/', $params['createtimeLe'], $matches) ? $params['createtimeLe'] . ' 23:59:59' : $params['createtimeLe'];
            $query->where('oil_vice_assign.createtime', '<=', $createtimeLe);
        }
        
        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('oil_vice_assign.createtime', '>=', $params['createtimeGe']);
        }
        
        if (isset($params['createtimeLt']) && $params['createtimeLt'] != '') {
            $query->where('oil_vice_assign.createtime', '<', $params['createtimeLt']);
        }
        
        if (isset($params['createtimeGt']) && $params['createtimeGt'] != '') {
            $query->where('oil_vice_assign.createtime', '>', $params['createtimeGt']);
        }
        
        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }
        
        //Search By trade_type
        if (isset($params['trade_typeNeq']) && $params['trade_typeNeq'] != '') {
            $query->where('oil_vice_assign.trade_type', '!=', '');
        }
        
        //Search By trade_place
        if (isset($params['trade_placeNeq']) && $params['trade_placeNeq'] != '') {
            $query->where('oil_vice_assign.trade_place', '!=', '');
        }
        
        //Search By account_nameIn
        if (isset($params['account_nameIn']) && $params['account_nameIn'] != '') {
            $query->whereIn('oil_card_main.account_name', $params['account_nameIn']);
        }
        
        return $query;
    }
    
    /**
     * 副卡分配记录表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page']  = isset($params['page']) ? $params['page'] : 1;
        
        $sqlObj        = OilViceAssign::
        leftJoin('oil_card_vice', 'oil_vice_assign.vice_no', '=', 'oil_card_vice.vice_no')
            ->leftJoin('oil_card_main', 'oil_card_vice.card_main_id', '=', 'oil_card_main.id')
            ->select(Capsule::connection()->raw('oil_vice_assign.*,oil_card_main.main_no'))
            ->orderBy('oil_vice_assign.createtime', 'desc');
        $oilViceAssign = new OilViceAssign();
        $sqlObj        = $oilViceAssign->scopeFilter($sqlObj, $params);
        //$sqlObj = OilViceAssign::Filter($params);
        // if (isset($params['_export']) && $params['_export'] == 1) {
        //    $data = $sqlObj->get();
        //} else {
//        $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        // }

        if  (isset($params['skip']) && isset($params['take'])) {
            $data = $sqlObj->skip(intval($params['skip']))->take(intval($params['take']))->get();
        } else if (isset($params['count']) && $params['count'] == 1) {
            $data = $sqlObj->count();
        }else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        
        return $data;
    }
    
    /**
     * 副卡分配记录表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilViceAssign::find($params['id']);
    }
    
    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilViceAssign::lockForUpdate()->where('id', $params['id'])->first();
    }
    
    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByApiIdLock(array $params)
    {
        \helper::argumentCheck(['api_id'], $params);
        
        return OilViceAssign::lockForUpdate()->where('api_id', $params['api_id'])->first();
    }
    
    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByUniKey(array $params)
    {
        return OilViceAssign::Filter($params)->first();
    }
    
    /**
     * 执行队列时判断数据是否存在
     * @param array $params
     * @return mixed
     */
    static public function getUniqueKeyForQueue(array $params)
    {
        return OilViceAssign::where('api_id', $params['api_id'])->orWhere(function ($query) use ($params) {
            $query->where('vice_no', $params['viceNo'])->where('assign_money', $params['assign_money'])->where('assign_time', $params['assign_time']);
        })->first();
    }
    
    /**
     * 悲观锁查询最后分配时间
     * @param array $params
     * @return object
     */
    static public function maxAssignTimeLock()
    {
        return OilViceAssign::max('assign_time');
    }
    
    /**
     * 副卡分配记录表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilViceAssign::create($params);
    }
    
    static public function batchAdd(array $params)
    {
        return OilViceAssign::insert($params);
    }
    
    /**
     * PDO批量添加
     * @param array $apiData
     * @return bool|int
     */
    static public function batchInsertByPdo($apiData = [])
    {
        //批量入库
        $data = false;
        if ($apiData) {
            $batchInsertSqlArr = [];
            foreach ($apiData as $v) {
                $fieldArr  = [];
                $valuesArr = [];
                foreach ($v as $key => $val) {
                    $fieldArr[]  = "`" . $key . "`";
                    $valuesArr[] = "'$val'";
                }
                $batchInsertSqlArr[] = "insert into oil_vice_assign (" . implode(",", $fieldArr) . ") values (" . implode(",", $valuesArr) . ")";
            }
            $batchInsertSql = implode(";", $batchInsertSqlArr);
            $data           = Capsule::connection()->getPdo()->exec($batchInsertSql);
        }
        
        return $data;
    }
    
    /**
     * 副卡分配记录表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilViceAssign::find($params['id'])->update($params);
    }
    
    /**
     * 副卡分配记录表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);
        
        return OilViceAssign::destroy($params['ids']);
    }
    
    /**
     * 获取卡分配的对账数据
     * @param array $params
     * @return int
     */
    static public function getAssignData($start_time, $end_time)
    {
        Capsule::connection()->enableQueryLog();
        $tt  = OilViceAssign::whereBetween("oil_vice_assign.assign_time", array($start_time, $end_time))
            ->leftJoin('oil_card_vice', 'oil_card_vice.vice_no', '=', 'oil_vice_assign.vice_no')
            ->leftJoin('oil_card_main', 'oil_card_main.id', '=', 'oil_card_vice.card_main_id')
            ->select(Capsule::connection()->raw("sum(if(oil_vice_assign.trade_type = '单位汇总(出)',oil_vice_assign.assign_money * -1,assign_money)) as assign_money,oil_vice_assign.vice_no,oil_card_main.main_no"))
            ->where("oil_vice_assign.assign_money", "!=", 0)
            ->whereNotIn("oil_card_vice.card_from",[40,41])
            ->whereRaw("if (oil_card_vice.oil_com in (1,52),oil_vice_assign.api_id is Null,1=1)")
            #->whereNull("oil_vice_assign.api_id")
            #->where("oil_vice_assign.trade_status","!=",'失败')
            ->groupby("oil_card_vice.vice_no")
            ->orderby("oil_vice_assign.createtime", 'asc')
            ->get()->toArray();
        $Sql = Capsule::connection()->getQueryLog();
        //print_r($Sql);exit;
        return $tt;
    }
    
    
    /**
     * 获取卡分配的对账数据
     * @param array $params
     * @return int
     */
    static public function getNewList(array $params)
    {
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page']  = isset($params['page']) ? $params['page'] : 1;
        if (API_ENV == 'dev') {
            $connection = "";
        } else {
            $connection = "slave";
        }
        
        $accountList = [];
        if (isset($params['orgcodeLk']) && $params['orgcodeLk'] != '') {
            $accountList = OilCustomerCard::getAccountList(['is_parent' => 1, "orgcode" => $params['orgcodeLk']]);
        }
        
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $accountList = OilCustomerCard::getAccountList(["orgcode" => $params['orgcode']]);
        }
        
        if (count($accountList) > 0) {
            $params['account_nameIn'] = $accountList->toArray();
        } else {
            $emptyData['total']         = 0;
            $emptyData['per_page']      = 50;
            $emptyData['current_page']  = 1;
            $emptyData['last_page']     = 0;
            $emptyData['next_page_url'] = null;
            $emptyData['prev_page_url'] = null;
            $emptyData['from']          = null;
            $emptyData['to']            = null;
            $emptyData['data']          = [];
            return $emptyData;
        }
        
        //Capsule::connection($connection)->enableQueryLog();
        $sqlObj = Capsule::connection($connection)->table("oil_vice_assign")
            ->leftJoin('oil_card_vice', 'oil_vice_assign.vice_no', '=', 'oil_card_vice.vice_no')
            ->leftJoin('oil_card_main', 'oil_card_vice.card_main_id', '=', 'oil_card_main.id')
            ->leftJoin('oil_org', 'oil_card_vice.org_id', '=', 'oil_org.id')
            ->select(Capsule::connection($connection)->raw('oil_vice_assign.id,oil_vice_assign.vice_no,oil_vice_assign.assign_money,oil_vice_assign.card_remain,
            oil_vice_assign.trade_type,oil_vice_assign.trade_place,oil_vice_assign.trade_status,oil_vice_assign.assign_time,oil_vice_assign.createtime,
            oil_vice_assign.updatetime,oil_card_main.main_no,oil_card_vice.oil_com,oil_org.orgcode,oil_org.org_name'))
            ->orderBy('oil_vice_assign.createtime', 'desc');
        $obj    = new OilViceAssign();
        $sqlObj = $obj->scopeFilter($sqlObj, $params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }
        $data = self::formatData($data, $params);
        //$sql = Capsule::connection($connection)->getQueryLog();
        //print_r($sql);exit;
        return $data;
    }
    
    static public function formatData($data, $params)
    {
        foreach ($data as &$_item) {
            $_item->assign_type_txt = $_item->card_type_txt = "";
            if (isset($params['_export']) && $params['_export'] == 1) {
                $_item->vice_no = $_item->vice_no . "\t";
                $_item->main_no = $_item->main_no . "\t";
            }
            if (in_array($_item->oil_com, [OilCom::ZSH, OilCom::ZCW_ZSH_CYZYK])) {
                if ( in_array($_item->trade_type ,['额度预分配','预分配']) ) {
                    $_item->assign_type_txt = "分配";
                }
                if ( in_array($_item->trade_type,['反额度预分配','负分配']) ) {
                    $_item->assign_type_txt = "圈回";
                }
                $_item->card_type_txt = "中石化";
            }
            if ($_item->oil_com == OilCom::ZSY) {
                if ($_item->trade_type == '单位分配(进)') {
                    $_item->assign_type_txt = "分配";
                }
                if ($_item->trade_type == '单位汇总(出)') {
                    $_item->assign_type_txt = "圈回";
                }
                $_item->card_type_txt = "中石油";
            }
            
            //机构名称显示机构层级关系处理（临时取消）
            /*if(isset($params['curent_orgcode']) && $params['curent_orgcode']){
                $_item->org_name = \Models\OilOrg::getOrgTreePart($params['curent_orgcode'],$_item->orgcode);
            }*/
        }
        return $data;
    }
    
    /**
     * search data from oilAgent
     * @return mixed
     */
    static public function syncAssignData()
    {
        $info = Capsule::connection()->select("select max(api_id) max_api_id from oil_vice_assign");
        $data = dspClient::post(
            [
                'method' => 'huoyunren.gascard.syncAssignData',
                'data'   => [
                    'data'   => json_encode([
                        'limit'   => '1000',
                        'startId' => intval($info[0]->max_api_id),
                    ]),
                    'format' => 'json'
                ]
            ]
        );
        
        return $data;
    }
    
    static public function test()
    {
        $data = dspClient::post(
            [
                'method' => 'huoyunren.gascard.listAssignData',
                'data'   => [
                    'data'   => json_encode([
                        'parentCard' => '1000111100014502445',
                        'page'       => 1,
                        'limit'      => '100',
                        'beginTime'  => '2016-01-01',
                        'endTime'    => date('Y-m-d H:i:s'),
                    ]),
                    'format' => 'json'
                ]
            ]
        );
        
        var_dump($data);
    }
    
    static public function getOneMinuteData($data)
    {
        $sql = "SELECT
	id
FROM
	`oil_vice_assign`
WHERE
	vice_no = '" . $data->cardNo . "'
AND
    assign_money = " . $data->amount . "
AND
ABS(TIMESTAMPDIFF(SECOND,'" . date('Y-m-d H:i:s', $data->opeTime / 1000) . "',assign_time)) < 110
ORDER BY
	createtime DESC";
        
        $data = Capsule::connection()->select($sql);
        
        return $data;
    }

    static public function getTotal(array $params)
    {
        return OilViceAssign::Filter($params)->count();
    }
}