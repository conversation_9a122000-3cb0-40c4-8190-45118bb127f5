<?php
/**
 * 站点机构维度日交易数据
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/10/28
 * Time: 15:03:13
 */
namespace Models;

class OilOrgStationMonthTrade extends \Framework\Database\Model
{
    protected $table = 'oil_org_station_month_trade';

    protected $guarded = ["id"];
    protected $fillable = ['date','top_org_id','top_org_code','station_pcode','station_code','station_name','oil_base_id','zsy_money','zsy_num','zsy_count','zsh_money','zsh_num','zsh_count','cylmk_money','cylmk_num','cylmk_count','qiaopai_money','qiaopai_num','qiaopai_count','czk_money','czk_num','czk_count','gxk_money','gxk_num','gxk_count','fck_money','fck_num','fck_count','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By date
        if (isset($params['date']) && $params['date'] != '') {
            if (strpos($params['date'], ',') === false) {
                $query->where('date', '=', $params['date']);
            } else {
                list($s, $e) = explode(',', $params['date']);

                if ($s == $e) {
                    $query->where('date', '=', $e);
                } else {
                    if ($s) {
                        $query->where('date', '>=', $s);
                    }
                    if ($e) {
                        $query->where('date', '<=', $e);
                    }
                }
            }
        }

        //Search By top_org_id
        if (isset($params['top_org_id']) && $params['top_org_id'] != '') {
            $query->where('top_org_id', '=', $params['top_org_id']);
        }

        //Search By top_org_code
        if (isset($params['top_org_code']) && $params['top_org_code'] != '') {
            $query->where('top_org_code', '=', $params['top_org_code']);
        }

        //Search By station_pcode
        if (isset($params['station_pcode']) && $params['station_pcode'] != '') {
            $query->where('station_pcode', '=', $params['station_pcode']);
        }

        if (! empty($params['station_code_neq_empty'])) {
            $query->where('oil_org_station_month_trade.station_code', '!=', '');
        }

        if (! empty($params['station_pcode_neq_empty'])) {
            $query->where('oil_org_station_month_trade.station_pcode', '!=', '');
        }

        //Search By station_code
        if (isset($params['station_code']) && $params['station_code'] != '') {
            $query->where('oil_org_station_month_trade.station_code', '=', $params['station_code']);
        }

        if (! empty($params['station_code_neq_empty'])) {
            $query->where('oil_org_station_month_trade.station_code', '!=', '');
        }

        //Search By station_name
        if (isset($params['station_name']) && $params['station_name'] != '') {
            $query->where('station_name', '=', $params['station_name']);
        }

        //Search By oil_base_id
        if (isset($params['oil_base_id']) && $params['oil_base_id'] != '') {
            $query->where('oil_base_id', '=', $params['oil_base_id']);
        }

        //Search By zsy_money
        if (isset($params['zsy_money']) && $params['zsy_money'] != '') {
            $query->where('zsy_money', '=', $params['zsy_money']);
        }

        //Search By zsy_num
        if (isset($params['zsy_num']) && $params['zsy_num'] != '') {
            $query->where('zsy_num', '=', $params['zsy_num']);
        }

        //Search By zsy_count
        if (isset($params['zsy_count']) && $params['zsy_count'] != '') {
            $query->where('zsy_count', '=', $params['zsy_count']);
        }

        //Search By zsh_money
        if (isset($params['zsh_money']) && $params['zsh_money'] != '') {
            $query->where('zsh_money', '=', $params['zsh_money']);
        }

        //Search By zsh_num
        if (isset($params['zsh_num']) && $params['zsh_num'] != '') {
            $query->where('zsh_num', '=', $params['zsh_num']);
        }

        //Search By zsh_count
        if (isset($params['zsh_count']) && $params['zsh_count'] != '') {
            $query->where('zsh_count', '=', $params['zsh_count']);
        }

        //Search By cylmk_money
        if (isset($params['cylmk_money']) && $params['cylmk_money'] != '') {
            $query->where('cylmk_money', '=', $params['cylmk_money']);
        }

        //Search By cylmk_num
        if (isset($params['cylmk_num']) && $params['cylmk_num'] != '') {
            $query->where('cylmk_num', '=', $params['cylmk_num']);
        }

        //Search By cylmk_count
        if (isset($params['cylmk_count']) && $params['cylmk_count'] != '') {
            $query->where('cylmk_count', '=', $params['cylmk_count']);
        }

        //Search By qiaopai_money
        if (isset($params['qiaopai_money']) && $params['qiaopai_money'] != '') {
            $query->where('qiaopai_money', '=', $params['qiaopai_money']);
        }

        //Search By qiaopai_num
        if (isset($params['qiaopai_num']) && $params['qiaopai_num'] != '') {
            $query->where('qiaopai_num', '=', $params['qiaopai_num']);
        }

        //Search By qiaopai_count
        if (isset($params['qiaopai_count']) && $params['qiaopai_count'] != '') {
            $query->where('qiaopai_count', '=', $params['qiaopai_count']);
        }

        //Search By czk_money
        if (isset($params['czk_money']) && $params['czk_money'] != '') {
            $query->where('czk_money', '=', $params['czk_money']);
        }

        //Search By czk_num
        if (isset($params['czk_num']) && $params['czk_num'] != '') {
            $query->where('czk_num', '=', $params['czk_num']);
        }

        //Search By czk_count
        if (isset($params['czk_count']) && $params['czk_count'] != '') {
            $query->where('czk_count', '=', $params['czk_count']);
        }

        //Search By gxk_money
        if (isset($params['gxk_money']) && $params['gxk_money'] != '') {
            $query->where('gxk_money', '=', $params['gxk_money']);
        }

        //Search By gxk_num
        if (isset($params['gxk_num']) && $params['gxk_num'] != '') {
            $query->where('gxk_num', '=', $params['gxk_num']);
        }

        //Search By gxk_count
        if (isset($params['gxk_count']) && $params['gxk_count'] != '') {
            $query->where('gxk_count', '=', $params['gxk_count']);
        }

        //Search By fck_money
        if (isset($params['fck_money']) && $params['fck_money'] != '') {
            $query->where('fck_money', '=', $params['fck_money']);
        }

        //Search By fck_num
        if (isset($params['fck_num']) && $params['fck_num'] != '') {
            $query->where('fck_num', '=', $params['fck_num']);
        }

        //Search By fck_count
        if (isset($params['fck_count']) && $params['fck_count'] != '') {
            $query->where('fck_count', '=', $params['fck_count']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        if (! empty($params['province_code'])) {
            $query->where('province_code', '=', $params['province_code']);
        }

        //Search By first_trade_time
        if (isset($params['first_trade_time']) && $params['first_trade_time'] != '') {
            if (strpos($params['first_trade_time'], ',') === false) {
                $query->where('first_trade_time', '>=', $params['first_trade_time']);
            } else {
                list($s, $e) = explode(',', $params['first_trade_time']);

                if ($s == $e) {
                    $query->where('first_trade_time', '=', $e);
                } else {
                    if ($s) {
                        $query->where('first_trade_time', '>=', $s);
                    }
                    if ($e) {
                        $query->where('first_trade_time', '<=', $e);
                    }
                }
            }
        }

        //Search By latest_trade_time
        if (isset($params['latest_trade_time']) && $params['latest_trade_time'] != '') {
            if (strpos($params['latest_trade_time'], ',') === false) {
                $query->where('latest_trade_time', '>=', $params['latest_trade_time']);
            } else {
                list($s, $e) = explode(',', $params['latest_trade_time']);

                if ($s == $e) {
                    $query->where('latest_trade_time', '=', $e);
                } else {
                    if ($s) {
                        $query->where('latest_trade_time', '>=', $s);
                    }
                    if ($e) {
                        $query->where('latest_trade_time', '<=', $e);
                    }
                }
            }
        }

        if (isset($params['top_org_id_not_in']) && !empty($params['top_org_id_not_in'])) {
            $query->whereNotIn('top_org_id', $params['top_org_id_not_in']);
        }

        return $query;
    }

    /**
     * 站点机构维度日交易数据 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = self::Filter($params);

        if (!  empty($params['withOilStation'])) {
            $sqlObj->leftJoin('oil_station', 'oil_station.station_code', '=', 'oil_org_station_month_trade.station_code');
        }

        if (!  empty($params['withOilStationOperators'])) {
            $sqlObj->leftJoin('oil_station_operators', 'oil_station_operators.operators_code', '=', 'oil_org_station_month_trade.station_pcode');
        }

        if (! empty($params['groupBy'])) {
            foreach ($params['groupBy'] as $k) {
                $sqlObj->groupBy($k);
            }
        }

        if (! empty($params['orderBy'])) {
            foreach ($params['orderBy'] as $k => $flag) {
                $sqlObj->orderBy($k, $flag);
            }
        }

        $fields = empty($params['fields']) ? '*' : $params['fields'];
        $sqlObj->selectRaw($fields);

        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $skip = ($params['page'] - 1) * $params['limit'];
            $data = $sqlObj->skip($skip)->take($params['limit'])->get();
        }

        return $data;
    }

    /**
     * 站点机构维度日交易数据 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgStationDayTrade::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgStationDayTrade::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 站点机构维度日交易数据 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilOrgStationDayTrade::create($params);
    }

    /**
     * 站点机构维度日交易数据 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgStationDayTrade::find($params['id'])->update($params);
    }

    /**
     * 站点机构维度日交易数据 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilOrgStationDayTrade::destroy($params['ids']);
    }

    static public function removeByFilter(array $params)
    {
        if (empty($params))
            return true;

        return OilOrgStationDayTrade::Filter($params)->delete();
    }
}