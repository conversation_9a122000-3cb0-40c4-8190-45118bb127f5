<?php
/**
 * gsp_dsp_fetch
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:16
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class GspDspFetch extends \Framework\Database\Model
{
    protected $table = 'gsp_dsp_fetch';

    protected $guarded = ["id"];

    protected $fillable = ['status'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }


        return $query;
    }

    /**
     * gsp_dsp_fetch 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = GspDspFetch::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * gsp_dsp_fetch 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspDspFetch::find($params['id']);
    }

    /**
     * gsp_dsp_fetch 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return GspDspFetch::create($params);
    }

    /**
     * gsp_dsp_fetch 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspDspFetch::find('id')->update($params);
    }

    /**
     * gsp_dsp_fetch 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return GspDspFetch::destroy($params['ids']);
    }




}