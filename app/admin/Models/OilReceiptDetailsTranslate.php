<?php
/**
 * 销项票翻译明细
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/08/16
 * Time: 20:23:01
 */
namespace Models;
use Fuel\Defines\ReceiptDetailTranslate;
use Fuel\Service\OilTypeService;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilReceiptDetailsTranslate extends \Framework\Database\Model
{
    protected $table = 'oil_receipt_details_translate';

    protected $guarded = ["id"];
    protected $fillable = ['receipt_detail_id','receipt_no','receipt_code','classify_id','sku','num','unit','creator',
        'last_operator','createtime','updatetime','tax_rate','out_status','out_reason'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By receipt_detail_id
        if (isset($params['receipt_detail_id']) && $params['receipt_detail_id'] != '') {
            $query->where('receipt_detail_id', '=', $params['receipt_detail_id']);
        }

        //Search By receipt_no
        if (isset($params['receipt_no']) && $params['receipt_no'] != '') {
            $query->where('oil_receipt_details_translate.receipt_no', 'like', "%".$params['receipt_no']."%");
        }

        //Search By receipt_code
        if (isset($params['receipt_code']) && $params['receipt_code'] != '') {
            $query->where('oil_receipt_details_translate.receipt_code', 'like', "%".$params['receipt_code']."%");
        }

        //Search By classify_id
        if (isset($params['classify_id']) && $params['classify_id'] != '') {
            $query->where('classify_id', '=', $params['classify_id']);
        }

        if (isset($params['classify_idIn']) && count($params['classify_idIn']) > 0) {
            $query->whereIn('classify_id', $params['classify_idIn']);
        }

        //Search By sku
        if (isset($params['skuLk']) && $params['skuLk'] != '') {
            $query->where('oil_receipt_details_translate.sku', 'like', "%".$params['skuLk']."%");
        }

        //Search By num
        if (isset($params['num']) && $params['num'] != '') {
            $query->where('num', '=', $params['num']);
        }

        //Search By unit
        if (isset($params['unit']) && $params['unit'] != '') {
            $query->where('unit', '=', $params['unit']);
        }

        //Search By creator
        if (isset($params['creator']) && $params['creator'] != '') {
            $query->where('creator', '=', $params['creator']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('createtime', '>=', $params['createtimeGe']);
        }

        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $query->where('createtime', '<=', $params['createtimeLe']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        if (isset($params['updatetimeGe']) && $params['updatetimeGe'] != '') {
            $query->where('updatetime', '>=', $params['updatetimeGe']);
        }

        if (isset($params['updatetimeLe']) && $params['updatetimeLe'] != '') {
            $query->where('updatetime', '<=', $params['updatetimeLe']);
        }

        //Search By unit
        if (isset($params['out_status']) && $params['out_status'] != '') {
            $query->where('out_status', '=', $params['out_status']);
        }

        return $query;
    }

    /**
     * 销项票翻译明细 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilReceiptDetailsTranslate::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 销项票翻译明细 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptDetailsTranslate::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptDetailsTranslate::lockForUpdate()->where('id',$params['id'])->first();
    }

    static public function getByFilter(array $params)
    {
        return OilReceiptDetailsTranslate::Filter($params)->first();
    }

    /**
     * 销项票翻译明细 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilReceiptDetailsTranslate::create($params);
    }

    /**
     * 销项票翻译明细 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptDetailsTranslate::find($params['id'])->update($params);
    }

    /**
     * 销项票翻译明细 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilReceiptDetailsTranslate::destroy($params['ids']);
    }


    static public function listWithDetails(array $params)
    {
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        if(isset($params['classify_nameLk']) && $params['classify_nameLk'] != ""){
            $classify = OilTypeCategory::pluckByNameLikeFilter(['nameLk'=>$params['classify_nameLk']]);
            if(count($classify) > 0){
                $params['classify_idIn'] = $classify;
            }
        }

        $field = "oil_receipt_details_translate.*,oil_receipt_details.goods_name,oil_receipt_details.unit as orgin_unit,
        oil_receipt_details.num as orgin_num,oil_receipt_details.tax_rate as orgin_tax_rate,oil_receipt_details.sku as orgin_sku,
        oil_receipt_manage.color_type";
        $sqlObj = OilReceiptDetailsTranslate::Filter($params)
            ->selectRaw($field)
            ->leftJoin('oil_receipt_details', 'oil_receipt_details.id', '=', 'oil_receipt_details_translate.receipt_detail_id')
            ->leftJoin('oil_receipt_manage', 'oil_receipt_manage.id', '=', 'oil_receipt_details.receipt_manage_id');
        $sqlObj->where("oil_receipt_details.is_del",'=',0);
        if(isset($params['skip']) && isset($params['take'])) {
            $data = $sqlObj->orderBy('oil_receipt_details_translate.createtime', 'desc')
                ->skip(intval($params['skip']))
                ->take(intval($params['take']))
                ->get();
        }else if(isset($params['count']) && $params['count'] == 1) {
            $data = $sqlObj->count();
        }else {
            $data = $sqlObj->orderBy('oil_receipt_details_translate.createtime', 'desc')
                ->paginate($params['limit'],['*'],'page',$params['page']);
        }


        $unitArr = (new OilTypeService())->getOilUnit();
        $classifyArr = OilTypeCategory::getTypeName();
        foreach ($data as &$_item){
            $_tmp = isset($classifyArr[$_item->classify_id]) ? $classifyArr[$_item->classify_id] : [];
            $_item->color_type_txt = $_item->color_type == 10 ? "红票" : "蓝票";
            $_item->orgin_tax_rateTxt = ($_item->orgin_tax_rate * 100)."%";
            $_item->num = number_format($_item->num,6,".","");
            $_item->unit_txt = isset($unitArr[$_item->unit]) ? $unitArr[$_item->unit] : "-";
            $_item->tax_rate = $_item->tax_rate."%";
            $_item->classify_txt = isset($_tmp['name']) ? $_tmp['name'] : "";
            $_item->out_status_txt = ReceiptDetailTranslate::getOutStatusList($_item->out_status);
        }
        return $data;
    }

}