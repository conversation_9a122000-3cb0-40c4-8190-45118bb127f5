<?php
/**
 * 卡账户
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2017/12/12
 * Time: 18:32:04
 */

namespace Models;

use Framework\Cache;
use Framework\Helper;
use Fuel\Defines\OilCom;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCardAccount extends \Framework\Database\Model
{
    //use SoftDeletes;

    //const DELETED_AT = 'deletetime';

    protected $table = 'oil_card_account';

    protected $guarded = ["id"];
    protected $fillable = ['id', 'subAccountType', 'subAccountName', 'vice_id', 'cardID','cardSubAccountID','common_account_no', 'account_no', 'amount',
        'status', 'createtime', 'updatetime', 'deletetime'];

    //disable incrementing id;
    public $incrementing = FALSE;

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        if (isset($params['cardID']) && $params['cardID']) {
            $query->where('cardID', '=', $params['cardID']);
        }

        if (isset($params['cardIDList']) && $params['cardIDList']) {
            $query->whereIn('cardID', $params['cardIDList']);
        }

        //Search By subAccountType
        if (isset($params['subAccountType']) && $params['subAccountType'] != '') {
            $query->where('subAccountType', '=', $params['subAccountType']);
        }

        //Search By subAccountName
        if (isset($params['subAccountName']) && $params['subAccountName'] != '') {
            $query->where('subAccountName', '=', $params['subAccountName']);
        }

        //Search By vice_id
        if (isset($params['vice_id']) && $params['vice_id'] != '') {
            $query->where('vice_id', '=', $params['vice_id']);
        }

        //Search By cardSubAccountID
        if (isset($params['cardSubAccountID']) && $params['cardSubAccountID'] != '') {
            $query->where('cardSubAccountID', '=', $params['cardSubAccountID']);
        }

        //Search By common_account_no
        if (isset($params['common_account_no']) && $params['common_account_no'] != '') {
            $query->where('common_account_no', '=', $params['common_account_no']);
        }

        //Search By amount
        if (isset($params['amount']) && $params['amount'] != '') {
            $query->where('amount', '=', $params['amount']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By deletetime
        if (isset($params['deletetime']) && $params['deletetime'] != '') {
            $query->where('deletetime', '=', $params['deletetime']);
        }

        //Search By is_del
        if (isset($params['is_del']) && $params['is_del'] != '') {
            $query->where('oil_org.is_del', '=', $params['is_del']);
        }

        //Search By oil_comIn
        if (isset($params['oil_comIn']) && $params['oil_comIn']) {
            $query->whereIn('oil_card_vice.oil_com', $params['oil_comIn']);
        }

        //Search By cardFromIn
        if (isset($params['cardFromIn']) && $params['cardFromIn']) {
            $query->whereIn('oil_card_vice.card_from', $params['cardFromIn']);
        }

        //Search By statusIn
        if (isset($params['statusIn']) && $params['statusIn']) {
            $query->whereIn('oil_card_vice.status', $params['statusIn']);
        }

        return $query;
    }

    /**
     * 卡账户 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCardAccount::Filter($params);

        if (isset($params['count']) && $params['count'] == 1) {
            return $sqlObj->count();
        }elseif(isset($params['skip']) && isset($params['take'])){
            $data = $sqlObj->skip(intval($params['skip']))->take(intval($params['take']))->get();
        }elseif (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * 卡账户 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardAccount::find($params['id']);
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardAccount::lockForUpdate()->where('id',$params['id'])->first();
    }


    /**
     * 插入时检查是否存在
     * @param array $params
     * @return object
     */
    static public function getByUniqueLock(array $params)
    {
        return OilCardAccount::lockForUpdate()->Filter($params)->first();
    }

    /**
     * 获取卡账户不全的Vice_id
     * @param array $params
     * @return object
     */
    static public function getNoAccountViceId()
    {
        //return OilCardAccount::select(Capsule::Raw("count(id) as num,vice_id"))->where("cardID",'not like',"%_1")->groupBy("cardID")->having("num",'=',1)->get();
        return Capsule::connection('online_only_read')->table('oil_card_account')
            ->select(Capsule::Raw("count(id) as num,vice_id"))
            ->where('createtime','>=',date("Y-m-d H:i:s",time()-60*30))
            ->groupBy("cardID")
            ->having("num",'<',2)
            ->get();
    }

    /**
     * 卡账户 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        $params['id'] = Helper::uuid();
        $params['createtime'] = date("Y-m-d H:i:s");

        return OilCardAccount::create($params);
    }

    /**
     * 卡账户 批量新增
     * @param array $params
     * @return mixed
     */
    static public function batchAdd(array $params)
    {
        return OilCardAccount::insert($params);
    }

    /**
     * 卡账户 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardAccount::find($params['id'])->update($params);
    }

    /**
     * 卡账户 根据id批量编辑
     * @param array $params
     * @return mixed
     */
    static public function editByIds(array $params,$upData)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilCardAccount::whereIn("id",$params['ids'])->update($upData);
    }

    /**
     * 卡账户 根据id批量编辑
     * @param array $params
     * @return mixed
     */
    static public function editByCondition(array $params,$upData)
    {
        return OilCardAccount::whereIn("vice_id",$params['ids'])->where('subAccountType',$params['accountType'])->whereNUll('common_account_no')->update($upData);
    }

    /**
     * 卡账户 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilCardAccount::destroy($params['ids']);
    }

    /**
     * 卡账户 根据卡id获取卡账户
     * @param array $params
     * @return int
     */
    static public function getCardAccount(array $params)
    {
        \helper::argumentCheck(['vice_id'], $params);
        //Capsule::connection()->enableQueryLog();
        return OilCardAccount::where("vice_id",$params['vice_id'])->where("subAccountType","CREDIT")->first();
        //print_r(Capsule::connection()->getQueryLog());
    }

    static public function getCardAccountByAccountNO(array $params)
    {
        \helper::argumentCheck(['vice_id','common_account_no'], $params);
        //Capsule::connection()->enableQueryLog();
        return OilCardAccount::lockForUpdate()->where("vice_id",$params['vice_id'])
            ->where('common_account_no',$params['common_account_no'])
            ->where("subAccountType","CREDIT")->first();
        //print_r(Capsule::connection()->getQueryLog());
    }

    //获取信用账户的卡数量
    static public function getNumByAccountNO(array $params,$viceIds = [])
    {
        \helper::argumentCheck(['common_account_no'], $params);
        return OilCardAccount::where('common_account_no',$params['common_account_no'])
            ->where("subAccountType","CREDIT")
            ->whereIn("vice_id",$viceIds)
            ->count("id");
    }

    /**
     * @title   根据cardIDs删除卡账户
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Models
     * @since
     * @params  type filedName required?
     * @param array $cardIds
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function removeByCardIds(array $cardIds)
    {
        return self::whereIn('cardId',$cardIds)->delete();
    }

    /**
     * 获取某个机构卡账户对应的机构账户为空的数据
     */
    static public function getCardListByOrg(array $condition)
    {
        //Capsule::connection()->enableQueryLog();
        $obj = self::whereIn("oil_card_vice.oil_com", [OilCom::GAS_FIRST_CHARGE,OilCom::GAS_FIRST_ZBANK_CHARGE])
            ->leftJoin('oil_card_vice', 'oil_card_vice.id', '=', 'oil_card_account.vice_id')
            ->select("oil_card_account.id","oil_card_account.common_account_no",
                "oil_card_account.cardSubAccountID","oil_card_account.amount","oil_card_vice.oil_com","oil_card_vice.vice_no",
                "oil_card_account.subAccountType","oil_card_account.vice_id")
            ->orderBy("oil_card_vice.createtime", "desc");


        if(isset($condition['isCommonNull'])){
            $obj->whereNull("oil_card_account.common_account_no");
            unset($condition['isCommonNull']);
        }

        if(isset($condition['createtimeGt'])){
            $obj->where("oil_card_vice.createtime",">=",$condition['createtimeGt']);
            unset($condition['createtimeGt']);
        }

        if(isset($condition['accountTypeIn'])){
            $obj->whereIn("oil_card_account.subAccountType",$condition['accountTypeIn']);
            unset($condition['accountTypeIn']);
        }

        if(isset($condition['cardViceIn'])){
            $obj->whereIn("oil_card_vice.vice_no",$condition['cardViceIn']);
            unset($condition['cardViceIn']);
        }

        $obj->where($condition);

        $data = $obj->get();
        //$sql1 = Capsule::connection()->getQueryLog();
        //print_r($sql1);
        return $data;
    }

    /**
     * @title 根据机构id获取支付平台id map
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param array $viceIds
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getCardAccountMap(array $viceIds)
    {
        $group_arr = [];
        $data = self::whereIn('vice_id',$viceIds)->get();

        if($data){
            $group_arr = [];
            foreach ($data as $v)
            {
                $group_arr[$v->subAccountType][$v->vice_id] = $v->cardSubAccountID;
            }
        }

        return $group_arr;
    }

    /**
     * @title   获取卡余额
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Models
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getCardBalance(array $params = [])
    {
        return self::select('oil_card_vice.id as vice_id', 'oil_card_vice.reserve_remain', 'oil_card_vice.comp_remain', 'oil_card_vice.card_remain',
            'oil_card_vice.point_reserve_total', 'oil_card_vice.point_remain','oil_card_vice.org_id',
            'oil_card_account.subAccountType', 'oil_card_account.cardSubAccountID')
            ->leftJoin('oil_card_vice', 'oil_card_vice.id', '=', 'oil_card_account.vice_id')
            ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice.org_id')
            ->Filter($params)
            ->get();
    }

    /**
     * 根据指定vice_id和账户类型查询卡账户
     * @param $vice_id
     * @param $account_type
     * @return mixed
     */
    public static function getByViceId($vice_id,$account_type)
    {
        $cacheName = md5(__METHOD__.$vice_id.$account_type);
        $data = Cache::get($cacheName);
        if(!$data){
            $data = self::where('vice_id', $vice_id)->where('subAccountType',$account_type)->first();
            Cache::put($cacheName,$data,86400);
        }

        return $data;
    }
    
    /**
     * 取字段值 返回第一个值
     * @param array $params
     * @param $pluckField
     * @return mixed
     */
    public static function getResField(array $params, $pluckField)
    {
        $res = self::getPluckFields($params, $pluckField);
        
        return !empty($res[0]) ? $res[0] : '';
    }
    
    /**
     * 取字段值
     * @param array $params
     * @param $pluckField
     * @return array
     */
    static public function getPluckFields(array $params, $pluckField)
    {
        $res = OilCardAccount::Filter($params)->pluck($pluckField);
        
        return !$res ? [] : $res->toArray();
    }
    
    /**
     * 更新账户余额
     * @param $id
     * @param $money
     * @param string $op
     * @return int
     */
    public static function updateAmountById($id, $money, $op = '+')
    {
        $sql = " UPDATE `oil_card_account` SET amount=amount $op $money  WHERE id = $id ";
        
        if ($op == '-') {
            $sql .= "AND amount >= $money";
        }
        
        return Capsule::connection()->getPdo()->exec($sql);
    }
    
    /**
     * 根据条件取信息
     * @param array $params
     * @param mixed $field
     * @return object
     */
    static public function getInfoByFilter(array $params, $field = "*")
    {
        $resObj =  self::Filter($params)->select($field)->first();
        
        return !$resObj ? [] : $resObj->toArray();
    }
}