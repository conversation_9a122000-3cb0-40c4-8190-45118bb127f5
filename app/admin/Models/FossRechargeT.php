<?php
/**
 * 油品充值金额表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/05/20
 * Time: 17:21:04
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class FossRechargeT extends \Framework\Database\Model
{
    protected $table = 'foss_recharge_t';

    protected $connection = 'finance';
    
    protected $fillable = ['REC_DATE','CARD_TYPE','PRODUCT_CODE','PRODUCT_NAME','FUNDING_PARTY','CUSTOMER_CODE','CUSTOMER_NAME','REC_AMOUNT','ENTITY_CODE','ENTITY_NAME','ORDER_LABEL','BACKUP1','BACKUP2','BACKUP3','BACKUP4','BACKUP5','UPDATE_DATE'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By REC_DATE
        if (isset($params['REC_DATE']) && $params['REC_DATE'] != '') {
            $query->where('REC_DATE', '=', $params['REC_DATE']);
        }

        //Search By CARD_TYPE
        if (isset($params['CARD_TYPE']) && $params['CARD_TYPE'] != '') {
            $query->where('CARD_TYPE', '=', $params['CARD_TYPE']);
        }

        //Search By PRODUCT_CODE
        if (isset($params['PRODUCT_CODE']) && $params['PRODUCT_CODE'] != '') {
            $query->where('PRODUCT_CODE', '=', $params['PRODUCT_CODE']);
        }

        //Search By PRODUCT_NAME
        if (isset($params['PRODUCT_NAME']) && $params['PRODUCT_NAME'] != '') {
            $query->where('PRODUCT_NAME', '=', $params['PRODUCT_NAME']);
        }

        //Search By FUNDING_PARTY
        if (isset($params['FUNDING_PARTY']) && $params['FUNDING_PARTY'] != '') {
            $query->where('FUNDING_PARTY', '=', $params['FUNDING_PARTY']);
        }

        //Search By CUSTOMER_CODE
        if (isset($params['CUSTOMER_CODE']) && $params['CUSTOMER_CODE'] != '') {
            $query->where('CUSTOMER_CODE', '=', $params['CUSTOMER_CODE']);
        }

        //Search By CUSTOMER_NAME
        if (isset($params['CUSTOMER_NAME']) && $params['CUSTOMER_NAME'] != '') {
            $query->where('CUSTOMER_NAME', '=', $params['CUSTOMER_NAME']);
        }

        //Search By REC_AMOUNT
        if (isset($params['REC_AMOUNT']) && $params['REC_AMOUNT'] != '') {
            $query->where('REC_AMOUNT', '=', $params['REC_AMOUNT']);
        }

        //Search By ENTITY_CODE
        if (isset($params['ENTITY_CODE']) && $params['ENTITY_CODE'] != '') {
            $query->where('ENTITY_CODE', '=', $params['ENTITY_CODE']);
        }

        //Search By ENTITY_NAME
        if (isset($params['ENTITY_NAME']) && $params['ENTITY_NAME'] != '') {
            $query->where('ENTITY_NAME', '=', $params['ENTITY_NAME']);
        }

        //Search By ORDER_LABEL
        if (isset($params['ORDER_LABEL']) && $params['ORDER_LABEL'] != '') {
            $query->where('ORDER_LABEL', '=', $params['ORDER_LABEL']);
        }

        //Search By BACKUP1
        if (isset($params['BACKUP1']) && $params['BACKUP1'] != '') {
            $query->where('BACKUP1', '=', $params['BACKUP1']);
        }

        //Search By BACKUP2
        if (isset($params['BACKUP2']) && $params['BACKUP2'] != '') {
            $query->where('BACKUP2', '=', $params['BACKUP2']);
        }

        //Search By BACKUP3
        if (isset($params['BACKUP3']) && $params['BACKUP3'] != '') {
            $query->where('BACKUP3', '=', $params['BACKUP3']);
        }

        //Search By BACKUP4
        if (isset($params['BACKUP4']) && $params['BACKUP4'] != '') {
            $query->where('BACKUP4', '=', $params['BACKUP4']);
        }

        //Search By BACKUP5
        if (isset($params['BACKUP5']) && $params['BACKUP5'] != '') {
            $query->where('BACKUP5', '=', $params['BACKUP5']);
        }

        //Search By UPDATE_DATE
        if (isset($params['UPDATE_DATE']) && $params['UPDATE_DATE'] != '') {
            $query->where('UPDATE_DATE', '=', $params['UPDATE_DATE']);
        }

        return $query;
    }

    /**
     * 油品充值金额表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = FossRechargeT::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 油品充值金额表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return FossRechargeT::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return FossRechargeT::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 油品充值金额表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return FossRechargeT::create($params);
    }

    /**
     * 油品充值金额表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return FossRechargeT::find($params['id'])->update($params);
    }

    /**
     * 油品充值金额表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return FossRechargeT::destroy($params['ids']);
    }




}