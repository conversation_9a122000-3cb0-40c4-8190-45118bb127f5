<?php
/**
 * oil_card_vice_app_area
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/09/09
 * Time: 16:01:16
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCardViceAppArea extends \Framework\Database\Model
{
    protected $table = 'oil_card_vice_app_area';

    protected $guarded = ["id"];
    protected $fillable = ['oil_com_name','oil_com','province_id','province_code','province_name','remark','creator_name',
        'last_operator','createtime','updatetime','is_bind_truck'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By oil_com_name
        if (isset($params['oil_com_name']) && $params['oil_com_name'] != '') {
            $query->where('oil_com_name', '=', $params['oil_com_name']);
        }

        //Search By oil_com
        if (isset($params['oil_com']) && $params['oil_com'] != '') {
            $query->where('oil_com', '=', $params['oil_com']);
        }

        //Search By province_id
        if (isset($params['province_id']) && $params['province_id'] != '') {
            $query->where('province_id', '=', $params['province_id']);
        }

        //Search By province_name
        if (isset($params['province_name']) && $params['province_name'] != '') {
            $query->where('province_name', '=', $params['province_name']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By creator_name
        if (isset($params['creator_name']) && $params['creator_name'] != '') {
            $query->where('creator_name', '=', $params['creator_name']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * oil_card_vice_app_area 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCardViceAppArea::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        if($data) {
            foreach ($data as $key => &$val){
                $val->_is_bind_truck = $val->is_bind_truck == 1 ? '否' : '是';
            }
        }

        return $data;
    }

    /**
     * 根据oilcom获取开卡地区
     * @param array $params
     * @return mixed
     */
    static public function getByOilCom(array $params)
    {
        $sqlObj = OilCardViceAppArea::Filter($params);
        $data = $sqlObj->selectRaw('province_id as id,province_name as province,province_code as code,remark,is_bind_truck')->get();

        return $data;
    }

    /*
     * 获取维护的油品类型
     */
    static public function getUseOilCom(array $params)
    {
        return OilCardViceAppArea::select('oil_com_name','oil_com')->groupBy('oil_com')->get();
    }

    /**
     * 根据oil_com,province_code获取开卡地区
     * @param array $params
     * @return mixed
     */
    static public function getByOilComAndProvinceCode(array $params)
    {
        \helper::argumentCheck(['oil_com','province_code'],$params);

        return OilCardViceAppArea::where('oil_com',$params['oil_com'])
            ->where('province_code',$params['province_code'])
            ->first();
    }

    /**
     * oil_card_vice_app_area 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardViceAppArea::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardViceAppArea::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_card_vice_app_area 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCardViceAppArea::create($params);
    }

    /**
     * oil_card_vice_app_area 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardViceAppArea::find($params['id'])->update($params);
    }

    /**
     * oil_card_vice_app_area 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCardViceAppArea::destroy($params['ids']);
    }




}