<?php
/**
 * 机构每日中游成本汇总数据
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2023/05/24
 * Time: 15:09:01
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilOrgOperatorDayTrades extends \Framework\Database\Model
{
    protected $table = 'oil_org_operator_day_trades';

    protected $guarded = ["id"];
    protected $fillable = ['trade_create_day','up_operator_id','down_operator_id','oil_base_id','org_root','month',
        'start_time','end_time','trade_money','trade_num','discount_money','is_open_invoice','flag','createtime','updatetime'
    ];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By trade_create_day
        if (isset($params['trade_create_day']) && $params['trade_create_day'] != '') {
            $query->where('trade_create_day', '=', $params['trade_create_day']);
        }

        //Search By up_operator_id
        if (isset($params['up_operator_id']) && $params['up_operator_id'] != '') {
            $query->where('up_operator_id', '=', $params['up_operator_id']);
        }

        //Search By down_operator_id
        if (isset($params['down_operator_id']) && $params['down_operator_id'] != '') {
            $query->where('down_operator_id', '=', $params['down_operator_id']);
        }

        //Search By oil_base_id
        if (isset($params['oil_base_id']) && $params['oil_base_id'] != '') {
            $query->where('oil_base_id', '=', $params['oil_base_id']);
        }

        if (isset($params['oil_base_id_in']) && $params['oil_base_id_in'] != '') {
            $query->whereIn('oil_base_id', $params['oil_base_id_in']);
        }

        //Search By org_root
        if (isset($params['org_root']) && $params['org_root'] != '') {
            $query->where('org_root', '=', $params['org_root']);
        }

        if( isset($params['month_root']) && count($params['month_root']) > 0 ){
            $query->where(function($query) use ($params){
                $query->whereIn('org_root', $params['month_root']);
                $query->where('end_time','<',date('Y-m-d H:i:s',time()));
            });
        }

        //Search By month
        if (isset($params['month']) && $params['month'] != '') {
            $query->where('month', '=', $params['month']);
        }

        if (isset($params['monthIn']) && $params['monthIn'] != '') {
            $query->whereIn('month', $params['monthIn']);
        }

        //Search By start_time
        if (isset($params['start_time']) && $params['start_time'] != '') {
            $query->where('start_time', '=', $params['start_time']);
        }

        //Search By end_time
        if (isset($params['end_time']) && $params['end_time'] != '') {
            $query->where('end_time', '=', $params['end_time']);
        }

        //Search By trade_money
        if (isset($params['trade_money']) && $params['trade_money'] != '') {
            $query->where('trade_money', '=', $params['trade_money']);
        }

        //Search By trade_num
        if (isset($params['trade_num']) && $params['trade_num'] != '') {
            $query->where('trade_num', '=', $params['trade_num']);
        }

        //Search By discount_money
        if (isset($params['discount_money']) && $params['discount_money'] != '') {
            $query->where('discount_money', '=', $params['discount_money']);
        }

        //Search By is_open_invoice
        if (isset($params['is_open_invoice']) && $params['is_open_invoice'] != '') {
            $query->where('is_open_invoice', '=', $params['is_open_invoice']);
        }

        //Search By flag
        if (isset($params['flag']) && $params['flag'] != '') {
            $query->where('flag', '=', $params['flag']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 机构每日中游成本汇总数据 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilOrgOperatorDayTrades::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 机构每日中游成本汇总数据 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgOperatorDayTrades::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgOperatorDayTrades::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 机构每日中游成本汇总数据 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilOrgOperatorDayTrades::create($params);
    }

    /**
     * 机构每日中游成本汇总数据 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgOperatorDayTrades::find($params['id'])->update($params);
    }

    /**
     * 机构每日中游成本汇总数据 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilOrgOperatorDayTrades::destroy($params['ids']);
    }


    static public function deleteByFilter(array $params)
    {

        return OilOrgOperatorDayTrades::Filter($params)->delete();
    }

    static public function editByFilter($condition,array $params)
    {
        return OilOrgOperatorDayTrades::Filter($condition)->update($params);
    }

    static public function fieldPluck($params,$field = "end_time")
    {
        return OilOrgOperatorDayTrades::Filter($params)->pluck($field);
    }

    static public function getOrgRootMap($params = [])
    {
        $data = OilOrgOperatorDayTrades::Filter($params)->get();
        $result = [];
        if(count($data) > 0 ){
            foreach ($data as $_val){
                $is_end = 2;
                if(empty($_val->end_time)){
                    $_end = date("Y-m-d",time())." 00:00:00";
                }else{
                    $is_end = 1;
                    $_end = $_val->end_time;
                }
                $result[$_val->org_root] = ['start'=>$_val->start_time,"end"=>$_end,"is_end"=>$is_end];
            }
            return $result;
        }
        return $result;
    }

    static public function getOrgMap($params = [])
    {
        $data = OilOrgOperatorDayTrades::Filter($params)->get();
        $result = [];
        if(count($data) > 0 ){
            foreach ($data as $_val){
                $_key = $_val->org_root.'#'.$_val->start_time;
                $result[$_key] = $_val->end_time;
            }
            return $result;
        }
        return $result;
    }

    static public function rootReceiptQuota($params = [])
    {
        $sql = "SELECT
	up_operator_id,
	down_operator_id,
	oil_base_id,
	sum( trade_num ) AS trade_num,
	sum( trade_money ) AS trade_money,
	sum( discount_money ) as discount_money,
	
	sum( open_trade_num ) AS open_trade_num,
	sum( open_trade_money ) AS open_trade_money,
	sum( open_discount_money ) as open_discount_money,
	end_time,
	`month`,
	is_open_invoice
FROM
	(
	SELECT
		up_operator_id,
		down_operator_id,
		end_time,
		`month`,
		GROUP_CONCAT( distinct is_open_invoice) as is_open_invoice,
	IF
		( oil_base_id IN ( 9, 10, 20 ), 21, oil_base_id ) oil_base_id,
		sum( trade_num ) AS trade_num,
		sum( trade_money ) AS trade_money,
		sum( discount_money ) as discount_money,
		
		sum(if(is_open_invoice != 1,trade_num,0) ) AS open_trade_num,
		sum(if(is_open_invoice != 1,trade_money,0) ) AS open_trade_money,
		sum(if(is_open_invoice != 1,discount_money,0) ) as open_discount_money
		
	FROM
		`oil_org_operator_day_trades` 
	WHERE
	    1 and oil_base_id > 0 and flag = 2 AND (trade_num != 0 OR trade_money != 0) "; //G7WALLET-6388

        if (isset($params['trade_create_Ge']) && $params['trade_create_Ge']) {
            $sql .= " and trade_create_day >= '" . substr($params['trade_create_Ge'], 0, 10)."'";
        }

        if (isset($params['trade_create_Lt']) && $params['trade_create_Lt']) {
            $sql .= " and trade_create_day < '" . substr($params['trade_create_Lt'], 0, 10) . "'";
        }

        //Search By up_operator_id
        if (isset($params['up_operator_id']) && $params['up_operator_id'] != '') {
            $sql .= " and up_operator_id = " . $params['up_operator_id'];
        }

        //Search By down_operator_id
        if (isset($params['down_operator_id']) && $params['down_operator_id'] != '') {
            $sql .= " and down_operator_id = " . $params['down_operator_id'];
        }

        if (isset($params['oil_base_idIn']) && $params['oil_base_idIn'] != '') {
            $sql .= " and oil_base_id in (" . implode(",", $params['oil_base_idIn']) . ")";
        }
        $sql .= "	GROUP BY
		`up_operator_id`,
		`down_operator_id`,
		`oil_base_id`,
		`end_time`,
		`month`
	) a 
GROUP BY
	`up_operator_id`,
	`down_operator_id`,
	`oil_base_id`,
	`end_time`,`month`";
        $data = Capsule::connection('slave')->select($sql);
        //$sql = Capsule::connection()->getQueryLog();
        if(count($data) > 0){
            $totalMap = $lockMap = $unlockMap =[];
            foreach ($data as $_item){
                $_keyStr = $_item->up_operator_id.'#'.$_item->down_operator_id.'#'.$_item->oil_base_id;

                if( $_item->is_open_invoice == 1 ) {
                    $totalMap[$_keyStr]['money'] += $_item->trade_money;
                    $totalMap[$_keyStr]['discount'] += $_item->discount_money;
                    $totalMap[$_keyStr]['trade_num'] += $_item->trade_num;
                }

                if(empty($_item->end_time)){
                    $lockMap[$_keyStr]['use_money'] += ($_item->trade_money - $_item->discount_money);
                    $lockMap[$_keyStr]['trade_num'] += $_item->trade_num;
                    $lockMap[$_keyStr]['month'][] = $_item->month;
                }else{
                    if( $_item->is_open_invoice == 10){
                        $unlockMap[$_keyStr]['use_money'] += 0;
                        $unlockMap[$_keyStr]['trade_num'] += 0;
                        $unlockMap[$_keyStr]['month'] = [];
                    }else{
                        $unlockMap[$_keyStr]['use_money'] += ($_item->trade_money - $_item->discount_money);
                        $unlockMap[$_keyStr]['trade_num'] += $_item->trade_num;
                        $unlockMap[$_keyStr]['month'][] = $_item->month;
                    }
                }
            }

            return ['lock'=>$lockMap,'unlock'=>$unlockMap,'total'=>$totalMap];
        }
        return [];
    }

    static public function getDataByGroup($params = [])
    {
        $sql = "SELECT
                `month`,
                up_operator_id,
                down_operator_id,
                oil_base_id,
                sum( operator_money ) AS operator_money,
                sum( operator_num ) AS operator_num,
                is_lock,
                org_root
            FROM
                (
                SELECT
                    `month`,
                    up_operator_id,
                    down_operator_id,
                IF
                    ( oil_base_id IN ( 9, 10, 20 ), 21, oil_base_id ) oil_base_id,
                    sum( trade_money - discount_money ) AS operator_money,
                    sum( trade_num ) AS operator_num,
                    IF(end_time IS NOT NULL AND end_time != '' AND NOW() > end_time,1,2) AS is_lock,
                    group_concat(DISTINCT IF(end_time IS NOT NULL AND end_time != '' and NOW() > end_time ,org_root,'')) AS org_root
                FROM
                    oil_org_operator_day_trades 
                WHERE
                    flag = 2 AND is_open_invoice = 1";
        if(isset($params['operator_id']) && $params['operator_id']){
            $sql .= " and up_operator_id = ".$params['operator_id'];
        }
        if(isset($params['seller_id']) && $params['seller_id']){
            $sql .= " and down_operator_id = ".$params['seller_id'];
        }
        if(isset($params['oil_type']) && $params['oil_type']){
            if($params['oil_type'] == 21){
                $sql .= " and oil_base_id in (9,10,20) ";
            }else {
                $sql .= " and oil_base_id = " . $params['oil_type'];
            }
        }
        $sql .= " GROUP BY
                    `month`,
                    up_operator_id,
                    down_operator_id,
                    oil_base_id,
                    is_lock
                ) AS a GROUP BY
                `month`,
                up_operator_id,
                down_operator_id,
                oil_base_id,is_lock;";
        return Capsule::connection('slave')->select($sql);
    }

    static public function getLockMonth($is_month = 2)
    {
        $sql = "SELECT
	`month`,
	up_operator_id,
	down_operator_id,
	oil_base_id,
	org_root,
	start_time,
	end_time 
FROM
	(
	SELECT
		`month`,
		up_operator_id,
		down_operator_id,
	IF
		( oil_base_id IN ( 9, 10, 20 ), 21, oil_base_id ) oil_base_id,
		org_root,
		start_time,
		end_time 
	FROM
		oil_org_operator_day_trades 
	WHERE
		flag = 2  ";
        if ($is_month == 1){
            $sql .= " and is_open_invoice = 1 ";
        }
        $sql .= " GROUP BY
		`month`,
		up_operator_id,
		down_operator_id,
		oil_base_id,
		org_root 
	) AS a 
GROUP BY
	`month`,
	up_operator_id,
	down_operator_id,
	oil_base_id,
	org_root;";
        $data = Capsule::connection("slave")->select($sql);
        $result = [];
        if(count($data) > 0){
            foreach ($data as $_item){
                $_key = $_item->month.'#'.$_item->up_operator_id.'#'.$_item->down_operator_id."#".$_item->oil_base_id;
                $is_end = 2;
                if(empty($_item->end_time)){
                    $_end = date("Y-m-d",time())." 00:00:00";
                }else{
                    $is_end = 1;
                    $_end = $_item->end_time;
                }
                $result[$_item->org_root][$_key] = ['start'=>$_item->start_time,"end"=>$_end,"is_end"=>$is_end];
            }
        }
        return $result;
    }
}