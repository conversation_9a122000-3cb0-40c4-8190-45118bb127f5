<?php
/**
 * 上游返利记录
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/06/18
 * Time: 20:47:55
 */
namespace Models;
use Fuel\Defines\CooperationType;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilSupplierFanli extends \Framework\Database\Model
{
    protected $table = 'oil_supplier_fanli';

    protected $guarded = ["id"];
    protected $fillable = ['supplier_name','settle_obj','area_code','area_name','fanli_fee','service_fee',
        'trade_starttime','trade_endtime','fanli_type', 'rebate_form','arrive_type','status','creator','last_operator',
        'audit_operator', 'createtime','updatetime','audit_time','remark','audit_remark','supplier_id','area_id','is_del',
        'no','cooperation_type'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        if (isset($params['noLk']) && $params['noLk'] != '') {
            $query->where('no', 'like', "%".$params['noLk']."%");
        }

        //Search By supplier_id
        if (isset($params['supplier_id']) && $params['supplier_id'] != '') {
            $query->where('supplier_id', '=', $params['supplier_id']);
        }

        //Search By area_id
        if (isset($params['area_id']) && $params['area_id'] != '') {
            $query->where('area_id', '=', $params['area_id']);
        }

        if (isset($params['idIn']) && $params['idIn'] != '') {
            $query->whereIn('id', $params['idIn']);
        }

        //Search By supplier_name
        if (isset($params['supplier_name']) && $params['supplier_name'] != '') {
            $query->where('supplier_name', '=', $params['supplier_name']);
        }

        //Search By settle_obj
        if (isset($params['settle_obj']) && $params['settle_obj'] != '') {
            $query->where('settle_obj', '=', $params['settle_obj']);
        }

        if (isset($params['cooperation_type']) && $params['cooperation_type'] != '') {
            $query->where('cooperation_type', '=', $params['cooperation_type']);
        }

        //Search By area_code
        if (isset($params['area_code']) && $params['area_code'] != '') {
            $query->where('area_code', '=', $params['area_code']);
        }

        //Search By area_name
        if (isset($params['area_name']) && $params['area_name'] != '') {
            $query->where('area_name', '=', $params['area_name']);
        }

        //Search By fanli_fee
        if (isset($params['fanli_fee']) && $params['fanli_fee'] != '') {
            $query->where('fanli_fee', '=', $params['fanli_fee']);
        }

        //Search By service_fee
        if (isset($params['service_fee']) && $params['service_fee'] != '') {
            $query->where('service_fee', '=', $params['service_fee']);
        }

        //Search By trade_starttime
        if (isset($params['trade_starttime']) && $params['trade_starttime'] != '') {
            $query->where('trade_starttime', '=', $params['trade_starttime']);
        }

        //Search By trade_endtime
        if (isset($params['trade_endtime']) && $params['trade_endtime'] != '') {
            $query->where('trade_endtime', '=', $params['trade_endtime']);
        }

        if (isset($params['end_timeLe']) && $params['end_timeLe'] != '' && isset($params['starttimeGe']) && $params['starttimeGe'] != '') {
            //$query->where('end_time', '>=', $params['expire_end']." 23:59:59")->where('start_time', '>=', $params['expire_start']." 00:00:00");
            //$query->orWhere('start_time', '>=', $params['expire_start']." 00:00:00")->where('end_time', '>=', $params['expire_end']." 23:59:59");
            //$query->orWhere('start_time', '>=', $params['expire_start']." 00:00:00")->where('end_time', '<=', $params['expire_end']." 23:59:59");
            $query->where(function($query) use($params) {
                $query->orWhere(function ($query) use ($params) {
                    $query->Where('trade_starttime', '>=', $params['starttimeGe'])->where('trade_endtime', '>=', $params['end_timeLe']);
                });
                $query->orWhere(function ($query) use ($params) {
                    $query->Where('trade_starttime', '>=', $params['starttimeGe'])->where('trade_endtime', '<=', $params['end_timeLe']);
                });
                $query->orWhere(function ($query) use ($params) {
                    $query->where('trade_endtime', '>=', $params['end_timeLe'])->where('trade_starttime', '<=', $params['starttimeGe']);
                });
            });
        }

        //Search By fanli_type
        if (isset($params['fanli_type']) && $params['fanli_type'] != '') {
            $query->where('fanli_type', '=', $params['fanli_type']);
        }

        //Search By arrive_type
        if (isset($params['arrive_type']) && $params['arrive_type'] != '') {
            $query->where('arrive_type', '=', $params['arrive_type']);
        }

        //Search By rebate_form
        if (isset($params['rebate_form']) && $params['rebate_form'] != '') {
            $query->where('rebate_form', '=', $params['rebate_form']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By is_del
        if (isset($params['is_del']) && $params['is_del'] != '') {
            $query->where('is_del', '=', $params['is_del']);
        }

        //Search By creator
        if (isset($params['creator']) && $params['creator'] != '') {
            $query->where('creator', 'like', '%'.$params['creator'].'%');
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By audit_operator
        if (isset($params['audit_operator']) && $params['audit_operator'] != '') {
            $query->where('audit_operator', '=', $params['audit_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By createtime
        if (isset($params['create_timeGe']) && $params['create_timeGe'] != '') {
            $query->where('createtime', '>=', $params['create_timeGe']);
        }

        if (isset($params['create_timeLe']) && $params['create_timeLe'] != '') {
            $query->where('createtime', '<=', $params['create_timeLe']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By audit_time
        if (isset($params['audit_time']) && $params['audit_time'] != '') {
            $query->where('audit_time', '=', $params['audit_time']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By audit_remark
        if (isset($params['audit_remark']) && $params['audit_remark'] != '') {
            $query->where('audit_remark', '=', $params['audit_remark']);
        }

        return $query;
    }

    /**
     * 上游返利记录 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        //Capsule::connection()->enableQueryLog();
        if (isset($params['_countdata']) && $params['_countdata'] == 1) {
            $fields = "count(id) as total_count,sum(fanli_fee) as totalFanli,
            sum(service_fee) as totalService";
        } else {
            $fields = '*';
        }

        if(isset($params['settle_obj']) && $params['settle_obj'] == 30){
            $params['cooperation_type'] = $params['settle_obj'];
            unset($params['settle_obj']);
        }

        $sqlObj = OilSupplierFanli::Filter($params)->selectRaw($fields);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('createtime', 'desc')->get();
        }elseif (isset($params['_countdata']) && $params['_countdata'] == 1) {
            $result = $sqlObj->orderBy('createtime', 'desc')->get();
            return $result;
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;

        foreach ($data as &$_item){
            switch ($_item->status){
                case 20:
                    $_item->status_txt = "审核通过";
                    break;
                case 30:
                    $_item->status_txt = "审核驳回";
                    break;
                default:
                    $_item->status_txt = "待审核";
            }
            if($_item->settle_obj == 10){
                $_item->area_code = "--";
                $_item->area_name = "--";
            }
            if(empty($_item->remark)){
                $_item->remark = "";
            }
            if(empty($_item->audit_remark)){
                $_item->audit_remark = "";
            }
            $obj_txt = "2级-服务区";
            if($_item->cooperation_type == CooperationType::COOPERATION_TYPE_ZK){
                $obj_txt = "2级-主卡";
            }
            $_item->settle_obj_txt = $_item->settle_obj == 20 ? $obj_txt : "1级-油站供应商";
            $_item->fanli_type_txt = $_item->fanli_type == 20 ? "积分" : "现金";
            $_item->arrive_type_txt = $_item->arrive_type == 20 ? "不充到账户余额" : "充到账户余额";
            $_item->cooperation_type_txt = CooperationType::getById($_item->cooperation_type);
        }

        return $data;
    }

    /**
     * 上游返利记录 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierFanli::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierFanli::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 上游返利记录 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilSupplierFanli::create($params);
    }

    static public function batchAdd($params)
    {
        return OilSupplierFanli::insert($params);
    }

    /**
     * 上游返利记录 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierFanli::find($params['id'])->update($params);
    }

    static public function batchUpdate($ids,$updateData)
    {
        return OilSupplierFanli::whereIn('id',$ids)->update($updateData);
    }
    /**
     * 上游返利记录 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilSupplierFanli::destroy($params['ids']);
    }

    static public function getOneByFilter(array $params)
    {
        return OilSupplierFanli::Filter($params)->first();
    }

    static public function createNo($prefix = "SYFL")
    {
        $microTime = microtime();
        $microArr = explode(" ", $microTime);
        $str = $prefix . date("ymds", time());
        $str .= substr($microArr[0], 3, 3);
        $str .= sprintf('%02d', rand(0, 99));

        $exist = self::where("no", "=", $str)->lockForUpdate()->first();
        if ($exist) {
            self::createNo();
        }
        return $str;
    }
}