<?php
/**
 * 销售组织表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:17
 */
namespace Models;
use Framework\Database\Model;
use Illuminate\Database\Capsule\Manager as Capsule;

class GspSysOrgs extends Model
{
    protected $table = 'gsp_sys_orgs';

    protected $guarded = ["id"];

    protected $fillable = ['org_code','org_name','contact','tel','address','remark','creator_id','last_operator','createtime','updatetime','status','to_k3_status','k3_id','is_del'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function Users()
    {
        return $this->hasMany('Models\GspSysUsers','id','org_id');
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By org_code
        if (isset($params['org_code']) && $params['org_code'] != '') {
            $query->where('org_code', '=', $params['org_code']);
        }

        //Search By org_name
        if (isset($params['org_name']) && $params['org_name'] != '') {
            $query->where('org_name', '=', $params['org_name']);
        }

        //Search By contact
        if (isset($params['contact']) && $params['contact'] != '') {
            $query->where('contact', '=', $params['contact']);
        }

        //Search By tel
        if (isset($params['tel']) && $params['tel'] != '') {
            $query->where('tel', '=', $params['tel']);
        }

        //Search By address
        if (isset($params['address']) && $params['address'] != '') {
            $query->where('address', '=', $params['address']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By to_k3_status
        if (isset($params['to_k3_status']) && $params['to_k3_status'] != '') {
            $query->where('to_k3_status', '=', $params['to_k3_status']);
        }

        //Search By k3_id
        if (isset($params['k3_id']) && $params['k3_id'] != '') {
            $query->where('k3_id', '=', $params['k3_id']);
        }

        //Search By is_del
        if (isset($params['is_del']) && $params['is_del'] != '') {
            $query->where('is_del', '=', $params['is_del']);
        }


        return $query;
    }

    /**
     * 销售组织表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = GspSysOrgs::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 销售组织表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspSysOrgs::find($params['id']);
    }

    /**
     * 销售组织表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return GspSysOrgs::create($params);
    }

    /**
     * 销售组织表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspSysOrgs::find($params['id'])->update($params);
    }

    /**
     * 销售组织表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return GspSysOrgs::destroy($params['ids']);
    }

    /**
     * 根据org_name获取业务组织信息
     * @param $orgName
     */
    static public function getByOrgName($orgName)
    {
        return GspSysOrgs::where('org_name','=',$orgName)->where('is_del','=',0)->first();
    }

    /**
     * 获取$key=>$value一维数组
     * @param array $orgName
     * @param string $value
     * @param string $key
     * @return mixed
     */
    static public function getByOrgCodesMap(array $orgName,$value='id',$key='org_name')
    {
        return GspSysOrgs::whereIn('org_name',$orgName)->lists($value,$key)->toArray();
    }
}