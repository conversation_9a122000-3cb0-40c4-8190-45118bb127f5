<?php
/**
 * 机构主卡地区维度月度交易数据
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/12/14
 * Time: 19:24:08
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilOrgCardRegionMonthTrade extends \Framework\Database\Model
{
    protected $table = 'oil_org_card_region_month_trade';

    protected $guarded = ["id"];
    protected $fillable = ['date','org_operators_id','org_pcode','top_org_id','top_org_code','main_operators_id','main_pcode','card_main_id','main_no','province_id','province_code','oil_base_id','zsy_money','zsy_num','zsy_count','zsh_money','zsh_num','zsh_count','cylmk_money','cylmk_num','cylmk_count','qiaopai_money','qiaopai_num','qiaopai_count','czk_money','czk_num','czk_count','gxk_money','gxk_num','gxk_count','fck_money','fck_num','fck_count','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By date
        if (isset($params['date']) && $params['date'] != '') {
            $query->where('date', '=', $params['date']);
        }

        //Search By org_operators_id
        if (isset($params['org_operators_id']) && $params['org_operators_id'] != '') {
            $query->where('org_operators_id', '=', $params['org_operators_id']);
        }

        //Search By org_pcode
        if (isset($params['org_pcode']) && $params['org_pcode'] != '') {
            $query->where('org_pcode', '=', $params['org_pcode']);
        }

        //Search By top_org_id
        if (isset($params['top_org_id']) && $params['top_org_id'] != '') {
            $query->where('top_org_id', '=', $params['top_org_id']);
        }

        //Search By top_org_code
        if (isset($params['top_org_code']) && $params['top_org_code'] != '') {
            $query->where('top_org_code', '=', $params['top_org_code']);
        }

        //Search By main_operators_id
        if (isset($params['main_operators_id']) && $params['main_operators_id'] != '') {
            $query->where('main_operators_id', '=', $params['main_operators_id']);
        }

        //Search By main_pcode
        if (isset($params['main_pcode']) && $params['main_pcode'] != '') {
            $query->where('main_pcode', '=', $params['main_pcode']);
        }

        //Search By card_main_id
        if (isset($params['card_main_id']) && $params['card_main_id'] != '') {
            $query->where('card_main_id', '=', $params['card_main_id']);
        }

        //Search By main_no
        if (isset($params['main_no']) && $params['main_no'] != '') {
            $query->where('main_no', '=', $params['main_no']);
        }

        //Search By province_id
        if (isset($params['province_id']) && $params['province_id'] != '') {
            $query->where('province_id', '=', $params['province_id']);
        }

        //Search By province_code
        if (isset($params['province_code']) && $params['province_code'] != '') {
            $query->where('province_code', '=', $params['province_code']);
        }

        //Search By oil_base_id
        if (isset($params['oil_base_id']) && $params['oil_base_id'] != '') {
            $query->where('oil_base_id', '=', $params['oil_base_id']);
        }

        //Search By zsy_money
        if (isset($params['zsy_money']) && $params['zsy_money'] != '') {
            $query->where('zsy_money', '=', $params['zsy_money']);
        }

        //Search By zsy_num
        if (isset($params['zsy_num']) && $params['zsy_num'] != '') {
            $query->where('zsy_num', '=', $params['zsy_num']);
        }

        //Search By zsy_count
        if (isset($params['zsy_count']) && $params['zsy_count'] != '') {
            $query->where('zsy_count', '=', $params['zsy_count']);
        }

        //Search By zsh_money
        if (isset($params['zsh_money']) && $params['zsh_money'] != '') {
            $query->where('zsh_money', '=', $params['zsh_money']);
        }

        //Search By zsh_num
        if (isset($params['zsh_num']) && $params['zsh_num'] != '') {
            $query->where('zsh_num', '=', $params['zsh_num']);
        }

        //Search By zsh_count
        if (isset($params['zsh_count']) && $params['zsh_count'] != '') {
            $query->where('zsh_count', '=', $params['zsh_count']);
        }

        //Search By cylmk_money
        if (isset($params['cylmk_money']) && $params['cylmk_money'] != '') {
            $query->where('cylmk_money', '=', $params['cylmk_money']);
        }

        //Search By cylmk_num
        if (isset($params['cylmk_num']) && $params['cylmk_num'] != '') {
            $query->where('cylmk_num', '=', $params['cylmk_num']);
        }

        //Search By cylmk_count
        if (isset($params['cylmk_count']) && $params['cylmk_count'] != '') {
            $query->where('cylmk_count', '=', $params['cylmk_count']);
        }

        //Search By qiaopai_money
        if (isset($params['qiaopai_money']) && $params['qiaopai_money'] != '') {
            $query->where('qiaopai_money', '=', $params['qiaopai_money']);
        }

        //Search By qiaopai_num
        if (isset($params['qiaopai_num']) && $params['qiaopai_num'] != '') {
            $query->where('qiaopai_num', '=', $params['qiaopai_num']);
        }

        //Search By qiaopai_count
        if (isset($params['qiaopai_count']) && $params['qiaopai_count'] != '') {
            $query->where('qiaopai_count', '=', $params['qiaopai_count']);
        }

        //Search By czk_money
        if (isset($params['czk_money']) && $params['czk_money'] != '') {
            $query->where('czk_money', '=', $params['czk_money']);
        }

        //Search By czk_num
        if (isset($params['czk_num']) && $params['czk_num'] != '') {
            $query->where('czk_num', '=', $params['czk_num']);
        }

        //Search By czk_count
        if (isset($params['czk_count']) && $params['czk_count'] != '') {
            $query->where('czk_count', '=', $params['czk_count']);
        }

        //Search By gxk_money
        if (isset($params['gxk_money']) && $params['gxk_money'] != '') {
            $query->where('gxk_money', '=', $params['gxk_money']);
        }

        //Search By gxk_num
        if (isset($params['gxk_num']) && $params['gxk_num'] != '') {
            $query->where('gxk_num', '=', $params['gxk_num']);
        }

        //Search By gxk_count
        if (isset($params['gxk_count']) && $params['gxk_count'] != '') {
            $query->where('gxk_count', '=', $params['gxk_count']);
        }

        //Search By fck_money
        if (isset($params['fck_money']) && $params['fck_money'] != '') {
            $query->where('fck_money', '=', $params['fck_money']);
        }

        //Search By fck_num
        if (isset($params['fck_num']) && $params['fck_num'] != '') {
            $query->where('fck_num', '=', $params['fck_num']);
        }

        //Search By fck_count
        if (isset($params['fck_count']) && $params['fck_count'] != '') {
            $query->where('fck_count', '=', $params['fck_count']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 机构主卡地区维度月度交易数据 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilOrgCardRegionMonthTrade::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 机构主卡地区维度月度交易数据 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgCardRegionMonthTrade::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgCardRegionMonthTrade::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 机构主卡地区维度月度交易数据 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilOrgCardRegionMonthTrade::create($params);
    }

    /**
     * 机构主卡地区维度月度交易数据 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgCardRegionMonthTrade::find($params['id'])->update($params);
    }

    /**
     * 机构主卡地区维度月度交易数据 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilOrgCardRegionMonthTrade::destroy($params['ids']);
    }




}