<?php
/**
 * 预申请单详情列表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/03/21
 * Time: 16:58:46
 */

namespace Models;

use Models\OilCardApply;
use Illuminate\Database\Capsule\Manager as Capsule;
use \Framework\Log;
use \Fuel\Service\ApplyDetailService;

class OilCardApplyDetails extends \Framework\Database\Model
{
    protected $table = 'oil_card_apply_details';

    protected $guarded = ["id"];

    protected $fillable = [
        'card_apply_id',
        'truck_img_url',
        'name',
        'truck_img_type',
        'truck_status',
        'truck_no',
        'id_card_front_url',
        'id_card_back_url',
        'id_card_front_status',
        'id_card_back_status',
        'createtime',
        'updatetime',
    ];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By card_apply_id
        if (isset($params['card_apply_id']) && $params['card_apply_id'] != '') {
            $query->where('card_apply_id', '=', $params['card_apply_id']);
        }

        //Search By truck_img_url
        if (isset($params['truck_img_url']) && $params['truck_img_url'] != '') {
            $query->where('truck_img_url', '=', $params['truck_img_url']);
        }

        //Search By name
        if (isset($params['name']) && $params['name'] != '') {
            $query->where('name', '=', $params['name']);
        }

        //Search By truck_img_type
        if (isset($params['truck_img_type']) && $params['truck_img_type'] != '') {
            $query->where('truck_img_type', '=', $params['truck_img_type']);
        }

        //Search By truck_stauts
        if (isset($params['truck_stauts']) && $params['truck_stauts'] != '') {
            $query->where('truck_stauts', '=', $params['truck_stauts']);
        }

        //Search By truck_no
        if (isset($params['truck_no']) && $params['truck_no'] != '') {
            $query->where('truck_no', '=', $params['truck_no']);
        }

        //Search By id_card_url
        if (isset($params['id_card_url']) && $params['id_card_url'] != '') {
            $query->where('id_card_url', '=', $params['id_card_url']);
        }

        //Search By id_card_img_url
        if (isset($params['id_card_img_url']) && $params['id_card_img_url'] != '') {
            $query->where('id_card_img_url', '=', $params['id_card_img_url']);
        }

        //Search By id_card_stauts
        if (isset($params['id_card_stauts']) && $params['id_card_stauts'] != '') {
            $query->where('id_card_stauts', '=', $params['id_card_stauts']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 预申请单详情列表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCardApplyDetails::Filter($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * 预申请单详情列表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardApplyDetails::find($params['id']);
    }

    /**
     * 详情查询
     * @param array $params
     * @return object
     */
    static public function getDetailsByAppId(array $params)
    {
        \helper::argumentCheck(['card_apply_id'], $params);

        return OilCardApplyDetails::select('*')->where('card_apply_id', '=', $params['card_apply_id'])->get()->toArray();
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardApplyDetails::lockForUpdate()->where('id', $params['id'])->first();
    }

    /**
     * 预申请单详情列表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCardApplyDetails::create($params);
    }

    /**
     * 预申请单详情列表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardApplyDetails::find($params['id'])->update($params);
    }

    /**
     * 预申请单详情列表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilCardApplyDetails::destroy($params['ids']);
    }

    /**
     * @title 驾驶证数据入库
     * @desc
     * <AUTHOR> <<EMAIL>>
     * @package Models
     * @since 2018/4/3
     * @params  type filedName required?
     * @param array $params
     * @param $sb_result
     * @return \Illuminate\Database\Eloquent\Model|object
     */
    public static function uploadLiscence(array $params, $sb_result)
    {
        if (isset($params['apply_id']) && $params['apply_id'] != '') {
            // 已存在
            $cardApply = OilCardApply::find($params['apply_id']);
        } else {
            // 新建草稿
            $cardApply = OilCardApply::create([
                'orgcode'  => $params['org_code'],
                'org_name' => $params['org_name'],
            ]);
        }

        // 司机名称
        $owner = $sb_result->owner ?: '';

        if (empty($sb_result->name) && empty($sb_result->plateNum)) {
            // 识别失败
            $truck_status = 2;
        } else {
            $truck_status = 1;
        }

        // 验证车牌号合法性
        if (!ApplyDetailService::isCarLicense($sb_result->plateNum)) {
            $truck_status = 2;
        }

        $info = [
            'truck_img_url'  => $params['pic_url'],
            'name'           => $owner,
            'truck_img_type' => mb_strlen($owner) > 6 ? 2 : 1,
            'truck_status'   => $truck_status,
            'truck_no'       => $sb_result->plateNum,
        ];

        // 识别成功就验证是否已经存在 存在不入库直接返回
        if ($truck_status == 1 &&
            self::getRepeatTruck($params['apply_id'], $sb_result->plateNum)
        ) {
            $info['repeat'] = TRUE;

            return (object)$info;
        }

        if (!empty($params['img_id'])) {
            $details = OilCardApplyDetails::find($params['img_id']);
            $details->update($info);
        } else {
            $details = $cardApply->cardDetails()->create($info);
        }

        // 重新统计数量
        ApplyDetailService::updateOilNum($params['apply_id']);

        return $details;
    }

    /**
     * @title 身份证信息入库
     * @desc
     * <AUTHOR> <<EMAIL>>
     * @package Models
     * @since 2018/4/3
     * @params  type filedName required?
     * @param array $params
     * @param $sb_result
     * @return mixed
     */
    public static function uploadIDCard(array $params, $sb_result)
    {
        $cardApplyDetails = OilCardApplyDetails::find($params['img_id']);
        if ($params['direction'] == 'front') {
            $id_card_front_status = 1;
            if (empty($sb_result->name)) {
                $message = '身份正面证识别失败 请上传正确的照片';
                $id_card_front_status = 0;
            }
            if ($sb_result->name != $cardApplyDetails->name) {
                $message = '身份证信息与驾驶证信息不匹配';
                $id_card_front_status = 0;
            }
            $info['id_card_front_url'] = $params['pic_url'];
            $info['id_card_front_status'] = $id_card_front_status;
        } else {
            $id_card_back_status = 1;
            if (empty($sb_result->invalidDate)) {
                $message = '身份证反面识别失败 请上传正确的照片';
                $id_card_back_status = 0;
            } else {
                if (time() - strtotime($sb_result->invalidDate) >= 0) {
                    $message = '身份证不在有效期范围内';
                    $id_card_back_status = 0;
                }
            }
            $info['id_card_back_url'] = $params['pic_url'];
            $info['id_card_back_status'] = $id_card_back_status;
        }

        $cardApplyDetails->update($info);
        $cardApplyDetails->message = $message;

        return $cardApplyDetails;
    }

    /**
     * @title 删除未识别的信息
     * @desc
     * <AUTHOR> <<EMAIL>>
     * @package Models
     * @since 2018/4/3
     * @params  type filedName required?
     * @param $apply_id
     * @return mixed
     */
    public static function delNoRegImages($apply_id)
    {
        return OilCardApplyDetails::where('card_apply_id', '=', $apply_id)
            ->where('truck_status', '=', '2')
            ->delete();
    }

    /**
     * @title 删除同一工单下车牌号相同的驾驶证
     * @desc
     * <AUTHOR> <<EMAIL>>
     * @package Models
     * @since 2018/4/3
     * @params  type filedName required?
     * @param $apply_id
     * @param $truck_no
     * @return bool
     */
    public static function getRepeatTruck($apply_id, $truck_no)
    {
        $count = OilCardApplyDetails::where('card_apply_id', $apply_id)
            ->where('truck_no', $truck_no)
            ->count();

        return $count > 0;
    }

}