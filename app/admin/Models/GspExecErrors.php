<?php
/**
 * 执行错误信息表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:16
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class GspExecErrors extends \Framework\Database\Model
{
    protected $table = 'gsp_exec_errors';

    protected $guarded = ["id"];

    protected $fillable = ['e_code','e_msg','e_desc','e_class','e_method','e_line','creator_id','createtime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By e_code
        if (isset($params['e_code']) && $params['e_code'] != '') {
            $query->where('e_code', '=', $params['e_code']);
        }

        //Search By e_msg
        if (isset($params['e_msg']) && $params['e_msg'] != '') {
            $query->where('e_msg', '=', $params['e_msg']);
        }

        //Search By e_desc
        if (isset($params['e_desc']) && $params['e_desc'] != '') {
            $query->where('e_desc', '=', $params['e_desc']);
        }

        //Search By e_class
        if (isset($params['e_class']) && $params['e_class'] != '') {
            $query->where('e_class', '=', $params['e_class']);
        }

        //Search By e_method
        if (isset($params['e_method']) && $params['e_method'] != '') {
            $query->where('e_method', '=', $params['e_method']);
        }

        //Search By e_line
        if (isset($params['e_line']) && $params['e_line'] != '') {
            $query->where('e_line', '=', $params['e_line']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }


        return $query;
    }

    /**
     * 执行错误信息表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = GspExecErrors::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 执行错误信息表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspExecErrors::find($params['id']);
    }

    /**
     * 执行错误信息表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return GspExecErrors::create($params);
    }

    /**
     * 执行错误信息表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspExecErrors::find($params['id'])->update($params);
    }

    /**
     * 执行错误信息表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return GspExecErrors::destroy($params['ids']);
    }




}