<?php
/**
 * 上游账户流水消费扩展表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2022/06/17
 * Time: 10:03:59
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilSupplierAccountStatementTradeExt extends \Framework\Database\Model
{
    protected $table = 'oil_supplier_account_statement_trade_ext';

    protected $guarded = ["id"];
    protected $fillable = ['statement_id','trades_id','policy_id','serial_num','rebate_value','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }
    
        //Search By trades_id
        if (isset($params['trades_id']) && $params['trades_id'] != '') {
            $query->where('trades_id', '=', $params['trades_id']);
        }
        
        //Search By statement_id
        if (isset($params['statement_id']) && $params['statement_id'] != '') {
            $query->where('statement_id', '=', $params['statement_id']);
        }

        //Search By policy_id
        if (isset($params['policy_id']) && $params['policy_id'] != '') {
            if (is_array()) {
                $query->whereIn('policy_id', $params['policy_id']);
            } else {
                $query->where('policy_id', '=', $params['policy_id']);
            }
        }

        //Search By serial_num
        if (isset($params['serial_num']) && is_numeric($params['serial_num'])) {
            $query->where('serial_num', '=', $params['serial_num']);
        }

        //Search By rebate_value
        if (isset($params['rebate_value']) && $params['rebate_value'] != '') {
            $query->where('rebate_value', '=', $params['rebate_value']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 上游账户流水消费扩展表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilSupplierAccountStatementTradeExt::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 上游账户流水消费扩展表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierAccountStatementTradeExt::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierAccountStatementTradeExt::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 上游账户流水消费扩展表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilSupplierAccountStatementTradeExt::create($params);
    }

    /**
     * 上游账户流水消费扩展表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierAccountStatementTradeExt::find($params['id'])->update($params);
    }

    /**
     * 上游账户流水消费扩展表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilSupplierAccountStatementTradeExt::destroy($params['ids']);
    }
    
    /**
     * 取字段值
     * @param array $params
     * @param $pluckField
     * @return array
     */
    static public function getPluckFields(array $params, $pluckField)
    {
        return self::Filter($params)->orderBy('createtime', 'asc')->orderBy('id', 'asc')->pluck($pluckField)->toArray();
    }
    
    /**
     * 取字段值 返回第一个值
     * @param array $params
     * @param $pluckField
     * @return mixed
     */
    static public function getResField(array $params, $pluckField)
    {
        $res = self::Filter($params)->pluck($pluckField)->toArray();
        
        return !empty($res[0]) ? $res[0] : '';
    }
    
    /**
     * 根据条件取信息
     * @param array $params
     * @return object
     */
    static public function getInfoByFilter(array $params)
    {
        return self::Filter($params)->orderBy('id', 'desc')->first();
    }
    
    /**
     * 取总数
     * @param array $params
     * @return mixed
     */
    static public function getTotal(array $params)
    {
        return self::Filter($params)->count();
    }
    
    /**
     * 根据条件更新
     * @param array $params
     * @param array $updateInfo
     * @return mixed
     */
    static public function updateByFilter(array $params, array $updateInfo)
    {
        return self::Filter($params)->update($updateInfo);
    }

}