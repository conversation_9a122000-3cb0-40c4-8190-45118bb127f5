<?php
/**
 * 车辆卡领取记录表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2022/02/11
 * Time: 15:29:09
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilTruckCardGet extends \Framework\Database\Model
{
    protected $table = 'oil_truck_card_get';

    protected $guarded = ["id"];
    protected $fillable = ['vice_card_id','driver_name','truck_no','get_type',
        'mobile','vice_no','start_time','end_time','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By mobile
        if (isset($params['mobile']) && $params['mobile'] != '') {
            $query->where('mobile', '=', $params['mobile']);
        }

        //Search By vice_no
        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $query->where('vice_no', '=', $params['vice_no']);
        }

        //Search By start_time
        if (isset($params['start_time']) && $params['start_time'] != '') {
            $query->where('start_time', '=', $params['start_time']);
        }

        //Search By end_time
        if (isset($params['end_time']) && $params['end_time'] != '') {
            $query->where('end_time', '=', $params['end_time']);
        }

        //Search By create_at
        if (isset($params['create_at']) && $params['create_at'] != '') {
            $query->where('create_at', '=', $params['create_at']);
        }

        //Search By update_at
        if (isset($params['update_at']) && $params['update_at'] != '') {
            $query->where('update_at', '=', $params['update_at']);
        }

        return $query;
    }

    /**
     * 车辆卡领取记录表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilTruckCardGet::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 车辆卡领取记录表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilTruckCardGet::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilTruckCardGet::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 车辆卡领取记录表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilTruckCardGet::create($params);
    }

    /**
     * 车辆卡领取记录表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilTruckCardGet::find($params['id'])->update($params);
    }

    /**
     * 车辆卡领取记录表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilTruckCardGet::destroy($params['ids']);
    }




}