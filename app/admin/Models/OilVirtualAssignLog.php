<?php
/**
 * 虚拟卡分配记录
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/07/31
 * Time: 15:53:50
 */
namespace Models;
use Framework\DingTalk\DingTalkAlarm;
use Framework\Log;
use Fuel\Defines\AccountType;
use Fuel\Service\AccountCenter\AccountException;
use Fuel\Service\AccountCenter\AccountService;
use G7Pay\Core\AccountCredit;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilVirtualAssignLog extends \Framework\Database\Model
{
    protected $table = 'oil_virtual_assign_log';

    protected $guarded = ["id"];
    protected $fillable = ['type','org_id','app_id','extID','billID','virtual_vice_no','money','creator_name','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By type
        if (isset($params['type']) && $params['type'] != '') {
            $query->where('type', '=', $params['type']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By app_id
        if (isset($params['app_id']) && $params['app_id'] != '') {
            $query->where('app_id', '=', $params['app_id']);
        }

        //Search By app_id
        if (isset($params['billID']) && $params['billID'] != '') {
            $query->where('billID', '=', $params['billID']);
        }

        //Search By virtual_vice_no
        if (isset($params['virtual_vice_no']) && $params['virtual_vice_no'] != '') {
            $query->where('virtual_vice_no', '=', $params['virtual_vice_no']);
        }

        //Search By money
        if (isset($params['money']) && $params['money'] != '') {
            $query->where('money', '=', $params['money']);
        }

        //Search By creator_name
        if (isset($params['creator_name']) && $params['creator_name'] != '') {
            $query->where('creator_name', '=', $params['creator_name']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 虚拟卡分配记录 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilVirtualAssignLog::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 虚拟卡分配记录 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilVirtualAssignLog::find($params['id']);
    }

    /**
     * 虚拟卡分配记录 详情查询
     * @param array $params
     * @return object
     */
    static public function getByApplyId(array $params)
    {
        \helper::argumentCheck(['app_id'],$params);

        return OilVirtualAssignLog::where("app_id",$params['app_id'])->first();
    }

    /**
     * 虚拟卡分配记录 详情查询
     * @param array $params
     * @return object
     */
    static public function getByApplyextID(array $params)
    {
        \helper::argumentCheck(['extID'],$params);

        return OilVirtualAssignLog::where("extID",$params['extID'])->first();
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilVirtualAssignLog::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 虚拟卡分配记录 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilVirtualAssignLog::create($params);
    }

    /**
     * 虚拟卡分配记录 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilVirtualAssignLog::find($params['id'])->update($params);
    }

    /**
     * 虚拟卡分配记录 编辑
     * @param array $params
     * @return mixed
     */
    static public function editByExtID($extID,array $params)
    {
        return OilVirtualAssignLog::where("extID",$extID)->update($params);
    }

    /**
     * 虚拟卡分配记录 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilVirtualAssignLog::destroy($params['ids']);
    }

    /**
     * @title 借款或充值卡授信分配到普罗斯
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function convertCardAssignMoney(array $params)
    {
        $res = NULL;
        \helper::argumentCheck(['amount_money_total','org_id','extID','type','app_id','creator_name'],$params);

        if($params['amount_money_total'] < 500){
            throw new \RuntimeException('最小充值金额不得低于500元',2);
        }

        //查询机构运营商
        $orgInfo = OilOrg::getById(['id' => $params['org_id']]);
        if (!$orgInfo) {
            //AccountException::handler($params['org_id'] . '未找到', 2);
            throw new \RuntimeException($params['org_id'] . '未找到',2);
        }

        //获取subAccountID
        $subAccountId = self::getSubAccountId($orgInfo);
        //$subAccountId = '********';
        if (!$subAccountId) {
            throw new \RuntimeException($params['org_id'] . '授信账户未找到',2);
            //AccountException::handler($params['org_id'] . '授信账户未找到', 2);
        }

        //4万5分一张
        $cardNum = $params['amount_money_total'] < 45000 ? 1 : ceil($params['amount_money_total'] / 45000);

        //取虚拟卡对应的卡
        $cardList = OilVirtualViceRelation::getCardByLimit([
            'org_id'=>$params['org_id'],
            'cardNum'=>$cardNum,
        ]);

        if(count($cardList) < $cardNum){

            require_once APP_MODULE_ROOT . DIRECTORY_SEPARATOR . 'oil_virtual_vice_relation' . DIRECTORY_SEPARATOR . 'control.php';
            $relObj = new \oil_virtual_vice_relation();
            try{
                $_POST['orgcode'] = $orgInfo->orgcode;
                $_POST['nums'] = $cardNum;
                $_POST['isReturn'] = true;
                $virRes = $relObj->createVirtulCard();
                Log::error('生成虚拟卡error'.var_export($virRes, TRUE),[],'cardAllocDirect');
                if(is_array($virRes) && count($virRes)){
                    (new DingTalkAlarm())->alarmToGroup("虚拟卡生成失败", "\n\n机构：".$orgInfo->orgcode."环境：".API_ENV, [], TRUE);
                    throw new \RuntimeException($virRes['msg'],401);
                }
                //(new DingTalkAlarm())->alarmToGroup("虚拟卡数量不够", "虚拟卡数量不够\n\n机构：".$orgInfo->orgcode."\n\n须办卡数量为：".$cardNum."\n\n环境：".API_ENV, [], TRUE);
            } catch (\Exception $e){
                Log::error('生成虚拟卡error',[$e->getMessage()],'cardAllocDirect');
                throw new \RuntimeException($e->getMessage(),401);
                //Log::error('DingTalk'.$e->getMessage(),$e->getCode());
            }
            //再次获取虚拟卡对应的卡
            $cardList = OilVirtualViceRelation::getCardByLimit([
                'org_id'=>$params['org_id'],
                'cardNum'=>$cardNum,
            ]);
        }


        $assignData = [
            'totalAmount' => abs(intval(bcmul($params['amount_money_total'], 100))),
            'subAccountID' => $subAccountId,
            'extID' => $params['extID']
        ];

        if($cardList){
            //得到授信发送数据
            $sendData = self::getSendData($params,$cardList,$orgInfo);
            $assignData['capitalDetail'] = $sendData['details'];

            //入库oil_virtual_assign_log
            if($sendData['insertLogData']){
                foreach ($sendData['insertLogData'] as $item){
                    self::add($item);
                }
            }

            try{
                //请求账户中心
                Log::error('cardAllocDirect:params:::' . var_export($assignData, TRUE), [], 'cardAllocDirect');
                $res = (new AccountCredit())->cardAllocDirect($assignData);
                $billID = null;
                Log::error('cardAllocDirect:res:::' . var_export($res, TRUE), [], 'cardAllocDirect');
                if ($res && $res->billID) {
                    $billID = $res->billID;
                    //todo 需要根据extID更新billID
                    self::editByExtID($params['extID'],["billID"=>$billID]);
                }

                /*//入库oil_virtual_assign_log
                if($sendData['insertLogData']){
                    foreach ($sendData['insertLogData'] as $item){
                        if(!empty($billID)) {
                            $item['billID'] = $billID;
                        }
                        self::add($item);
                    }
                }*/
            } catch (\Exception $e){
                Log::error('授信-预分配:' . $e->getCode() . '--' . $e->getMessage(), [], 'accountException');
                //AccountException::handler($e->getMessage(), $e->getCode(), strval($e),$exception = 1);
                throw new \RuntimeException($e->getMessage(),$e->getCode());
            }
        }else{
            throw new \RuntimeException('此机构无虚拟石化卡',2);
        }

        return $res;
    }

    /*
     * 获取subAccountID
     */
    static public function getSubAccountId($orgInfo)
    {
        $creditInfo = OilCreditAccount::getByOrgIdOne(['org_id'=>$orgInfo->id]);

        if(!$creditInfo->subAccountID){
            //g7Pay credit account
            $accountRecord = (new AccountService())->getBalanceByOrgCode(['orgCode' => $orgInfo->orgcode]);
            if ($accountRecord && isset($accountRecord->data)) {
                foreach ($accountRecord->data as $v) {
                    if ($v->subAccountType == AccountType::ACCOUNT_CENTER_CREDIT_GLP) {
                        $subAccountID = $v->subAccountID;
                        OilCreditAccount::updateByAccountNo(['account_no' => $creditInfo->account_no, 'subAccountID' => $v->subAccountID]);
                        break;
                    }
                }
            }
        }else{
            $subAccountID = $creditInfo->subAccountID;
        }

        return $subAccountID;
    }

    /*
     * 拼接授信发送数据
     */
    static public function getSendData($params,$cardList,$orgInfo)
    {
        $remainder = $params['amount_money_total'];
        $details = $insertLogData = [];
        foreach ($cardList as $value){
            if($remainder > 0){
                //拼接普罗斯数据
                $amount = $remainder > 45000 ? 45000 : $remainder;
                $details[] = [
                    'amount' => abs(intval(bcmul($amount, 100))),//分配额度,
                    'applyTime' => date('c', strtotime(\helper::nowTime())),
                    'cardSubAccountID' => $value->cash_cardSubAccountID,
                    'orgBelong' => $orgInfo->org_name, //所属机构
                    'orgPay' => $orgInfo->org_name, //付款机构
                ];

                $insertLogData[] = [
                    'type' => $params['type'],
                    'app_id' => $params['app_id'],
                    'org_id' => $params['org_id'],
                    'extID'  => $params['extID'],
                    'virtual_vice_no' => $value->virtual_vice_no,
                    'money' => abs(intval(bcmul($amount, 100))),//分配额度
                    'creator_name' => $params['creator_name'],
                    'createtime' => \helper::nowTime(),
                ];

                $remainder = $remainder - 45000;
            }
        }

        return ['details' => $details,'insertLogData' => $insertLogData];
    }


}