<?php
/**
 * 返利计算任务表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/02/21
 * Time: 09:39:57
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilFanliRuleCal extends \Framework\Database\Model
{
    protected $table = 'oil_fanli_rule_cal';

    protected $guarded = ["id"];
    protected $fillable = ['no','source','status','audit_status','fanli_total_money','fanli_total_jifen','createtime','updatetime','creator_id','last_operator','is_del'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By idIn
        if (isset($params['idIn']) && $params['idIn'] != '') {
            $query->whereIn('id', $params['idIn']);
        }

        //Search By no
        if (isset($params['no']) && $params['no'] != '') {
            $query->where('no', '=', $params['no']);
        }

        //Search By fanli_noLk
        if (isset($params['fanli_noLk']) && $params['fanli_noLk'] != '') {
            $query->where('no', '=', $params['fanli_noLk']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By audit_status
        if ( isset($params['audit_status']) && $params['audit_status'] != '') {
            $query->where('audit_status', '=', $params['audit_status']);
        }

        //Search By fanli_total_money
        if (isset($params['fanli_total_money']) && $params['fanli_total_money'] != '') {
            $query->where('fanli_total_money', '=', $params['fanli_total_money']);
        }

        //Search By fanli_total_jifen
        if (isset($params['fanli_total_jifen']) && $params['fanli_total_jifen'] != '') {
            $query->where('fanli_total_jifen', '=', $params['fanli_total_jifen']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By expire_cal_start
        if (isset($params['expire_cal_start']) && $params['expire_cal_start'] != '') {
            $query->where('createtime', '>=', $params['expire_cal_start']);
        }

        if (isset($params['expire_cal_end']) && $params['expire_cal_end'] != '') {
            $query->where('createtime', '<=', $params['expire_cal_end']." 23:59:59");
        }

        //Search By expire_data_start
        if (isset($params['expire_data_start']) && $params['expire_data_start'] != '') {
            $query->where('createtime', '>=', $params['expire_data_start']);
        }

        if (isset($params['expire_data_end']) && $params['expire_data_end'] != '') {
            $query->where('createtime', '<=', $params['expire_data_end']." 23:59:59");
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By is_del
        if (isset($params['is_del']) && ($params['is_del'] != '' || $params['is_del'] >= 0) ) {
            $query->where('is_del', '=', $params['is_del']);
        }

        return $query;
    }

    /**
     * 返利计算任务表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilFanliRuleCal::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('createtime', 'desc')->get();
        }elseif(isset($params['_count']) && $params['_count'] == 1){
            return $sqlObj->count();
        }elseif (isset($params['skip']) && isset($params['take'])) {
            $data = $sqlObj->orderBy('createtime', 'desc')->skip(intval($params['skip']))->take(intval($params['take']))->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        foreach ($data as &$val){
            switch ($val->audit_status){
                case 1:
                    $val->audit_status_txt = "已审核";
                    break;
                case 2:
                    $val->audit_status_txt = "已驳回";
                    break;
                default:
                    $val->audit_status_txt = "待审核";
            }
            $condition['_count'] = 1;
            $condition['module_name'] = "oil_fanli_rule_cal";
            $condition["third_id"] = $val->id;
            $val->log_num = OilSysOperatorLogs::getList($condition);
            $val->cal_date = substr($val->createtime,0,16);
            $val->source_txt = $val->source == 2 ? "导入" : '计算';
        }
        return $data;
    }

    /**
     * 返利计算任务表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilFanliRuleCal::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilFanliRuleCal::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 返利计算任务表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilFanliRuleCal::create($params);
    }

    /**
     * 返利计算任务表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilFanliRuleCal::find($params['id'])->update($params);
    }

    /**
     * 返利计算任务表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilFanliRuleCal::destroy($params['ids']);
    }

    /**
     * 根据id批量编辑
     * @param array $params
     * @return mixed
     */
    static public function batchEdit(array $params,$condition)
    {
        return self::whereIn("id",$condition)->update($params);
    }

    /**
     * @title 生成返利单号
     * <AUTHOR>
     * @return string
     */
    static public function createOrderNo()
    {
        $order_no = 'FL'.date('ymd');
        $fanli_no   = self::where('no','like',$order_no.'%')->max('no');
        if($fanli_no){
            $order_no .= sprintf("%04d",(substr($fanli_no,-4) + 1));
        }else{
            $order_no .= '0001';
        }

        return $order_no;
    }
}