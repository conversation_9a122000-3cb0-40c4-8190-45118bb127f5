<?php
/**
 * 系统模块数据操作日志
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/07/05
 * Time: 14:19:03
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilSysOperatorLogs extends \Framework\Database\Model
{
    protected $table = 'oil_sys_operator_logs';

    protected $guarded = ["id"];
    protected $fillable = ['system_name','log_time','module_name','method_name','third_id','request_params','ip','operator_id','operator_account','operator_name','operator_content','complete_status','remark','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By system_name
        if (isset($params['system_name']) && $params['system_name'] != '') {
            $query->where('system_name', '=', $params['system_name']);
        }

        //Search By log_time
        if (isset($params['log_time']) && $params['log_time'] != '') {
            $query->where('log_time', '=', $params['log_time']);
        }

        //Search By module_name
        if (isset($params['module_name']) && $params['module_name'] != '') {
            $query->where('module_name', 'like',"%".$params['module_name']."%");
        }

        //Search By method_name
        if (isset($params['method_name']) && $params['method_name'] != '') {
            $query->where('method_name', 'like', '%'.$params['method_name']."%");
        }

        if (isset($params['method_nameNotIn']) && $params['method_nameNotIn'] != '') {
            $query->whereNotIn('method_name', $params['method_nameNotIn']);
        }


        //Search By third_id
        if (isset($params['third_id']) && $params['third_id'] != '') {
            $query->where('third_id', '=', $params['third_id']);
        }

        //Search By request_params
        if (isset($params['request_params']) && $params['request_params'] != '') {
            $query->where('request_params', '=', $params['request_params']);
        }

        //Search By ip
        if (isset($params['ip']) && $params['ip'] != '') {
            $query->where('ip', '=', $params['ip']);
        }

        //Search By operator_id
        if (isset($params['operator_id']) && $params['operator_id'] != '') {
            $query->where('operator_id', '=', $params['operator_id']);
        }

        //Search By operator_account
        if (isset($params['operator_account']) && $params['operator_account'] != '') {
            $query->where('operator_account', 'like', '%'.$params['operator_account'].'%');
        }

        //Search By operator_name
        if (isset($params['operator_name']) && $params['operator_name'] != '') {
            $query->where('operator_name', 'like', '%'.$params['operator_name'].'%');
        }

        //Search By operator_content
        if (isset($params['operator_content']) && $params['operator_content'] != '') {
            $query->where('operator_content', '=', $params['operator_content']);
        }

        //Search By complete_status
        if (isset($params['complete_status']) && $params['complete_status'] != '') {
            $query->where('complete_status', '=', $params['complete_status']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        if (isset($params['operator_timeGe']) && $params['operator_timeGe'] != '') {
            $query->where('createtime', '>=', $params['operator_timeGe']);
        }

        if (isset($params['operator_timeLe']) && $params['operator_timeLe'] != '') {
            $query->where('createtime', '<=', $params['operator_timeLe']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 系统模块数据操作日志 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilSysOperatorLogs::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 系统模块数据操作日志 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSysOperatorLogs::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSysOperatorLogs::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 系统模块数据操作日志 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilSysOperatorLogs::create($params);
    }

    /**
     * 系统模块数据操作日志 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSysOperatorLogs::find($params['id'])->update($params);
    }

    /**
     * 系统模块数据操作日志 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilSysOperatorLogs::destroy($params['ids']);
    }




}