<?php
/**
 * oil_jobs_failed
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/03/26
 * Time: 11:01:35
 */

namespace Models;

use Illuminate\Database\Capsule\Manager as Capsule;

class OilJobsFailed extends \Framework\Database\Model
{
    protected $table = 'oil_jobs_failed';
    
    public $incrementing = FALSE;
    
    protected $guarded  = ['id'];
    protected $fillable = [
        'id', 'channel', 'service', 'params', 'userInfo', 'tried','type',
        'willTries', 'message', 'exception', 'createtime', 'updatetime'
    ];
    
    public function getFillAble()
    {
        return $this->fillable;
    }
    
    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }
        
        //Search By channel
        if (isset($params['channel']) && $params['channel'] != '') {
            $query->where('channel', '=', $params['channel']);
        }
        
        //Search By service
        if (isset($params['service']) && $params['service'] != '') {
            $query->where('service', '=', $params['service']);
        }
        
        //Search By params
        if (isset($params['params']) && $params['params'] != '') {
            $query->where('params', '=', $params['params']);
        }
        
        //Search By userInfo
        if (isset($params['userInfo']) && $params['userInfo'] != '') {
            $query->where('userInfo', '=', $params['userInfo']);
        }
        
        //Search By tried
        if (isset($params['tried']) && $params['tried'] != '') {
            $query->where('tried', '=', $params['tried']);
        }
        
        //Search By willTries
        if (isset($params['willTries']) && $params['willTries'] != '') {
            $query->where('willTries', '=', $params['willTries']);
        }
        
        //Search By message
        if (isset($params['message']) && $params['message'] != '') {
            $query->where('message', '=', $params['message']);
        }
        
        //Search By exception
        if (isset($params['exception']) && $params['exception'] != '') {
            $query->where('exception', '=', $params['exception']);
        }
        
        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }
        
        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }
        
        return $query;
    }
    
    /**
     * oil_jobs_failed 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilJobsFailed::Filter($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }
        
        return $data;
    }
    
    /**
     * oil_jobs_failed 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilJobsFailed::find($params['id']);
    }
    
    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilJobsFailed::lockForUpdate()->where('id', $params['id'])->first();
    }
    
    /**
     * oil_jobs_failed 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilJobsFailed::create($params);
    }
    
    /**
     * oil_jobs_failed 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilJobsFailed::find($params['id'])->update($params);
    }
    
    /**
     * oil_jobs_failed 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);
        
        return OilJobsFailed::destroy($params['ids']);
    }
    
    
}