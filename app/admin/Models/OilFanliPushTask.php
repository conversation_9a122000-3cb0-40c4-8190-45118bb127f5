<?php
/**
 * 返利推送任务
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/02/02
 * Time: 11:03:56
 */

namespace Models;

use Framework\Helper;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilFanliPushTask extends \Framework\Database\Model
{
    protected $table = 'oil_fanli_push_task';

    public $incrementing = FALSE;

    protected $guarded = ["id"];
    protected $fillable = ['id', 'billID', 'extID', 'type', 'no', 'org_id', 'account_id', 'g7pay_account_id', 'amount',
        'status', 'createtime', 'pushtime', 'updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By billID
        if (isset($params['billID']) && $params['billID'] != '') {
            $query->where('billID', '=', $params['billID']);
        }

        //Search By extID
        if (isset($params['extID']) && $params['extID'] != '') {
            $query->where('extID', '=', $params['extID']);
        }

        //Search By type
        if (isset($params['type']) && $params['type'] != '') {
            $query->where('type', '=', $params['type']);
        }

        //Search By no
        if (isset($params['no']) && $params['no'] != '') {
            $query->where('no', '=', $params['no']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By account_id
        if (isset($params['account_id']) && $params['account_id'] != '') {
            $query->where('account_id', '=', $params['account_id']);
        }

        //Search By g7pay_account_id
        if (isset($params['g7pay_account_id']) && $params['g7pay_account_id'] != '') {
            $query->where('g7pay_account_id', '=', $params['g7pay_account_id']);
        }

        //Search By amount
        if (isset($params['amount']) && $params['amount'] != '') {
            $query->where('amount', '=', $params['amount']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By pushtime
        if (isset($params['pushtime']) && $params['pushtime'] != '') {
            $query->where('pushtime', '=', $params['pushtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 返利推送任务 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilFanliPushTask::Filter($params);

        if(isset($params['count']) && intval($params['count']) == 1){
            return $sqlObj->count();
        }elseif(isset($params['take']) && intval($params['take']) == 1){
            if(isset($params['skip']) && intval($params['skip'])){
                $sqlObj->skip(intval($params['skip']));
            }
            $data = $sqlObj->take(intval($params['take']))->get();
        }elseif (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * 返利推送任务 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilFanliPushTask::find($params['id']);
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilFanliPushTask::lockForUpdate()->where('id', $params['id'])->first();
    }

    /**
     * 返利推送任务 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        $params['id'] = Helper::uuid();

        return OilFanliPushTask::create($params);
    }

    /**
     * 返利推送任务 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilFanliPushTask::find($params['id'])->update($params);
    }

    /**
     * 返利推送任务 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilFanliPushTask::destroy($params['ids']);
    }


}