<?php
/**
 * 油品机构联系人
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:18
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilOrgContacts extends \Framework\Database\Model
{
    protected $table = 'oil_org_contacts';

    protected $guarded = ["id"];

    protected $fillable = ['org_id','contact_name','contact_mobile','contact_mobile2','contact_email','contact_region',
        'contact_qq','remark','is_active','creator_id','last_operator','other_creator_id','other_creator','createtime',
        'updatetime','is_del','gos_id'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By contact_name
        if (isset($params['contact_name']) && $params['contact_name'] != '') {
            $query->where('contact_name', '=', $params['contact_name']);
        }

        //Search By contact_mobile
        if (isset($params['contact_mobile']) && $params['contact_mobile'] != '') {
            $query->where('contact_mobile', '=', $params['contact_mobile']);
        }

        //Search By contact_mobile2
        if (isset($params['contact_mobile2']) && $params['contact_mobile2'] != '') {
            $query->where('contact_mobile2', '=', $params['contact_mobile2']);
        }

        //Search By contact_email
        if (isset($params['contact_email']) && $params['contact_email'] != '') {
            $query->where('contact_email', '=', $params['contact_email']);
        }

        //Search By contact_region
        if (isset($params['contact_region']) && $params['contact_region'] != '') {
            $query->where('contact_region', '=', $params['contact_region']);
        }

        //Search By contact_qq
        if (isset($params['contact_qq']) && $params['contact_qq'] != '') {
            $query->where('contact_qq', '=', $params['contact_qq']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By is_active
        if (isset($params['is_active']) && $params['is_active'] != '') {
            $query->where('is_active', '=', $params['is_active']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By other_creator_id
        if (isset($params['other_creator_id']) && $params['other_creator_id'] != '') {
            $query->where('other_creator_id', '=', $params['other_creator_id']);
        }

        //Search By other_creator
        if (isset($params['other_creator']) && $params['other_creator'] != '') {
            $query->where('other_creator', '=', $params['other_creator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By is_del
        if (isset($params['is_del']) && $params['is_del'] != '') {
            $query->where('is_del', '=', $params['is_del']);
        }


        return $query;
    }

    /**
     * 油品机构联系人 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilOrgContacts::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 油品机构联系人 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgContacts::find($params['id']);
    }

    /**
     * 油品机构联系人 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        $params['createtime'] = \helper::nowTime();
        return OilOrgContacts::create($params);
    }

    /**
     * 油品机构联系人 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgContacts::find($params['id'])->update($params);
    }

    /**
     * 油品机构联系人 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilOrgContacts::destroy($params['ids']);
    }
    
    
    /**
     * @title   通过条件过滤出指定字段数组
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     * @param array $params
     * @param       $pluckField
     * @return mixed
     * @returns
     * mixed
     * @returns
     */
    static public function getPluckFields(array $params, $pluckField)
    {
        return OilOrgContacts::Filter($params)->pluck($pluckField)->toArray();
    }
    
    /**
     * 根据条件取信息
     * @param array $params
     * @return object
     */
    static public function getInfoByFilter(array $params)
    {
        return OilOrgContacts::Filter($params)->first();
    }
}