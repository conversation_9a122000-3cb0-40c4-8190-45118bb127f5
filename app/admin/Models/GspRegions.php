<?php
/**
 * 地区表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:16
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class GspRegions extends \Framework\Database\Model
{
    protected $table = 'gsp_regions';

    protected $guarded = ["id"];

    protected $fillable = ['code','name'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By code
        if (isset($params['code']) && $params['code'] != '') {
            $query->where('code', '=', $params['code']);
        }

        //Search By name
        if (isset($params['name']) && $params['name'] != '') {
            $query->where('name', '=', $params['name']);
        }


        return $query;
    }

    /**
     * 地区表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = GspRegions::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 地区表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspRegions::find($params['id']);
    }

    /**
     * 地区表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return GspRegions::create($params);
    }

    /**
     * 地区表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return GspRegions::find($params['id'])->update($params);
    }

    /**
     * 地区表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return GspRegions::destroy($params['ids']);
    }




}