<?php
/**
 * 账户中心请求日志
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2017/12/15
 * Time: 23:16:03
 */

namespace Models;

use Framework\Helper;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilAccountCenterLog extends \Framework\Database\Model
{
    protected $table = 'oil_account_center_log';

    protected $fillable = ['id', 'subject', 'action', 'params', 'data', 'message',
        'status', 'time_use',
        'createtime', 'updatetime'];

    public $incrementing = FALSE;

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By subject
        if (isset($params['subject']) && $params['subject'] != '') {
            $query->where('subject', '=', $params['subject']);
        }

        //Search By action
        if (isset($params['action']) && $params['action'] != '') {
            $query->where('action', '=', $params['action']);
        }

        //Search By params
        if (isset($params['params']) && $params['params'] != '') {
            $query->where('params', '=', $params['params']);
        }

        //Search By data
        if (isset($params['data']) && $params['data'] != '') {
            $query->where('data', '=', $params['data']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By time_use
        if (isset($params['time_use']) && $params['time_use'] != '') {
            $query->where('time_use', '=', $params['time_use']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 账户中心请求日志 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $sqlObj = self::Filter($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * 账户中心请求日志 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return self::find($params['id']);
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return self::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 账户中心请求日志 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        $params['id'] = Helper::uuid();
        $params['createtime'] = date("Y-m-d H:i:s");

        return self::create($params);
    }

    /**
     * 账户中心请求日志 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return self::find($params['id'])->update($params);
    }

    /**
     * 账户中心请求日志 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return self::destroy($params['ids']);
    }

    /*
     * 定时删除
     */
    static public function cronSetTimeOutRemove(array $params)
    {
        return OilAccountCenterLog::where('createtime','<',$params['ltTime'])->delete();
    }


}