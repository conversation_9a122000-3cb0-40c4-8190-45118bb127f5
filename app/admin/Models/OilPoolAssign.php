<?php
/**
 * oil_pool_assign
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/04/12
 * Time: 12:44:05
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilPoolAssign extends \Framework\Database\Model
{
    protected $table = 'oil_pool_assign';

    protected $guarded = ["id"];

    protected $fillable = ['no','no_type','from_org_id','into_org_id','from_orgcode','from_orgname','into_orgcode','into_orgname','app_time','money','status','data_from','is_del','creator_id','other_creator_id','other_creator','last_operator','remark','createtime','updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By no
        if (isset($params['no']) && $params['no'] != '') {
            $query->where('no', '=', $params['no']);
        }

        //Search By no_type
        if (isset($params['no_type']) && $params['no_type'] != '') {
            $query->where('no_type', '=', $params['no_type']);
        }

        //Search By from_org_id
        if (isset($params['from_org_id']) && $params['from_org_id'] != '') {
            $query->where('from_org_id', '=', $params['from_org_id']);
        }

        //Search By into_org_id
        if (isset($params['into_org_id']) && $params['into_org_id'] != '') {
            $query->where('into_org_id', '=', $params['into_org_id']);
        }

        //Search By from_orgcode
        if (isset($params['from_orgcode']) && $params['from_orgcode'] != '') {
            $query->where('from_orgcode', '=', $params['from_orgcode']);
        }

        //Search By from_orgname
        if (isset($params['from_orgname']) && $params['from_orgname'] != '') {
            $query->where('from_orgname', '=', $params['from_orgname']);
        }

        //Search By into_orgcode
        if (isset($params['into_orgcode']) && $params['into_orgcode'] != '') {
            $query->where('into_orgcode', '=', $params['into_orgcode']);
        }

        //Search By into_orgname
        if (isset($params['into_orgname']) && $params['into_orgname'] != '') {
            $query->where('into_orgname', '=', $params['into_orgname']);
        }

        //Search By app_time
        if (isset($params['app_time']) && $params['app_time'] != '') {
            $query->where('app_time', '=', $params['app_time']);
        }

        //Search By money
        if (isset($params['money']) && $params['money'] != '') {
            $query->where('money', '=', $params['money']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By data_from
        if (isset($params['data_from']) && $params['data_from'] != '') {
            $query->where('data_from', '=', $params['data_from']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By other_creator_id
        if (isset($params['other_creator_id']) && $params['other_creator_id'] != '') {
            $query->where('other_creator_id', '=', $params['other_creator_id']);
        }

        //Search By other_creator
        if (isset($params['other_creator']) && $params['other_creator'] != '') {
            $query->where('other_creator', '=', $params['other_creator']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }


        return $query;
    }

    /**
     * oil_pool_assign 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilPoolAssign::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_pool_assign 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilPoolAssign::find($params['id']);
    }

    /**
     * oil_pool_assign 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilPoolAssign::create($params);
    }

    /**
     * oil_pool_assign 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilPoolAssign::find($params['id'])->update($params);
    }

    /**
     * oil_pool_assign 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilPoolAssign::destroy($params['ids']);
    }

    /**
     * 生成单号
     * @param $head 编号首字母
     * 编号 + 年月日 + 当日序号最大值+1
     *
     */
    public function createNo($head = '')
    {
        $no = "";
        if(!empty($head))
        {
            $no = $head.date('ymd');
            Capsule::connection()->enableQueryLog();
            $result = Capsule::connection()->select("SELECT MAX(no) as no FROM oil_pool_assign WHERE no LIKE '$no%'");
            if($result[0]->no)
            {
                $no .= sprintf("%05d",(substr($result[0]->no,-5) + 1));
            }
            else
            {
                $no .= '00001';
            }
        }
        return $no;
    }



}