<?php
/**
 * oil_credit_payment_details
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/07/30
 * Time: 12:00:10
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCreditPaymentDetails extends \Framework\Database\Model
{
    protected $table = 'oil_credit_payment_details';

    protected $guarded = ["id"];
    protected $fillable = ['payment_id','payNo','chargeNo','trade_id','trade_extID','vice_no','trade_money','trade_num','service_fee','orgcode','org_name','trade_time','status','remark','createtime','updatetime','last_operator_id','last_operator'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By payNo
        if (isset($params['payment_id']) && $params['payment_id'] != '') {
            $query->where('payment_id', '=', $params['payment_id']);
        }

        //Search By payNo
        if (isset($params['payNo']) && $params['payNo'] != '') {
            $query->where('payNo', '=', $params['payNo']);
        }

        //Search By trade_id
        if (isset($params['trade_id']) && $params['trade_id'] != '') {
            $query->where('trade_id', '=', $params['trade_id']);
        }

        //Search By vice_no
        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $query->where('vice_no', '=', $params['vice_no']);
        }

        //Search By trade_money
        if (isset($params['trade_money']) && $params['trade_money'] != '') {
            $query->where('trade_money', '=', $params['trade_money']);
        }

        //Search By trade_num
        if (isset($params['trade_num']) && $params['trade_num'] != '') {
            $query->where('trade_num', '=', $params['trade_num']);
        }

        //Search By service_fee
        if (isset($params['service_fee']) && $params['service_fee'] != '') {
            $query->where('service_fee', '=', $params['service_fee']);
        }

        //Search By orgcode
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $query->where('orgcode', '=', $params['orgcode']);
        }

        //Search By org_name
        if (isset($params['org_name']) && $params['org_name'] != '') {
            $query->where('org_name', '=', $params['org_name']);
        }

        //Search By trade_time
        if (isset($params['trade_time']) && $params['trade_time'] != '') {
            $query->where('trade_time', '=', $params['trade_time']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By last_operator_id
        if (isset($params['last_operator_id']) && $params['last_operator_id'] != '') {
            $query->where('last_operator_id', '=', $params['last_operator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        return $query;
    }

    /**
     * oil_credit_payment_details 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCreditPaymentDetails::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        foreach ($data as $key => &$val){
            $val->remark = $val->remark ? $val->remark : "";
            $val->trade_id = $val->trade_id ? $val->trade_id : "";
            $val->trade_extID = $val->trade_extID ? $val->trade_extID : "";
            $val->id_extID = $val->trade_id;
            if(!empty($val->trade_extID)){
                $val->id_extID = $val->id_extID."#".$val->trade_extID;
            }

            switch ($val->status){
                case 1:
                    $val->status_txt = "已处理";
                    break;
                default:
                    $val->status_txt = "待处理";
            }
        }

        return $data;
    }

    /**
     * oil_credit_payment_details 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCreditPaymentDetails::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCreditPaymentDetails::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_credit_payment_details 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCreditPaymentDetails::create($params);
    }

    /**
     * oil_credit_payment_details 批量新增
     * @param array $params
     * @return mixed
     */
    static public function batchAdd(array $params)
    {
        return OilCreditPaymentDetails::insert($params);
    }

    /**
     * oil_credit_payment_details 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCreditPaymentDetails::find($params['id'])->update($params);
    }

    /**
     * oil_credit_payment_details 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCreditPaymentDetails::destroy($params['ids']);
    }

    /**
     * oil_credit_payment_details 根据api_id删除或批量删除
     * @param array $params
     * @return int
     */
    static public function removeByApiId(array $params)
    {
        \helper::argumentCheck(['payment_id'],$params);

        return OilCreditPaymentDetails::where("payment_id",$params['payment_id'])->delete();
    }


}