<?php
/**
 * oil_receipt_title_quota
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/05/04
 * Time: 15:26:17
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilReceiptTitleQuota extends \Framework\Database\Model
{
    protected $table = 'oil_receipt_title_quota';

    protected $guarded = ["id"];
    protected $fillable = ['orgroot','org_name','receipt_title_id','corp_name','receipt_type','pay_company_id','exclusive_custom','is_recepit_nowtime','company_name','is_have_corp','total_charge','total_repaid','total_charge_addition_repaid','total_receipt','tatal_charge_subtraction_receipted','total_trades','total_frozen','total_trades_subtraction_frozen','max_amount','_exclusive_custom','_is_recepit_nowtime','is_first_receipt_apply'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By orgroot
        if (isset($params['orgroot']) && $params['orgroot'] != '') {
            $query->where('orgroot', '=', $params['orgroot']);
        }

        //Search By org_name
        if (isset($params['org_name']) && $params['org_name'] != '') {
            $query->where('org_name', '=', $params['org_name']);
        }

        //Search By receipt_title_id
        if (isset($params['receipt_title_id']) && $params['receipt_title_id'] != '') {
            $query->where('receipt_title_id', '=', $params['receipt_title_id']);
        }

        //Search By corp_name
        if (isset($params['corp_name']) && $params['corp_name'] != '') {
            $query->where('corp_name', '=', $params['corp_name']);
        }

        //Search By receipt_type
        if (isset($params['receipt_type']) && $params['receipt_type'] != '') {
            $query->where('receipt_type', '=', $params['receipt_type']);
        }

        //Search By pay_company_id
        if (isset($params['pay_company_id']) && $params['pay_company_id'] != '') {
            $query->where('pay_company_id', '=', $params['pay_company_id']);
        }

        //Search By exclusive_custom
        if (isset($params['exclusive_custom']) && $params['exclusive_custom'] != '') {
            $query->where('exclusive_custom', '=', $params['exclusive_custom']);
        }

        //Search By is_recepit_nowtime
        if (isset($params['is_recepit_nowtime']) && $params['is_recepit_nowtime'] != '') {
            $query->where('is_recepit_nowtime', '=', $params['is_recepit_nowtime']);
        }

        //Search By company_name
        if (isset($params['company_name']) && $params['company_name'] != '') {
            $query->where('company_name', '=', $params['company_name']);
        }

        //Search By is_have_corp
        if (isset($params['is_have_corp']) && $params['is_have_corp'] != '') {
            $query->where('is_have_corp', '=', $params['is_have_corp']);
        }

        //Search By total_charge
        if (isset($params['total_charge']) && $params['total_charge'] != '') {
            $query->where('total_charge', '=', $params['total_charge']);
        }

        //Search By total_repaid
        if (isset($params['total_repaid']) && $params['total_repaid'] != '') {
            $query->where('total_repaid', '=', $params['total_repaid']);
        }

        //Search By total_charge_addition_repaid
        if (isset($params['total_charge_addition_repaid']) && $params['total_charge_addition_repaid'] != '') {
            $query->where('total_charge_addition_repaid', '=', $params['total_charge_addition_repaid']);
        }

        //Search By total_receipt
        if (isset($params['total_receipt']) && $params['total_receipt'] != '') {
            $query->where('total_receipt', '=', $params['total_receipt']);
        }

        //Search By tatal_charge_subtraction_receipted
        if (isset($params['tatal_charge_subtraction_receipted']) && $params['tatal_charge_subtraction_receipted'] != '') {
            $query->where('tatal_charge_subtraction_receipted', '=', $params['tatal_charge_subtraction_receipted']);
        }

        //Search By total_trades
        if (isset($params['total_trades']) && $params['total_trades'] != '') {
            $query->where('total_trades', '=', $params['total_trades']);
        }

        //Search By total_frozen
        if (isset($params['total_frozen']) && $params['total_frozen'] != '') {
            $query->where('total_frozen', '=', $params['total_frozen']);
        }

        //Search By total_trades_subtraction_frozen
        if (isset($params['total_trades_subtraction_frozen']) && $params['total_trades_subtraction_frozen'] != '') {
            $query->where('total_trades_subtraction_frozen', '=', $params['total_trades_subtraction_frozen']);
        }

        //Search By max_amount
        if (isset($params['max_amount']) && $params['max_amount'] != '') {
            $query->where('max_amount', '=', $params['max_amount']);
        }

        //Search By _exclusive_custom
        if (isset($params['_exclusive_custom']) && $params['_exclusive_custom'] != '') {
            $query->where('_exclusive_custom', '=', $params['_exclusive_custom']);
        }

        //Search By _is_recepit_nowtime
        if (isset($params['_is_recepit_nowtime']) && $params['_is_recepit_nowtime'] != '') {
            $query->where('_is_recepit_nowtime', '=', $params['_is_recepit_nowtime']);
        }

        //Search By is_first_receipt_apply
        if (isset($params['is_first_receipt_apply']) && $params['is_first_receipt_apply'] != '') {
            $query->where('is_first_receipt_apply', '=', $params['is_first_receipt_apply']);
        }

        return $query;
    }

    /**
     * oil_receipt_title_quota 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilReceiptTitleQuota::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_receipt_title_quota 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptTitleQuota::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptTitleQuota::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_receipt_title_quota 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilReceiptTitleQuota::create($params);
    }

    /**
     * oil_receipt_title_quota 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptTitleQuota::find($params['id'])->update($params);
    }

    /**
     * oil_receipt_title_quota 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilReceiptTitleQuota::destroy($params['ids']);
    }




}