<?php
/**
 * 预分配记录表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/10/11
 * Time: 14:20:03
 */
namespace Models;
use Framework\Log;
use Illuminate\Database\Capsule\Manager as Capsule;
use Fuel\Request\dspClient;

class OilPreAssign extends \Framework\Database\Model
{
    protected $table = 'oil_pre_assign';

    protected $guarded = ["id"];
    protected $fillable = ['api_id','main_no','money_total','jifen_total','before_balance','after_balance','order_id','customer_name','fetch_time','assign_time','status','assign_num','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By api_id
        if (isset($params['api_id']) && $params['api_id'] != '') {
            $query->where('api_id', '=', $params['api_id']);
        }

        //Search By main_no
        if (isset($params['main_no']) && $params['main_no'] != '') {
            $query->where('main_no', '=', $params['main_no']);
        }

        //Search By money
        if (isset($params['money_total']) && $params['money_total'] != '') {
            $query->where('money_total', '=', $params['money_total']);
        }

        //Search By jifen
        if (isset($params['jifen_total']) && $params['jifen_total'] != '') {
            $query->where('jifen_total', '=', $params['jifen_total']);
        }

        //Search By before_balance
        if (isset($params['before_balance']) && $params['before_balance'] != '') {
            $query->where('before_balance', '=', $params['before_balance']);
        }

        //Search By after_balance
        if (isset($params['after_balance']) && $params['after_balance'] != '') {
            $query->where('after_balance', '=', $params['after_balance']);
        }

        //Search By order_id
        if (isset($params['order_id']) && $params['order_id'] != '') {
            $query->where('order_id', '=', $params['order_id']);
        }

        //Search By customer_name
        if (isset($params['customer_name']) && $params['customer_name'] != '') {
            $query->where('customer_name', 'like', '%'.$params['customer_name'].'%');
        }

        //Search By fetch_time
        if (isset($params['fetch_time']) && $params['fetch_time'] != '') {
            $query->where('fetch_time', '=', $params['fetch_time']);
        }

        //Search By assign_time
        if (isset($params['assign_time']) && $params['assign_time'] != '') {
            $query->where('assign_time', '=', $params['assign_time']);
        }

        //Search By assigntimeGe
        if (isset($params['assigntimeGe']) && $params['assigntimeGe'] != '') {
            $query->where('assign_time', '>=', $params['assigntimeGe'] . ' 00:00:00');
        }

        //Search By assigntimeLe
        if (isset($params['assigntimeLe']) && $params['assigntimeLe'] != '') {
            $query->where('assign_time', '<=', $params['assigntimeLe'] . ' 23:59:59');
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By assign_num
        if (isset($params['assign_num']) && $params['assign_num'] != '') {
            $query->where('assign_num', '=', $params['assign_num']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 预分配记录表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilPreAssign::Filter($params)->orderBy('assign_time','desc')->orderBy('createtime','desc');
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * search data from oilAgent
     * @return mixed
     */
    static public function syncAssignOrderData($page)
    {
        $startId = Capsule::connection()->select("select max(api_id) startId from oil_pre_assign");

        $startId = $startId[0]->startId + 1;

        Log::error('$startId--'.$startId,[],'oilPreAssign');

        $data        = dspClient::post(
            [
                'method' => 'huoyunren.gascard.syncAssignOrderData',
                'data'   => [
                    'data'=>json_encode([
                        'sysId' => 'GSP',
                        'page' => $page,
                        'limit' => '1000',
                        'startId'  => $startId,
                    ]),
                    'format'=>'json'
                ]
            ]
        );

        return $data;
    }

    static public function test()
    {
        $data        = dspClient::post(
            [
                'method' => 'huoyunren.gascard.syncAssignOrderData',
                'data'   => [
                    'data'=>json_encode([
                        'sysId' => 'GSP',
                        'page' => 1,
                        'limit' => '2000',
                        'startId'  => 1,
                    ]),
                    'format'=>'json'
                ]
            ]
        );

        var_dump($data);
    }

    /**
     * 预分配记录表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilPreAssign::find($params['id']);
    }

    /**
     * 预分配记录表 详情查询
     * @param array $params
     * @return object
     */
    static public function getByApiId(array $params)
    {
        \helper::argumentCheck(['api_id'],$params);

        return OilPreAssign::where('api_id',$params['api_id'])->first();
    }

    /**
     * 预分配记录表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilPreAssign::create($params);
    }

    /**
     * 预分配记录表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilPreAssign::find($params['id'])->update($params);
    }

    /**
     * 预分配记录表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilPreAssign::destroy($params['ids']);
    }




}