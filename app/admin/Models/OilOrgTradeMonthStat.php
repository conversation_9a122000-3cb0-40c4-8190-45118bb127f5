<?php
/**
 * oil_org_trade_month_stat
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/02/11
 * Time: 10:56:41
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilOrgTradeMonthStat extends \Framework\Database\Model
{
    protected $table = 'oil_org_trade_month_stat';

    
    protected $fillable = ['id','org_id','orgcode','org_name','oil_type','total_amount','total_use_fanli','month','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $query->where('orgcode', 'like', $params['orgcode'] . '%');
        }

        if (isset($params['oil_type_list']) && $params['oil_type_list'] != '') {
            $query->whereIn('oil_type', $params['oil_type_list']);
        }

        if (isset($params['oil_type_eq']) && $params['oil_type_eq'] != '') {
            $query->where('oil_type','=', $params['oil_type_eq']);
        }

        if (isset($params['stat_split_time']) && $params['stat_split_time'] != '') {
            $month = date("Ym",strtotime($params['stat_split_time']));
            $query->where('month','<', $month);
        }

        return $query;
    }

    /**
     * oil_org_trade_month_stat 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilOrgTradeMonthStat::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_org_trade_month_stat 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgTradeMonthStat::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgTradeMonthStat::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_org_trade_month_stat 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilOrgTradeMonthStat::create($params);
    }

    /**
     * oil_org_trade_month_stat 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgTradeMonthStat::find($params['id'])->update($params);
    }

    /**
     * oil_org_trade_month_stat 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilOrgTradeMonthStat::destroy($params['ids']);
    }

    static public function sumTradeMoney(array $params)
    {
        //Capsule::connection()->enableQueryLog();
        $data = $sqlObj = OilOrgTradeMonthStat::Filter($params)->sum("total_amount");
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        return $data;
    }


    static public function sumTradeUseFanliMoney(array $params)
    {
        //Capsule::connection()->enableQueryLog();
        $data = $sqlObj = OilOrgTradeMonthStat::Filter($params)->sum("total_use_fanli");
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        return $data;
    }


}