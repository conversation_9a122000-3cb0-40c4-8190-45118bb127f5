<?php
/**
 * 企业签约合同图片上传表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/03/20
 * Time: 15:08:19
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCompanyContractImg extends \Framework\Database\Model
{
    protected $table = 'oil_company_contract_img';

    public $timestamps = false;

    protected $guarded = ["id"];
    protected $fillable = ['company_id','img_url','sort'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By company_id
        if (isset($params['company_id']) && $params['company_id'] != '') {
            $query->where('company_id', '=', $params['company_id']);
        }

        //Search By img_url
        if (isset($params['img_url']) && $params['img_url'] != '') {
            $query->where('img_url', '=', $params['img_url']);
        }

        //Search By sort
        if (isset($params['sort']) && $params['sort'] != '') {
            $query->where('sort', '=', $params['sort']);
        }

        return $query;
    }

    /**
     * 企业签约合同图片上传表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCompanyContractImg::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 企业签约合同图片上传表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCompanyContractImg::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCompanyContractImg::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 企业签约合同图片上传表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCompanyContractImg::create($params);
    }

    /**
     * 企业签约合同图片上传表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCompanyContractImg::find($params['id'])->update($params);
    }

    /**
     * 企业签约合同图片上传表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCompanyContractImg::destroy($params['ids']);
    }




}