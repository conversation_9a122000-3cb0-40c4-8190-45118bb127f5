<?php
ini_set('date.timezone', 'Asia/Shanghai');//设置时区
/* 记录最开始的时间。*/
global $timeStart;
$timeStart = _getTime();

use Framework\Config;
use Framework\Mailer\MailSender;

/* composer autoloader */
define('APP_ROOT', realpath(dirname(__DIR__)));
define('APP_CONFIG', APP_ROOT . DIRECTORY_SEPARATOR . 'config');
define('APP_MODULE_ROOT', APP_ROOT . DIRECTORY_SEPARATOR . 'module');
define('APP_WWW_ROOT', APP_ROOT . DIRECTORY_SEPARATOR . 'www');
define('APP_LIB_ROOT', APP_ROOT . DIRECTORY_SEPARATOR . 'library');
define('APP_SERVICE_ROOT', APP_LIB_ROOT . DIRECTORY_SEPARATOR . 'Fuel'.DIRECTORY_SEPARATOR.'Service');
define('GOS_SDK_CONFIG', APP_CONFIG . DIRECTORY_SEPARATOR . 'gosSdk.php');
require_once APP_ROOT . DIRECTORY_SEPARATOR . 'vendor' . DIRECTORY_SEPARATOR . 'autoload.php';

/* 包含必须的类文件。*/
include_once dirname(dirname(dirname(dirname(__FILE__)))) . DIRECTORY_SEPARATOR . 'lib' . DIRECTORY_SEPARATOR
	. 'dubhe-client-php' . DIRECTORY_SEPARATOR . 'Du.php';
//include dirname(dirname(dirname(dirname(__FILE__)))) . DIRECTORY_SEPARATOR . 'framework' . DIRECTORY_SEPARATOR . 'error.class.php';
include_once APP_LIB_ROOT . DIRECTORY_SEPARATOR . 'Framework'.DIRECTORY_SEPARATOR.'Exception'.DIRECTORY_SEPARATOR.'RegisterErrorHandler.php';

include dirname(dirname(dirname(dirname(__FILE__)))) . DIRECTORY_SEPARATOR . 'framework' . DIRECTORY_SEPARATOR . 'router.class.php';
include dirname(dirname(dirname(dirname(__FILE__)))) . DIRECTORY_SEPARATOR . 'framework' . DIRECTORY_SEPARATOR . 'control.class.php';
include dirname(dirname(dirname(dirname(__FILE__)))) . DIRECTORY_SEPARATOR . 'framework' . DIRECTORY_SEPARATOR . 'model.class.php';
include dirname(dirname(dirname(dirname(__FILE__)))) . DIRECTORY_SEPARATOR . 'framework' . DIRECTORY_SEPARATOR . 'helper.class.php';
include dirname(dirname(dirname(dirname(__FILE__)))) . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . 'admin' . DIRECTORY_SEPARATOR . 'lib' . DIRECTORY_SEPARATOR . 'base.control.class.php';
include dirname(dirname(dirname(dirname(__FILE__)))) . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . 'admin' . DIRECTORY_SEPARATOR . 'lib' . DIRECTORY_SEPARATOR . 'cliargs.php';

require_once dirname(dirname(__FILE__)) . DIRECTORY_SEPARATOR . 'module' . DIRECTORY_SEPARATOR . 'client' . DIRECTORY_SEPARATOR . 'lib' . DIRECTORY_SEPARATOR . 'config.inc.php';
require_once APP_ROOT .'/common/functions.php';


// console init framework env
CliArgs::initFrameworkEnv();
CliArgs::loadArg($argv, $argc);

// 创建URL类型的Transaction
if (isset($_REQUEST['method']) && $_REQUEST['method']) {
	$t = Du::newTransaction("URL", "{$_SERVER['PHP_SELF']}?m={$_REQUEST['m']}&f={$_REQUEST['f']}&method={$_REQUEST['method']}");
} else {
	$t = Du::newTransaction("URL", "{$_SERVER['PHP_SELF']}?m={$_REQUEST['m']}&f={$_REQUEST['f']}");
}
register_shutdown_function("complete", $t);


if (isset($_SERVER['HTTP_DUCONTEXT']) && $_SERVER['HTTP_DUCONTEXT']) {
	$ctx = json_decode(base64_decode($_SERVER['HTTP_DUCONTEXT']));
	Du::logRemoteCallServer($ctx->_catRootMessageId, $ctx->_catParentMessageId, $ctx->_catChildMessageId);
}
try {
	/* 实例化路由对象，并加载配置，连接到数据库，加载common模块。*/
	$app = router::createApp('gos', dirname(dirname(__FILE__)));
	$config = $app->loadConfig('common');
	//由于消化代理数据，存在model字段为*
	if(!isset($_REQUEST['spider_msg_type']) || empty($_REQUEST['spider_msg_type'])){
        $common = $app->loadCommon();
    }
	define('API_ENV', $config->api_env); //api环境

    $maintainConfig = Config::get('maintain');
    if($maintainConfig['status'] && !in_array($app->myAdmin->organ->id,$maintainConfig['whiteList'])){
        throw new \RuntimeException($maintainConfig['message'], $maintainConfig['code']);
    }

	/**
	 * 处理请求，加载相应的模块
	 */
	$app->parseRequest();
	$app->loadModule();

	// 设置Transaction成功
	$t->setStatus(Du::SUCCESS);
} catch (Exception $e) {
	// 输出JSON格式错误
	$ecode = $e->getCode();
	$result = [
		'code'    => $ecode,
		'message' => strval($e)
	];

	if ($ecode != 403)
		helper::datalog('inside|' . $ecode . '|' . $e->getMessage(), 'syslog_');


	header("Content-Type:	application/json");
	$errorstring = json_encode($result, JSON_UNESCAPED_UNICODE);

	if (!in_array($ecode, [2, 403])) {
		// 设置Transaction失败，并记录日志
		$t->setStatus("Error");
		Du::logError('Error', get_class($e), $e);
	} else {
		// 设置Transaction成功
		$t->setStatus(Du::SUCCESS);
	}

	echo $errorstring;
	exit;
}

function complete($t)
{
	if ($t->getStatus() == 'unset')
		$t->setStatus(Du::SUCCESS);
	$t->complete();
}

/* 获取系统时间，微秒为单位。*/
function _getTime()
{
	list($usec, $sec) = explode(" ", microtime());

	return ((float)$usec + (float)$sec);
}

session_write_close();
