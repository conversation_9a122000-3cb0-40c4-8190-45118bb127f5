<?php
/**
 * 公共类,权限检查
 * <AUTHOR>
 * @package ZenTaoPHP
 */

class baseControl extends control {
	/**
	 * 构造函数：
	 *
	 * 1. 引用全局对象，使之可以通过成员变量访问。
	 * 2. 设置模块相应的路径信息，并加载对应的model文件。
	 * 3. 自动将$lang和$config赋值到模板。
	 *
	 * @access public
	 * @return void
	 */
	public $dbh;//事务处理所用的DB库变量      Story #8396gsp系统添加事务处理功能
	public function __construct($moduleName = '', $methodName = '')
	{
		parent::__construct();
		$this->mkFrameDir();//创建框架目录 Story #43067
		/*检查当前用户是否有当前资源的权限*/
//		$this->checkPower();
		//事务处理所用的DB库  Story #8396gsp系统添加事务处理功能
		$this->dbh = model::getDbh('db');
	}


	/**
	 * 检查当前用户是否有当前资源的权限
	 * <AUTHOR>
	 * @since 2013/12/19
	 */
	public function checkPower(){
		//当前用户拥有的权限
		$source = $this->app->myAdmin->source;
		//所有权限
		$sourceAll = $this->app->myAdmin->sourceAll;
		$moduleName = $this->moduleName;
		$methodName = $this->methodName;
		$hasPower = true;
		$hasModulePower = false;
		if($source){
			$hasPower = false;
			$sourceRec = new stdClass();
			$sourceRec->source_id = 'asdfasdf';
			foreach($source as $sv){
				$m = explode('/',$sv->url);
				if($m[1] == $moduleName){//拥有当前module的权限
					$hasModulePower = true;
					$sourceRec = $sv;
				}
			}
			//检查是否有所请求的控件的权限
//			$hasPower = $this->checkSource($sourceRec,$hasModulePower);
			$moduleName == 'common'?$hasPower=true:'';
			$moduleName == 'login'?$hasPower=true:'';
		}
		if(!$hasPower){//没有权限
			throw new \RuntimeException("Do not have access!");
		}
	}

    /*
     * 检测用户是否已离职
     * 该接口已废弃不可用
     */
    public function checkUserResign($email)
    {
        $status = 1;//默认离职
        if(!$email){
            \Framework\Log::error('请求Ldap中email为空',[$_SERVER['REQUEST_URI']],'checkUserResign');
            return $status;
        }
        $cache_name = 'checkUserResign_'.$email;
        $data = \Framework\Cache::get($cache_name);
        //永不缓存
        $data = [];
        if(!$data){
            $user_name = explode('@',$email);
            $name = $user_name[0];
            $url = 'http://devops.chinawayltd.com/safe/users/isleaved';

            //请求ldap
            $client = new \GuzzleHttp\Client();
            try {
                \Framework\Log::error('请求Ldap接口参数',[$name],'checkUserResign');
                $req = $client->request('get',$url,['query'=>['username'=>$name]]);
                $resultData = $req->getBody()->getContents();
                $result = json_decode($resultData, true);
                if(isset($result['code']) && $result['code'] === 0){
                    $status = $result['data']['status'];
                }
                \Framework\Log::error('请求Ldap接口结果',[$result],'checkUserResign');
            }catch (\GuzzleHttp\Exception\ConnectException $e){
                // todo
                \Framework\Log::error('请求Ldap接口异常',[$e],'checkUserResign');
                \helper::throwException('请求LDAP失败，请重试', 403);
            }
            \Framework\Cache::put($cache_name,['status'=>$status],43200);
        }else{
            $status = $data['status'];
        }
        \Framework\Log::error('请求Ldap接口status',[$status],'checkUserResign');
        return $status;
    }

	/**
	 * 创建框架目录 admin/tmp/
	 * Story #43067
	 */
	public function mkFrameDir(){
		$tmp = $this->app->getTmpRoot();
		$cache = $this->app->getCacheRoot();
		$log = $cache . 'log' . DIRECTORY_SEPARATOR;
		$data = $this->app->getTmpRoot() . 'data' . DIRECTORY_SEPARATOR;
		$temp = $data . 'temp' . DIRECTORY_SEPARATOR;//图片临时目录
		if(!is_dir($tmp)){
			@mkdir($tmp,0777);
			@chmod($tmp, 0777);
		}
		if(!is_dir($cache)){
			@mkdir($cache,0777);
			@chmod($cache, 0777);
		}
		if(!is_dir($log)){
			@mkdir($log,0777);
			@chmod($log, 0777);
		}
		if(!is_dir($data)){
			@mkdir($data,0777);
			@chmod($data, 0777);
		}
		if(!is_dir($temp)){
			@mkdir($temp,0777);
			@chmod($temp, 0777);
		}
	}

	/**
	 * 检查当前用户是否有当前资源的权限
	 * <AUTHOR>
	 * @since 2013/12/19
	 * @param $reSource 一条moudle资源
	 * @return boolean 是否有权限：true=>有,false=>无
	 */
	public function checkSource($reSource,$hasModulePower){
		$outList = array(//默认没有权限，单不包括按钮和search
		array('m'=>'service_recover','f'=>'suitSearch'),
		array('m'=>'service_recover','f'=>'partSearch'),
		array('m'=>'storage_in','f'=>'suitSearch'),
		array('m'=>'storage_in','f'=>'partSearch'),
		);

		//当前用户拥有的权限
		$source = $this->app->myAdmin->source;
		//所有权限
		$sourceAll = $this->app->myAdmin->sourceAll;
		$methodName = $this->methodName;
		$moduleName = $this->moduleName;
		$publicFn = true;//请求的是否为公共方法
		$hasPower = false;
		foreach($sourceAll as $allV){
			if($allV->source_id == $reSource->source_id){
				foreach($allV->control as $conV){
					$_fn = explode('|',$conV->url);
					$fn = $_fn[4]?$_fn[4]:$_fn[0];//如果没有第五个参数，则取第一个（没有配置phpFn，则使用jsFn）
					if($fn == $methodName){
						$publicFn = false;
					}
				}
			}
		}
		if(!$publicFn){
			foreach($reSource->control as $reConV){
				$re_fn = explode('|',$reConV->url);
				$refn = $re_fn[4]?$re_fn[4]:$re_fn[0];//如果没有第五个参数，则取第一个（没有配置phpFn，则使用jsFn）
				if($refn == $methodName){//拥有当前控件的权限
					$hasPower = true;
				}
			}
		}else{
			if($hasModulePower){
				$hasPower = true;
			}else{
				if($methodName != 'search'){//请求的是公共方法
					$hasPower = true;
					foreach($outList as $lv){
						if($moduleName == $lv['m'] && $methodName == $lv['f']){
							$hasPower = false;
						}
					}
				}
			}
		}
		return $hasPower;
	}








	/**
	 * 把csv文件解析为一个数组返回
	 *
	 * @param string $file 要解析的csv文件路径
	 * @param char $delimiter csv文件里的内容分隔符 默认为;
	 * @return array
	 */
	public static function open($file, $delimiter = ';'){
		return self::ordenamultiarray(self::csvarray($file, $delimiter), 1);
	}
	private function csvarray($file, $delimiter)
	{
		$result = array();
		$size = filesize($file) + 1;
		$file = fopen($file, 'r');
		$keys = fgetcsv($file, $size, $delimiter);
		fseek($file,0);//这里原来的没有..自己加上..这样能读取到第一行的内容
		while ($row = fgetcsv($file, $size, $delimiter))
		{
			for($i = 0; $i < count($row); $i++)
			{
				if(array_key_exists($i, $keys))
				{
					$row[$keys[$i]] = $row[$i];
				}
			}
			$result[] = $row;
		}
		fclose($file);
		return $result;
	}
	private function ordenamultiarray($multiarray, $secondindex)
	{
		while (list($firstindex, ) = each($multiarray))
		$indexmap[$firstindex] = $multiarray[$firstindex][$secondindex];
		asort($indexmap);
		while (list($firstindex, ) = each($indexmap))
		if (is_numeric($firstindex))
		$sortedarray[] = $multiarray[$firstindex];
		else $sortedarray[$firstindex] = $multiarray[$firstindex];
		return $sortedarray;
	}

	/**
	 * 装换FormData成where条件
	 * <AUTHOR>
	 * //Story #48804
	 */
	public function getFormData($params = null, $_where = ''){
		$where = '';
		if ($params && $form_operator = json_decode($params['form_operator'])) {
            $form_operator = (array)$form_operator;
            unset($form_operator['trade_time_custom'],$form_operator['assign_time_custom']);
			foreach ($form_operator as $key => $val) {

				if (!isset($val->column)) continue;

				if (trim($params[$key]) !== '') {//Story #51423
					$value = trim($params[$key]);

					switch ($val->column_operator) {
						case 'like_left':
							$val->column_operator = 'LIKE';
							$value = "%" . $value;
							break;
						case 'like_right':
							$val->column_operator = 'LIKE';
							$value = $value . '%';
							break;
						case 'like':
						case 'like_left_right':
							$val->column_operator = 'LIKE';
							$value = '%' . $value . '%';
							break;
						case 'in':
						case 'IN':
							$value = $this->subReplace($value, '|');//字符串替换

							if (count(explode("','", $value)) === 1) {
								$val->column_operator = '=';
							} else {
								$val->column_operator = 'IN';
								$value = "('{$value}')";
							}
							break;
						case 'eq':
							$val->column_operator = '=';
							break;
						case 'ne':
							$val->column_operator = '!=';
							break;
						case 'ge':
							$val->column_operator = '>=';
							break;
						case 'gt':
							$val->column_operator = '>';
							break;
						case 'le':
							$val->column_operator = '<=';
							break;
						case 'lt':
							$val->column_operator = '<';
							break;
					}
					if ( isset($val->column_date_type) && $val->column_date_type) {
						if ($val->column_date_type == 'datefield') {
							$value = substr($value, 0, 10);
							if (strpos($key, 'start') !== false) {
								$value .= ' 00:00:00';
								!$val->column_operator ? $val->column_operator = '>=' : '';
							} else {
								$value .= ' 23:59:59';
								!$val->column_operator ? $val->column_operator = '<=' : '';
							}
						} else if ($val->column_date_type == 'datetimefield') {
							if (strpos($value, 'T')) {
								$value = str_replace('T', ' ', $value);
							} else {
								$value = substr($value, 0, 10) . ' ' . substr($value, -8);
							}

							if (strpos($key, 'start') !== false) {
								!$val->column_operator ? $val->column_operator = '>=' : '';
							} else {
								!$val->column_operator ? $val->column_operator = '<=' : '';
							}
						}
					}

					!$val->column_operator ? $val->column_operator = '=' : '';
					if ($val->column_operator != 'IN') {
						$value = "'{$value}'";
					}

					$where .= " AND {$val->column} {$val->column_operator} {$value} ";
				}
			}
		}
		if (empty($_where) && !empty($where)) {
			return ' 1 ' . $where;
		} else if (empty($_where) && empty($where)) {
			return ' 1 ';
		} else {
			return $_where . $where;
		}
	}

	/**
	 * 字符串替换
	 * //Story #48804
	 * @param $str  String 要过滤的值
	 * @param $exc  String 以什么符号分割，默认是 #
	 * @example F860628003#F860628002 替换成 F860628003','F860628002
	 * <AUTHOR>
	 * @since 20150923
	 * @return mixed
	 */
	public function subReplace($str,$exc = '#')
	{
		if ($str !== '' && !is_array($str)) {
			$string = '';
			$arr = explode($exc,$str);
			foreach ($arr as $val) {
				if ($val === '')	{
					continue;
				} else {
					$string .= $string !== '' ? "','" . trim($val) : trim($val);//Story #49755
				}
			}

			return $string;
		}
	}

    protected function getDateRange($month)
    {
        if (empty($month))
            return;

        if (strpos($month, ',') === false) {
            $s = $e = $month;
        } else {
            list($s, $e) = explode(',', $month);
        }

        $date = date('Y-m-d', strtotime($s)).','.date('Y-m-d', strtotime('-1 day', strtotime('+1 month', strtotime($e))));

        return $date;
    }
}
