<?php
/**
 * 支付系统配置,g7pay
 * Created by PhpStorm.
 * User: zlx66
 * Date: 2016/5/24/024
 * Time: 20:27
 */

return [
    /**
     * app key
     * test f0c0ea6f
     * demo 783a80b1
     * pro 8bf5b17c
     */
    'app_key'       => '783a80b1',

    /**
     * app secret 用于签名与验签
     * test 14eae772cca7428a9c287dac
     * demo 029e8107e6a24aeebc5e3c34
     * pro ce4adfedd9da41d691a29c95
     */
    'app_secret'    => '029e8107e6a24aeebc5e3c34',

    /**
     * 与g7pay交互地址
     * static protected $apiUrlDev = 'http://test.pay.g7s.chinawayltd.com/gateway/router/rest';
     * static protected $apiUrlTest = 'http://test.pay.g7s.chinawayltd.com/gateway/router/rest';
     * static protected $apiUrlDemo = 'https://demo.pay.g7s.chinawayltd.com/gateway/router/rest';
     * static protected $apiUrlPro = 'http://pay.huoyunren.com/gateway/router/rest';
     */
    'apiUrl'        => 'http://demo.pay.g7s.chinawayltd.com/gateway/router/rest',

    /**
     * 是否采用https访问
     */
    'isHttps'   =>  FALSE,

    /**
     * 支付通道
     */
    'channelId' =>  'chn_02',

    /**
     * 接口前缀
     */
    'requestPrefix' => '',

    /**
     * 接口交互错误时是否邮件报警
     */
    'errorAlarmEmail' => TRUE
];
