<?php

$app_key = '1132';
$app_secret = 'httx';
$apiUrl = 'http://172.16.1.143:8088/router/rest';

$clientEnv = \Models\OilConfigure::getBySysKey('client_env');
if ($clientEnv && $clientEnv == 'test') {
    $app_key = 'c157d6';
    $app_secret = '568797BE8EDED59630F8B51A5C186441';
    $apiUrl = 'http://172.16.1.144:8080/router/rest';
}elseif ($clientEnv && $clientEnv == 'pro') {
    $app_key = 'c157d6';
    $app_secret = '568797BE8EDED59630F8B51A5C186441';
    $apiUrl = 'http://172.16.1.144/router/rest';
}

$options = [
    /**
     * app key
     * test => '1088'
     * pro => '1132'
     */
    'app_key'    => $app_key,

    /**
     * app secret 用于签名与验签
     */
    'app_secret' => $app_secret,

    /**
     * 交互地址
     * test =>  http://172.16.1.143:8088/router/rest
     * test =>  http://dev.router.chinawayltd.com/router
     * pro  =>  http://121.41.49.121:8080/router/rest
     */
    'apiUrl'     => $apiUrl,

    'method'          => [
        "PARENTCARDINFO"   => "huoyunren.gascard.parentCardInfo",
        "ADDPARENTCARD"    => "huoyunren.gascard.addparentcard",
        "DELPCARD"         => "huoyunren.gascard.delpcard",
        "LISTCHARGEDETAIL" => "huoyunren.gascard.listChargeDetail",
        "ADDCRAWLERTIME"   => "huoyunren.gascard.addCrawlerTime",
        "STARTACCOUNT"     => "huoyunren.gascard.startAccount",
        "STOPACCOUNT"      => "huoyunren.gascard.stopAccount",
        "CHILDCARDINFO"    => "huoyunren.gascard.childCardInfo",
        "LISTRECORD"       => "huoyunren.gascard.listrecord",
    ],

    /**
     * 接口前缀
     */
    'requestPrefix'   => '',

    /**
     * 接口交互错误时是否邮件报警
     */
    'errorAlarmEmail' => TRUE,
];


define('APP_KEY', $options['app_key']); //1132
define('APP_SECRET', $options['app_secret']);

/*******************油品业务测试接口*************************/
define('OIL_API_URL', $options['apiUrl']);
//define('OIL_API_URL','http://121.41.49.121:8080/router/rest');//pro

//define("PARENTCARDINFO", "huoyunren.gascard.parentCardInfo", TRUE); //获取主卡信息
//define("ADDPARENTCARD", "huoyunren.gascard.addparentcard", TRUE); //添加主卡
//define("DELPCARD", "huoyunren.gascard.delpcard", TRUE); //删除主卡
//define("LISTCHARGEDETAIL", "huoyunren.gascard.listChargeDetail", TRUE); //获取充值明细
//define("ADDCRAWLERTIME", "huoyunren.gascard.addCrawlerTime", TRUE); //设置抓取时间
//define("STARTACCOUNT", "huoyunren.gascard.startTask", TRUE); //设置开始时间
//define("STOPACCOUNT", "huoyunren.gascard.stopTask", TRUE); //设置关闭时间
//define("CHILDCARDINFO", "huoyunren.gascard.childCardInfo", TRUE); //获取子卡信息
//define("LISTRECORD", "huoyunren.gascard.listrecord", TRUE);  //主卡充值记录同步

//钉钉接口地址
define('DINGTALK_KEY', '112445');
define('DINGTALK_SECRET', '8f5271');
define("DINGTALK_HOST",'http://sms.test.chinawayltd.com/message/dingtalk');

//gsp接口地址
define('GSPADMIN_URL', 'http://admin.gsp.cn/rest/');
//define('GSPADMIN_URL','http://gsp.huoyunren.com/rest/');
define('GSPADMIN_KEY','ips');
define('GSPADMIN_SECRET','********************************');

return $options;