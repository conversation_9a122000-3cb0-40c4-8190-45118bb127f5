<?php
/**
 * 油品交互系统 配置文件
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON>
 * Date: 2016/5/24/024
 * Time: 20:28
 */

$app_key = 'c157d6';
$app_secret = '568797BE8EDED59630F8B51A5C186441';
$apiUrl = 'http://test.router.chinawayltd.com/router';

$clientEnv = \Models\OilConfigure::getBySysKey('client_env');
if ($clientEnv && $clientEnv == 'test') {
    $app_key = 'c157d6';
    $app_secret = '568797BE8EDED59630F8B51A5C186441';
    $apiUrl = 'http://172.16.1.144:8080/router/rest';
}elseif ($clientEnv && $clientEnv == 'pro') {
    $app_key = 'c157d6';
    $app_secret = '568797BE8EDED59630F8B51A5C186441';
    $apiUrl = 'http://172.16.1.144/router/rest';
}

$apiUrl = 'http://gaspider-g7s.test.chinawayltd.com/router/rest';
return [
    /**
     * app key
     * dev/test:c157d6
     * pro:086f6a
     */
    'app_key'         => $app_key,

    /**
     * app secret 用于签名与验签
     * dev/test:568797BE8EDED59630F8B51A5C186441
     * pro:FE03E4F003A6474B4C68C65F9240842D
     */
    'app_secret'      => $app_secret,

    /**
     * 交互地址
     * dev  =>  http://dev.gas.chinawayltd.com/rest/
     * test =>  http://test.router.chinawayltd.com/router
     * demo =>  http://test.gas.chinawayltd.com/rest/
     * pro  =>  http://deneb.chinawayltd.com/router
     */
    'apiUrl'          => $apiUrl,

    /**
     * 是否采用https请求
     */
    'isHttps'         => FALSE,

    /**
     * 接口前缀
     */
    'requestPrefix'   => '',

    /**
     * 接口交互错误时是否邮件报警
     */
    'errorAlarmEmail' => TRUE,

];