<?php

$apiConfig = [
    //用户中心
    'UCENTER'         => [
        'apiurl'             => 'http://test.api.g7s.chinawayltd.com/interface/index.php',//'http://g7s.api.huoyunren.com/interface/index.php',//接口地址
        'app_key'            => 'admin_api',//app_key
        'app_secret'         => '6b9265fe91aa1bade52ea739a095cae3',//'2efa04b71555acaa4a09544a17b0b5ae','2efa04b71555acaa4a09544a17b0b5ae',//接口密钥
        'appkey_name'        => 'app_key',//app_key的key名
        'createSignFunction' => 'createSignUcenter',//生成签名的方法名
    ],

    //用户中心-查找机构
    'UCENTER_FINDORG' => [
        'apiurl'             => 'http://test.api.g7s.chinawayltd.com/interface/index.php',//接口地址
        'app_key'            => 'admin_api',
        'app_secret'         => '6b9265fe91aa1bade52ea739a095cae3',//'2efa04b71555acaa4a09544a17b0b5ae',
        'appkey_name'        => 'app_key',//app_key的key名
        'createSignFunction' => 'createSignUcenter',//生成签名的方法名
    ],

    //微众银行
    'WEBANK'          => [
        'apiurl'             => 'http://test.pay.g7s.chinawayltd.com/router/rest',
        'app_key'            => 'admin',
        'app_secret'         => 'admin',
        'appkey_name'        => 'appkey',
        'createSignFunction' => 'createSignWebank',
    ],
    //短信接口
    'MSG'             => [
        'apiurl'             => 'http://test.smsservice.chinawayltd.com/router/rest',   //测试接口
        'app_key'            => '1088',
        'app_secret'         => 'httx',
        //	'apiurl'             => 'http://smsservice.huoyunren.com/router/rest',
        //	'app_key'            => '1124',
        //	'app_secret'         => 'gsp#2826',
        'appkey_name'        => 'app_key',
        'createSignFunction' => 'createSign'
    ],
];
define("UCENTERCREATEORGAN", "ucenter.org.createOrgan", TRUE); //createOrgan 机构创建
define("UCENTERGETRESOURCETREE", "ucenter.tree.getResourceTree", TRUE); //getResourceTree 获取G7S权限资源树
define("UCENTERCREATEROLE", "ucenter.role.createRole", TRUE); //createRole  创建角色
define("UCENTERCREATEUSER", "ucenter.user.createUser", TRUE); //createRole  创建角色
define("EPAYWEBANKSYNCUSTOMER", "huoyunren.epay.webank.syn.customer", TRUE);// 客户信息同步
define("EPAYWEOPENAPPSERVICE", "truckmanager.appaccount.openService", TRUE);//openService 开通APP账户
define("LILNKVEHICLE", "huoyunren.epay.webank.syn.vehicle", TRUE); //车辆档案信息同步
define("LILNKDRIVER", "huoyunren.epay.webank.syn.driver", TRUE);  //司机信息同步
define("EPAYDICT", "huoyunren.epay.dict", TRUE);
define("ORGFINDORGANS", "ucenter.org.findOrgans", TRUE);// g7s-机构名称查询
define("ORGFINDOTREE", "ucenter.org.findOrgans", TRUE);// g7s-机构名称查询
define("SMSSENDER", "huoyunren.sms.sender", TRUE); //smssender 发短信操作
define("SMSINBOX", "huoyunren.sms.inbox", TRUE); //smsinbox 收件箱
define("SMSSENDED", "huoyunren.sms.sended", TRUE); //smssended 发件箱

return $apiConfig;
