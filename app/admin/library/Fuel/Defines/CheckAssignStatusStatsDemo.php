<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;


class CheckAssignStatusStatsDemo
{
    static public $list= [
        '1' => [
            'total' => 0,
            'data'  => [
                [
                    'oil_com'      => 1,
                    'check_assign' => '0',
                    'total'        => 0
                ],
                [
                    'oil_com'      => 1,
                    'check_assign' => '5',
                    'total'        => 0
                ],
                [
                    'oil_com'      => 1,
                    'check_assign' => '-10',
                    'total'        => 0
                ],
                [
                    'oil_com'      => 1,
                    'check_assign' => '10',
                    'total'        => 0
                ]
            ]
        ],
        '2' => [
            'total' => 0,
            'data'  => [
                [
                    'oil_com'      => 2,
                    'check_assign' => '0',
                    'total'        => 0
                ],
                [
                    'oil_com'      => 2,
                    'check_assign' => '5',
                    'total'        => 0
                ],
                [
                    'oil_com'      => 2,
                    'check_assign' => '-10',
                    'total'        => 0
                ],
                [
                    'oil_com'      => 2,
                    'check_assign' => '10',
                    'total'        => 0
                ]
            ]
        ]
    ];

    static public function getById($id)
    {
        return isset(self::$list[$id]) ? self::$list[$id] : null;
    }

    static public function getAll()
    {
        return self::$list;
    }
}