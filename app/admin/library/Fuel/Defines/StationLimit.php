<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;


class StationLimit
{
    const POCDE_TYPE    = 2;
    const ORGCODE_TYPE    = 4;
    const STATION_ID_TYPE    = 3;

    static public $list = [
        self::POCDE_TYPE    => 'pcode',
        self::ORGCODE_TYPE    => 'orgcode',
        self::STATION_ID_TYPE    => 'station_id',
    ];

    static public function getById($id)
    {
        return isset(self::$list[$id]) ? self::$list[$id] : NULL;
    }

    static public function getAll()
    {
        return self::$list;
    }

    //壳牌出示付款码及老吕跳转url
    static public function payMentPcodeList()
    {
        return [
            "2000LTCS",
            "2000K2TC",
            "2000L2VM",//延长壳牌 online
            '200044CD',//广东壳牌 线上
            '20003VMM',//陕西壳牌 test
            '2000JBU5',//陕西壳牌 online
            '2000VSVCD4TMLV',//福建壳牌 test
            '20008QONJDCS', //福建壳牌 online
            '20008QONTAUTLC', //线上港森
            '2000DU2U', //测试港森
            //'20008QON4UCTUA', //线上星油能
            //'200044SJ', //测试星油能源
            '2000CVJJ',//老吕线上
            '2000KTCD',//老吕测试
            '2000TASU',//老吕气站测试
            '2000JM2M',//老吕气站线上
            '2000MSA5',//万金油--宝兑通渠道测试环境
            '2000LBSV',//万金油--宝兑通渠道线上环境
        ];
    }

}