<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;


class AssignType
{
    const AUTO_ASSIGN     = 10;//自动分配
    const MANUAL_ASSIGN      = 20;//手工分配
    const GAS_ASSIGN      = 30;//撬装分配
    const MANUAL_DEDUCTION_ASSIGN      = 21;//截流手动分配

    static public $list = [
        self::AUTO_ASSIGN   =>  '自动',
        self::MANUAL_ASSIGN =>  '手动',
        self::GAS_ASSIGN    =>  '撬装卡',
        self::MANUAL_DEDUCTION_ASSIGN    =>  '截流'
    ];

    static public function getById($key)
    {
        return isset(self::$list[$key]) ? self::$list[$key] : '--';
    }

    static public function getAutoAssign()
    {
        return OilCom::getAutoAssignType();
    }

    static public function getManualAssign()
    {
        return OilCom::getManualType();
    }

    static public function getGasAssignType()
    {
        return OilCom::getGasType();
    }

}