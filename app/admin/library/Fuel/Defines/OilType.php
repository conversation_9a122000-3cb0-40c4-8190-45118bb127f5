<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;


class OilType
{
    const QI_YOU = 1;
    const CHAI_YOU = 2;
    const FUEL_YOU = 12;
    const GAS_YOU = 5;
    const UREA_YOU = 6;
    const NOT_OIL  = 3; // 非油产品
    const REPAIR = 7; //维修保养
    const LUBE_OIL  = 8; //润滑油类
    const IN_CHAI_YOU  = 14; //柴油
    const IN_QI_YOU  = 11; //汽油
    const IN_J_QI_YOU  = 15; //甲醇汽油
    const IN_Y_QI_YOU  = 13; //乙醇汽油
    const IN_GAS_YOU  = 21; //天然气

    static public $list = [
        self::QI_YOU   => '汽油',
        self::CHAI_YOU => '柴油',
        self::FUEL_YOU => '燃油',
        self::GAS_YOU => '天然气',
        self::UREA_YOU => "尿素",
        self::REPAIR => '维修保养',
        self::LUBE_OIL => '润滑油类',
    ];

    static public $oil_type = [
        self::QI_YOU   => '汽油',
        self::CHAI_YOU => '柴油',
        self::FUEL_YOU => '燃油',
        self::GAS_YOU => '天然气',
        self::UREA_YOU => '尿素',
        self::NOT_OIL => '非油产品',
        self::REPAIR => '维修保养',
        self::LUBE_OIL => '润滑油类',

        self::IN_CHAI_YOU => '柴油',
        self::IN_QI_YOU => '汽油',
        self::IN_J_QI_YOU => '甲醇汽油',
        self::IN_Y_QI_YOU => '乙醇汽油',
        self::IN_GAS_YOU => '天然气类',
    ];

    static public $in_oil_type = [
        self::IN_CHAI_YOU => '柴油',
        self::IN_QI_YOU => '汽油',
        self::IN_J_QI_YOU => '甲醇汽油',
        12 => '甲醇汽油',
        self::IN_Y_QI_YOU => '乙醇汽油',
        self::IN_GAS_YOU => '天然气类',
    ];

    static public function getById($id)
    {
        return isset(self::$list[$id]) ? self::$list[$id] : NULL;
    }

    static public function getByInId($id)
    {
        return isset(self::$in_oil_type[$id]) ? self::$in_oil_type[$id] : NULL;
    }

    static public function getAll()
    {
        return self::$list;
    }

    /**
     * get bind_status_id by bind name
     * @param $name
     * @return null
     */
    static public function getByIdByName($name)
    {
        $_list = array_flip(self::$list);

        return isset($_list[$name]) ? $_list[$name] : NULL;
    }

    static public function getReceiptList()
    {
        unset(self::$list[1]);
        unset(self::$list[2]);
        unset(self::$list[7]);
        unset(self::$list[8]);
        unset(self::$list[6]);
        return self::$list;
    }

    //=============油品二级分类配置================
    const OIL_SEC_TYPE_QI       = '101';
    const OIL_SEC_TYPE_QI_YC    = '102';
    const OIL_SEC_TYPE_QI_JC    = '103';
    const OIL_SEC_TYPE_CHAI     = '201';
    const OIL_SEC_TYPE_NOT_OIL  = '301';
    const OIL_SEC_TYPE_GAS_YH   = '501';
    const OIL_SEC_TYPE_GAS_YS   = '502';
    const OIL_SEC_TYPE_UREA_YOU = '601';
    const OIL_REPAIR            = '701';
    const OIL_LUBE              = '801';

    /**
     * 油品二级分类对应的税收编码及计量单位
     */
    public static $oil_sec_type = [
        self::OIL_SEC_TYPE_QI     => [
            'unit'   => ['升'],
            'tax_no' => '1070101010100000000',
            'title'  => '汽油',
            'key'    => self::OIL_SEC_TYPE_QI,
            'parent_oil_type' => self::QI_YOU,
            'second_oil_type_id' => OilTypeBase::SEC_GASOLINE,
        ],
        self::OIL_SEC_TYPE_QI_YC  => [
            'unit'   => ['升'],
            'tax_no' => '1070101010300000000',
            'title'  => '乙醇汽油',
            'key'    => self::OIL_SEC_TYPE_QI_YC,
            'parent_oil_type' => self::QI_YOU,
            'second_oil_type_id' => OilTypeBase::SEC_ETHANOL_GASOLINE
        ],
        self::OIL_SEC_TYPE_QI_JC  => [
            'unit'   => ['升'],
            'tax_no' => '1070101010200000000',
            'title'  => '甲醇汽油',
            'key'    => self::OIL_SEC_TYPE_QI_JC,
            'parent_oil_type' => self::QI_YOU,
            'second_oil_type_id' => OilTypeBase::SEC_METHANOL_GASOLINE
        ],
        self::OIL_SEC_TYPE_CHAI   => [
            'unit'   => ['升'],
            'tax_no' => '1070101030100000000',
            'title'  => '柴油',
            'key'    => self::OIL_SEC_TYPE_CHAI,
            'parent_oil_type' => self::CHAI_YOU,
            'second_oil_type_id' => OilTypeBase::SEC_DIESEL
        ],
        self::OIL_SEC_TYPE_GAS_YH => [
            'unit'   => ['千克','立方米（方）'],
            'tax_no' => '1020202000000000000',
            'title'  => '液化天然气',
            'key'    => self::OIL_SEC_TYPE_GAS_YH,
            'parent_oil_type' => self::GAS_YOU,
            'second_oil_type_id' => OilTypeBase::SEC_LIQUEFIED_NATURAL_GAS
        ],
        self::OIL_SEC_TYPE_GAS_YS => [
            'unit'   => ['千克','立方米（方）'],
            'tax_no' => '1020202000000000000',
            'title'  => '压缩天然气',
            'key'    => self::OIL_SEC_TYPE_GAS_YS,
            'parent_oil_type' => self::GAS_YOU,
            'second_oil_type_id' => OilTypeBase::SEC_NATURAL_GAS
        ],
        self::OIL_SEC_TYPE_UREA_YOU => [
            'unit'   => ['无'],
            'tax_no' => '',
            'title'  => '尿素',
            'key'    => self::OIL_SEC_TYPE_UREA_YOU,
            'parent_oil_type' => self::UREA_YOU
        ],
        self::OIL_SEC_TYPE_NOT_OIL => [
            'unit'   => ['无'],
            'tax_no' => '',
            'title'  => '非油产品',
            'key'    => self::OIL_SEC_TYPE_NOT_OIL,
            'parent_oil_type' => self::NOT_OIL
        ],
        self::OIL_REPAIR => [
            'unit'   => ['无'],
            'tax_no' => '',
            'title'  => '维修保养',
            'key'    => self::OIL_REPAIR,
            'parent_oil_type' => self::REPAIR
        ],
        self::OIL_LUBE => [
            'unit'   => ['无'],
            'tax_no' => '',
            'title'  => '润滑油类',
            'key'    => self::OIL_LUBE,
            'parent_oil_type' => self::LUBE_OIL
        ],
    ];

    const PRODUCT_NAME_CAR = 1;
    const PRODUCT_NAME_NOT_OIL = 2;
    public static $product_name = [
        self::PRODUCT_NAME_CAR => '车用油',
        self::PRODUCT_NAME_NOT_OIL => '非油产品'
    ];

    const DATA_SOURCE_RECORD = 10;
    const DATA_SOURCE_SYS = 20;
    public static $data_source = [
        self::DATA_SOURCE_RECORD => '消费记录',
        self::DATA_SOURCE_SYS => '模板导入'
    ];

    public static $rebate_oil_type = [
        self::QI_YOU,self::CHAI_YOU,self::GAS_YOU
    ];

    /****油品库存分类***/
    const STOCK_CLASSIFY_INTO = 10;
    const STOCK_CLASSIFY_OUT = 20;

    public static $stock_classify = [
        self::STOCK_CLASSIFY_INTO => '进项票',
        self::STOCK_CLASSIFY_OUT => '销项票',
    ];

    static public function transformValueToId($v)
    {
        switch ($v) {
            case '升':
                return Unit::getLId();
            case 'Kg':
            case '千克':
                return Unit::getKgId();
        }
    }

    static public $upstreamRebateOil = [
        self::QI_YOU   => '汽油',
        self::CHAI_YOU => '柴油',
        self::GAS_YOU => '天然气',
        self::UREA_YOU => "尿素",
    ];

    /**
     * 油品单位转换
     * @var array
     */
    public static $oilToUnit = [
        self::QI_YOU => '升',
        self::CHAI_YOU => '升',
        self::UREA_YOU => '升',
        self::GAS_YOU => '千克',
    ];

    /*
     * 内部票开票油品类型
     */
    public static $internalOilTypeMap = [
        self::IN_CHAI_YOU => '柴油',
        self::IN_QI_YOU => '汽油',
        self::IN_J_QI_YOU => '甲醇汽油',
        self::IN_Y_QI_YOU => '乙醇汽油',
        self::IN_GAS_YOU => '天然气类',
    ];

    /*
     * 油品类型对开灵发票种类映射表
     */
    static public $oil_type_to_specific_factor = [
        self::QI_YOU      => 1,
        self::CHAI_YOU    => 1,
        self::FUEL_YOU    => 1,
        self::GAS_YOU     => 0,
        self::UREA_YOU    => 0,
        self::NOT_OIL     => 0,
        self::REPAIR      => 0,
        self::LUBE_OIL    => 0,
        self::IN_CHAI_YOU => 1,
        self::IN_QI_YOU   => 1,
        self::IN_J_QI_YOU => 1,
        self::IN_Y_QI_YOU => 1,
        self::IN_GAS_YOU  => 0,
    ];



    const OIL_IMPORT_TYPE_CHAI       = '柴油';
    const OIL_IMPORT_TYPE_QI    = '汽油';
    const OIL_IMPORT_TYPE_QI_YC    = '乙醇汽油';
    const OIL_IMPORT_TYPE_QI_JC     = '甲醇汽油';
    const OIL_IMPORT_TYPE_LUBE  = '润滑油';
    const OIL_IMPORT_TYPE_GAS = '天然气';


    /**
     * 发票导入映射 进项
     * 1020203000000000000    *液化天然气*液化天然气(LNG)
     * 1070101110100000000    *液化气*液化天然气(LNG)
     * 1100201990000000000    *燃气*液化天然气
     * 1100202030000000000    *燃气*液化天然气（LNG)
     */

    public static $taxNoOilName = [
        '1070101030100000000' => self::OIL_IMPORT_TYPE_CHAI,
        '1070101010100000000' => self::OIL_IMPORT_TYPE_QI,
        '1070101010300000000' => self::OIL_IMPORT_TYPE_QI_YC,
        '1070101010200000000' => self::OIL_IMPORT_TYPE_QI_JC,
        '1070101070100000000' => self::OIL_IMPORT_TYPE_LUBE,
//         '1100202030000000000' => self::OIL_IMPORT_TYPE_GAS,
//         '1100201990000000000' => self::OIL_IMPORT_TYPE_GAS,
//         '1070101110100000000' => self::OIL_IMPORT_TYPE_GAS,
//         '1020203000000000000' => self::OIL_IMPORT_TYPE_GAS,
    ];

    public static $oil_sec_type_for_import_input = [
        self::OIL_IMPORT_TYPE_CHAI     => [
            'title'  => '柴油',
            'unit'   => ['吨'],
            'tax_no' => '1070101030100000000',
            // 税务局系数
            'tax_factor' => [1],
            // 统计局系数
            'statistics_factor' => [ 1176, 0.00086],
        ],
        self::OIL_IMPORT_TYPE_QI  => [
            'title'  => '汽油',
            'unit'   => ['吨'],
            'tax_no' => '1070101010100000000',
            // 税务局系数
            'tax_factor' => 1,
            // 统计局系数
            'statistics_factor' => [1388, 0.00073],
        ],
        self::OIL_IMPORT_TYPE_QI_YC  => [
            'title'  => '乙醇汽油',
            'unit'   => ['吨'],
            'tax_no' => '1070101010300000000',
            // 税务局系数
            'tax_factor' => 1,
            // 统计局系数
            'statistics_factor' => [1388, 0.00073],
        ],
        self::OIL_IMPORT_TYPE_QI_JC  => [
            'title'  => '甲醇汽油',
            'unit'   => ['吨'],
            'tax_no' => '1070101010200000000',
            // 税务局系数
            'tax_factor' => 1,
            // 统计局系数
            'statistics_factor' => [1388, 0.00073],
        ],
        self::OIL_IMPORT_TYPE_LUBE  => [
            'title'  => '润滑油',
            'unit'   => ['吨'],
            'tax_no' => '1070101070100000000',
            // 税务局系数
            'tax_factor' => 1,
            // 统计局系数
            'statistics_factor' => [1],
        ],
        self::OIL_IMPORT_TYPE_GAS  => [
            'title'  => '天然气',
            'unit'   => ['公斤','千克','KG','kg','升','L','立方米','m³','立方','m3',],
            'tax_no' => '1100202030000000000',
            // 税务局系数
            'tax_factor' => [
                '公斤' => 0.001,
                '千克' => 0.001,
                'KG'  => 0.001,
                'kg'  => 0.001,
                '升'  => 1/1380000,
                'L'   => 1/1380000,
                '立方米' => 1/1380,
                '立方' => 1/1380,
                'm3' => 1/1380,
                'm³' => 1/1380,
            ],
            // 统计局系数
            'statistics_factor' => [
                '公斤' => 0.001,
                '千克' => 0.001,
                'KG'  => 0.001,
                'kg'  => 0.001,
                '升'  => 1/1380000,
                'L'   => 1/1380000,
                '立方米' => 1/1380,
                '立方' => 1/1380,
                'm3' => 1/1380,
                'm³' => 1/1380,
            ],
        ],
    ];
    
    public static $oil_sec_type_for_import_input_new = [
        self::OIL_IMPORT_TYPE_CHAI     => [
            'title'  => '柴油',
            'unit'   => ['吨','L','升'],
            'tax_no' => '1070101030100000000',
            // 税务局系数
            'tax_factor' => ['吨'=>1,'L'=>1/1176,'升'=>1/1176],
            // 统计局系数
            'statistics_factor' => ['吨'=>1,'L'=>'0.00086','升'=>'0.00086'],
        ],
        self::OIL_IMPORT_TYPE_QI  => [
            'title'  => '汽油',
            'unit'   => ['吨','L','升'],
            'tax_no' => '1070101010100000000',
            // 税务局系数
            'tax_factor' => ['吨'=>1,'L'=>1/1388,'升'=>1/1388],
            // 统计局系数
            'statistics_factor' => ['吨'=>1,'L'=>'0.00073','升'=>'0.00073'],
        ],
        self::OIL_IMPORT_TYPE_QI_YC  => [
            'title'  => '乙醇汽油',
            'unit'   => ['吨','L','升'],
            'tax_no' => '1070101010300000000',
            // 税务局系数
            'tax_factor' => ['吨'=>1,'L'=>1/1388,'升'=>1/1388],
            // 统计局系数
            'statistics_factor' => ['吨'=>1,'L'=>'0.00073','升'=>'0.00073'],
        ],
        self::OIL_IMPORT_TYPE_QI_JC  => [
            'title'  => '甲醇汽油',
            'unit'   => ['吨','L','升'],
            'tax_no' => '1070101010200000000',
            // 税务局系数
            'tax_factor' => ['吨'=>1,'L'=>1/1388,'升'=>1/1388],
            // 统计局系数
            'statistics_factor' => ['吨'=>1,'L'=>'0.00073','升'=>'0.00073'],
        ],
        self::OIL_IMPORT_TYPE_LUBE  => [
            'title'  => '润滑油',
            'unit'   => ['公斤','KG','升','kg','千克'],
            'tax_no' => '1070101070100000000',
            // 税务局系数
            'tax_factor' => ['升'=>1/1126,'公斤'=>'0.001','千克'=>'0.001','KG'=>'0.001','kg'=>'0.001',],
            // 统计局系数
            'statistics_factor' => ['升'=>1/1126,'公斤'=>'0.001','千克'=>'0.001','KG'=>'0.001','kg'=>'0.001',],
        ],
  
    ];

    public static $oil_sec_type_for_import_output = [
        self::OIL_IMPORT_TYPE_CHAI     => [
            'title'  => '柴油',
            'unit'   => ['公斤','千克','KG','kg','升','L'],
            'tax_no' => '1070101030100000000',
            // 税务局系数
            'tax_factor' => [
                '公斤' => 0.001,
                '千克' => 0.001,
                'KG'  => 0.001,
                'kg'  => 0.001,
                '升'  => 1/1176,
                'L'   => 1/1176
            ],
            // 统计局系数
            'statistics_factor' => [
                '公斤' => 0.001,
                '千克' => 0.001,
                'KG'  => 0.001,
                'kg'  => 0.001,
                '升'  => 0.00086,
                'L'   => 0.00086
            ],
        ],
        self::OIL_IMPORT_TYPE_QI  => [
            'title'  => '汽油',
            'unit'   => ['公斤','千克','KG','kg','升','L'],
            'tax_no' => '1070101010100000000',
            // 税务局系数
            'tax_factor' => [
                '公斤' => 0.001,
                '千克' => 0.001,
                'KG'  => 0.001,
                'kg'  => 0.001,
                '升'  => 1/1388,
                'L'   => 1/1388
            ],
            // 统计局系数
            'statistics_factor' => [
                '公斤' => 0.001,
                '千克' => 0.001,
                'KG'  => 0.001,
                'kg'  => 0.001,
                '升'  => 0.00073,
                'L'   => 0.00073
            ],
        ],
        self::OIL_IMPORT_TYPE_QI_YC  => [
            'title'  => '乙醇汽油',
            'unit'   => ['公斤','千克','KG','kg','升','L'],
            'tax_no' => '1070101010300000000',
            // 税务局系数
            'tax_factor' => [
                '公斤' => 0.001,
                '千克' => 0.001,
                'KG'  => 0.001,
                'kg'  => 0.001,
                '升'  => 1/1388,
                'L'   => 1/1388
            ],
            // 统计局系数
            'statistics_factor' => [
                '公斤' => 0.001,
                '千克' => 0.001,
                'KG'  => 0.001,
                'kg'  => 0.001,
                '升'  => 0.00073,
                'L'   => 0.00073
            ],
        ],
        self::OIL_IMPORT_TYPE_QI_JC  => [
            'title'  => '甲醇汽油',
            'unit'   => ['公斤','千克','KG','kg','升','L'],
            'tax_no' => '1070101010200000000',
            // 税务局系数
            'tax_factor' => [
                '公斤' => 0.001,
                '千克' => 0.001,
                'KG'  => 0.001,
                'kg'  => 0.001,
                '升'  => 1/1388,
                'L'   => 1/1388
            ],
            // 统计局系数
            'statistics_factor' =>  [
                '公斤' => 0.001,
                '千克' => 0.001,
                'KG'  => 0.001,
                'kg'  => 0.001,
                '升'  => 0.00073,
                'L'   => 0.00073
            ],
        ],
        self::OIL_IMPORT_TYPE_LUBE  => [
            'title'  => '润滑油',
            'unit'   => ['公斤','千克','KG','kg','升','L'],
            'tax_no' => '1070101070100000000',
            // 税务局系数
            'tax_factor' => [
                '公斤' => 0.001,
                '千克' => 0.001,
                'KG'  => 0.001,
                'kg'  => 0.001,
                '升'  => 1/1126,
                'L'   => 1/1126
            ],
            // 统计局系数
            'statistics_factor' => [
                '公斤' => 0.001,
                '千克' => 0.001,
                'KG'  => 0.001,
                'kg'  => 0.001,
                '升'  => 1/1126,
                'L'   => 1/1126
            ],
        ],
        self::OIL_IMPORT_TYPE_GAS  => [
            // todo
            'title'  => '天然气',
            'unit'   => ['公斤','千克','KG','kg','升','L','立方米','m³','立方','m3',],
            'tax_no' => '1100202030000000000',
            // 税务局系数
            'tax_factor' => [
                '公斤' => 0.001,
                '千克' => 0.001,
                'KG'  => 0.001,
                'kg'  => 0.001,
                '升'  => 1/1380000,
                'L'   => 1/1380000,
                '立方米' => 1/1380,
                '立方' => 1/1380,
                'm3' => 1/1380,
                'm³' => 1/1380,
            ],
            // 统计局系数
            'statistics_factor' => [
                '公斤' => 0.001,
                '千克' => 0.001,
                'KG'  => 0.001,
                'kg'  => 0.001,
                '升'  => 1/1380000,
                'L'   => 1/1380000,
                '立方米' => 1/1380,
                '立方' => 1/1380,
                'm3' => 1/1380,
                'm³' => 1/1380,
            ],
        ],
    ];
}