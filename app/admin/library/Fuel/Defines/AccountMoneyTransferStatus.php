<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;


class AccountMoneyTransferStatus
{
    static public $statusList = [
        '0' => ['id' => '-1', 'status' => '未审核'],
        '-1' => ['id' => '-1', 'status' => '已驳回'],
        '1'  => ['id' => '1', 'status' => '已审核'],
        '-2' => ['id' => '-2', 'status' => '已删除']
    ];

    static public $payStatusList = [
        '-1' => ['id' => '-1', 'status' => '未支付'],
        '0' => ['id' => '0', 'status' => '成功'],
        '1'  => ['id' => '1', 'status' => '失败'],
        '2' => ['id' => '2', 'status' => '处理中']
    ];

    static public function getById($id)
    {
        return isset(self::$statusList[$id]) ? self::$statusList[$id] : NULL;
    }

    static public function getPayStatusById($id)
    {
        return isset(self::$payStatusList[$id]) ? self::$payStatusList[$id] : NULL;
    }

    static public function getAll()
    {
        return self::$statusList;
    }

    static public function getDefinedCompanyId()
    {
        if(in_array(API_ENV, ['pro', 'prod'])){
            return [3060];
        }else{
            return [1595];
        }
    }

    static public function getCompanyOrgMap()
    {
        if(in_array(API_ENV, ['pro', 'prod'])){
            return [
                7145 => '205C8M01',
            ];
        }else{ 
            return [
                1334 => '20013Z01', 
            ];
        }
    }
    static public function getTransferOrgMap()
    {
        if(in_array(API_ENV, ['pro', 'prod'])){
            return [
                '205C8M01' => [
                    '201XW32NKF'=>'205C8M0108',
                    '201XW32NN3'=>'205C8M0107',
                    '201XW32P5S'=>'205C8M0106',
                    '201XW32P5R'=>'205C8M0105',
                    '201XW32P5Q'=>'205C8M0104',
                    '201XW32P5P'=>'205C8M0103',
                    '201XW32P5O'=>'205C8M0102',
                    '201XW32P5N'=>'205C8M0101',
                    '201XW32P06'=>'205C8M0109',
                    '201XW32P13'=>'205C8M010A',
                    '201XW32P12'=>'205C8M010B',
                    '201XW32P4T'=>'205C8M010C',
                    '201XW32NQK'=>'205C8M010D',
                    '201XW315P4'=>'205C8M010E',
                    '201XW32O2R'=>'205C8M010F',
                    '201XW31G01'=>'205C8M010G',
                    '201XW32P8P'=>'205C8M010H',
                    '201XW32P60'=>'205C8M010I',
                    '201XW32MJF'=>'205C8M010J',
                    '201XW32LOZ'=>'205C8M010K',
                    '201XW32PHT'=>'205C8M010L',
                ],
            ];
        }else{
            return [
               '20013Z01' =>  [
                    '201L9K0106' => '20013Z0101',
                    '201L9K0104' => '20013Z0102',
               ],
            ];
        }
    }
}