<?php
/**
 * 消费记录是否已开票
 * User: <PERSON>
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;

class IsOpenInvoiceForTrades
{
    //已开票
    const YES = '10';
    //部分开票
    const PART = '20';
    //未开票
    const NO = '30';

    static public $status = [
        self::YES          => '已开票',
        self::PART           => '部分开票',
        self::NO           => '未开票',
    ];

    static public function getById($id)
    {
        return isset(self::$status[ $id ]) ? self::$status[ $id ] : NULL;
    }

    static public function getAll()
    {
        return self::$status;
    }
}