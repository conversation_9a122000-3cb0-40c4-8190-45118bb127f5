<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;


class OilGasAssignAppStatus
{
    static public $list = [
        '-1'    =>  '已驳回',
        '0'    =>  '待审核',
        '1'    =>  '已审核'
    ];

    static public function getById($id)
    {
        return isset(self::$list[$id]) ? self::$list[$id] : NULL;
    }

    static public function getAll()
    {
        return self::$list;
    }
}