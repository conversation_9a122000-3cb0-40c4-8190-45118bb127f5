<?php

namespace Fuel\Defines;

/**
 * Created by PhpStorm.
 * User: tim
 * Date: 19-7-25
 * Time: 上午11:32
 */

use Framework\Cache;
use Framework\Log;
use Models\OilCardViceTrades;

class ReceiptConfig
{
    public $priceMap = [];

    /**
     * 新模型下黑名单（废弃，用白名单控制）
     *
     * @var array
     */
    public static $orgWhiteList = [
        '200133', '200B0M', '20007I', '2000BG', '200575', '2000T0', '2008JV', '2008GA', '200826', '200D5I', '200MP2',
        '200PM1', '200P9I', '200OPF', '200K82', '200IZE', '200WF3', '2003TG', '2007RL', '2014FA', '2010PK', '20141D',
        '2015LG', '200YI9', '2016Z3', '2015ML', '200V7L', '201AGC', '2015DK', '201AG9', '201AGB', '201BHP', '201AO6',
        '200SBM', '201CZ7', '201FET', '201E6U', '20007J', '201FH7', '200D8X', '201FXC', '201F2T', '201C4G', '200R0Z',
        '200RKF', '2015PB', '200ZP6', '201H1S', '200QJE', '200XMN', '200Z4G', '200R80', '200URT', '201HZU', '2014GB',
        '201IWD', '201HEE', '2004X1', '20170X', '200UH3', '201IZ5', '201BRQ', '201J6X', '200ZX0', '200XTJ', '2017GJ',
        '200MWY', '200TWE', '2015U3', '2017GT', '200V3A', '2010KR', '201GGX', '200V3E', '200JD2', '200QB8', '200FWF',
        '2019DR', '2014GI', '201BEO', '2013Q7', '201HP2', '200X1T', '200IF3', '200S2E', '200YF1', '2013MH', '201KZV',
        '201KUQ', '201KFH', '200IQY', '201GXI', '200I7I', '2006KF', '200PZE', '2015T0', '201K4V', '201JZK', '2017ZX',
        '201LB8', '201KJQ', '201M66', '201LUP', '201JZS', '200U2X', '200TA3', '201JMM', '201LY1', '201JYB', '201BOO',
        '200BNE', '2016F6', '20115S', '201ICP', '201ICN', '201ICO', '201O0J', '201LP2', '2018QN', '201HDR', '201OOJ',
        '201P5B', '201OFQ', '201LDR', '201OQL', '201DM7', '201PVB', '201QIO', '201MAO', '201NVC', '200zxe', '200NCQ',
        '201P5A', '201MKB', '201N7Y', '201FLP', '201K9F', '200QWK', '201NVH', '200LS1', '2008EM', '200JHK', '201QSR',
        '201NVN', '201R9K', '201TRR', '201UHB', '201QXC', '201NV7', '201MKF', '201S2H', '201UUV', '201VQI', '201VSQ',
        '201VBP', '201WPD	', '201WD2', '201WBQ', '201T9S', '200KX2', '201Z0I', '201Z3S', '200SHC', '201ZVO', '201LXN',
        '201GH2', '201HZO', '200PTR', '2020S1', '20218A', '202160', '2021MS', '20239M', '2023PP', '201YNU', '200VTA',
        '2025JS', '201UWF', '201JQ0', '2026HV', '201U25', '201JAD', '201GGC',
    ];

    /**
     * 是否开启合同档案校验全局
     *
     * @var bool
     */
    public static $is_open_check_contract = false;

    /**
     * 发票扣减黑名单
     *
     * @var array
     */
    public static $orgBlackList = [
        '2000ZH', '202AM1', '202AQL', '201H1S', '200MNR', '200P0I', '20108J', '200V12', '200UOZ', '201GK7', '200Q4S', '200WKE', '201UTS', '201MS8', '201VOK', '201RWX', '201XGB', '2025ZE', '201W6O', '20272M'
    ];

    /**
     * 新模型下白名单
     *
     * @var array
     */
    public static $orgWhiteListV4 = [
        '2000T0', '2000BG', '200J3I', '200WF3', '2008GA', '200D8X', '20007Z', '201K4V', '201FXC', '20197G', '20266M', '200IF3', '201ICN', '200V3A', '2013LY', '201E6U', '200ZIK', '201ICO', '200L9K', '200MOX', '202448', '2007RL', '2016Z3', '201ICP', '201JU8', '200TOP', '200I8U', '200ZXE', '201CZ7', '2015UF', '200GYU', '200R80', '20005W', '200QWK', '20007J', '201L84', '200HF3', '201J9R', '2016V4', '201H5Z', '200MP2', '201K8E', '201LY1', '201LB8', '200D8V', '200IZE', '201M66', '200TWE', '200PZE', '201LTO', '2000HS', '2008JV', '200OPF', '20181V', '2018IP', '200JD2', '2014GB', '200D5I', '2006KF', '200ND6', '200FJ4', '200URT', '201IOH', '201AG9', '201N03', '201K45', '201LAD', '2010PK', '200G3V', '200GTD', '20115S', '2015LG', '201KNJ', '2019SW', '201JAD', '201LDR', '200FWF', '200V7L', '2001CW', '201INM', '200U2V', '201J6X', '200YI9', '201BGU', '201AO6', '2016F6', '200ZP6', '20007K', '201JZK', '2013MH', '2008EM', '201AGB', '201JJO', '201B7J', '2014GI', '201MGM', '200LNY', '200SKM', '201NE2', '201I0T', '201S3C', '201KZV', '201MBJ', '201BOT', '201GGX', '200I7I', '201LUP', '200X1T', '2013Q7', '201AGC', '2015ML', '200S3K', '201M4O', '200JB0', '201WU2', '200NBQ', '201OP5', '200SBM', '200IDD', '20170X', '201QIO', '201FSO', '200P76', '200BNE', '201AFK', '201B6U', '201VR4', '201JZB', '200B0M', '201C4G', '200PM1', '2015T0', '2001U0', '201H4X', '201BV1', '201IFB', '200YF1', '201AWB', '201KUQ', '2013TE', '200MWY', '201HP2', '201KKD', '200XBX', '201NVC', '200U2X', '201BOO', '2012N8', '2021D1', '201FET', '200XDD', '20145T', '200XTJ', '201Y1U', '200Q52', '201UUV', '201HYC', '200LL9', '201FLY', '201IRN', '201L2Q', '2009JN', '201IGR', '2016F3', '201LSQ', '2014FA', '201BUI', '2015DK', '201WNJ', '201MMP', '201IUF', '201IWD', '200JCV', '200P30', '201HZU', '2014KS', '200WPN', '201H92', '200V3E', '200MTY', '200JHK', '200R0Z', '200G8F', '201JYB', '202160', '200R0E', '200S2E', '201HOT', '200VLO', '201B3W', '201Z3S', '201LQL', '200U19', '200GAL', '200ZX0', '201HVT', '201UWQ', '201DM7', '200HJG', '200IKL', '201AEO', '201OFQ', '20162Q', '201OQL', '201NV7', '201M2C', '200J6T', '201S2H', '200I3F', '201LLH', '200MWK', '2010VS', '201VSQ', '201GAU', '2004AY', '201SV7', '200LS1', '200GSM', '2015PB', '201SZG', '201A5U', '200MKJ', '200Q1E', '200TO4', '201GF6', '201WBQ', '201NJA', '201IJM', '201JOI', '200UK2', '201OQT', '201KFH', '201Q2Y', '200Y1Q', '201RI5', '201F2T', '2017ZX', '200I1R', '201NL8', '200R89', '201IB9', '2021MS', '2018QN', '201INB', '200EV8', '200J46', '201UMS', '201HXV', '201UEQ', '2021H2', '201NIN', '200QJE', '201Z0I', '200SPV', '200SHC', '200Y4P', '201NN4', '201HCZ', '20218A', '201K9F', '200WHE', '200UTH', '200JWG', '201L3F', '200P9I', '200K8G', '201OA8', '200O37', '200K77', '200U9Q', '201JWZ', '200QB8', '200K82', '201IQ0', '201URW', '201O0J', '200UL3', '201K39', '201WD2', '200QI7', '201IAC', '201TB7', '201984', '201WBR', '20081Q', '201J2T', '200IF8', '2014XO', '2001LF', '2017GY', '201OIQ', '2017GJ', '202498', '201ICX', '201B19', '200WTS', '201CWK', '201529', '2015SO', '201EDT', '201VBU', '201JWY', '201GF5', '201Y6T', '200HWI', '200WEN', '200NVS', '200WPT', '201FEL', '201BHP', '200JMG', '201S3H', '201BEO', '201J09', '200P2Q', '201L58', '201KHV', '200TEA', '200NCQ', '201QNF', '201VBP', '200UR7', '201IJT', '201JI7', '200VEC', '201LXN', '201QCM', '201JI1', '201YDW', '201P5A', '201DJ1', '201ML7', '201SIW', '201UYP', '200X7T', '201P5B', '200IYP', '200575', '201B5X', '2020S1', '2019DR', '201W9F', '201FWB', '201MKB', '2026HV', '200N3H', '200J1J', '201LEK', '201TSH', '200VRW', '2016HN', '20120J', '201P8Q', '201P59', '201GTY', '201HS8', '200MZ7', '201N7Y', '201DUC', '201MAP', '200X5N', '200RUC', '201PUF', '201HKB', '200JDV', '201JQ4', '201RXM', '200UWX', '200UQ9', '200XOV', '200RWE', '200IO4', '200TA3', '200W2J', '200SU2', '201QSR', '201CSH', '201TNK', '201BE9', '2023PP', '2020AD', '201I3B', '201CWJ', '200G6R', '201IAT', '200ZEO', '200YP0', '200V1O', '201KAG', '200TP1', '200PHF', '200PXD', '2015UI', '200YLH', '201U8O', '201YDY', '2016TR', '201RWX', '201MKF', '201Y0J', '200OZQ', '201FPH', '200YPO', '200UJ7', '200UHC', '201EU9', '201LIP', '200QV3', '201IMP', '201ISA', '201GF7', '200TG9', '201WNK', '20190D', '200IGV', '201SE4', '20183O', '201EOK', '201LB0', '200GOB', '200JWE', '201E3P', '2021SF', '200WZS', '200IWH', '201MKV', '200MRR', '201FKH', '201JZS', '201SGK', '201DXR', '201LAW', '20141D', '200Z4G', '201137', '201G3L', '20154F', '201MAA', '201K6F', '201Y1P', '2025RA', '200TLX', '202AE3', '2012DI', '200V5S', '200SNH', '201UHB', '20231N', '2012BF', '201I07', '200JQK', '201SIY', '200NY9', '200J8O', '201LP2', '201F21', '201RTL', '200FJ0', '2016C6', '200HYE', '2024M3', '201S2T', '200QNA', '200TGJ', '200F9Z', '200LKT', '201IAH', '201HWW', '201ABR', '201BRQ', '200TJP', '201K58', '200Q4S', '201SSO', '201OOJ', '2025JS', '200R5R', '201J3X', '20123V', '201YOO', '201IKZ', '201HKZ', '201XQ9', '201WV8', '200TJO', '2015XX', '200BSP', '200I6G', '201LMW', '201U25', '201VW4', '20207T', '201YBG', '200XN2', '200KT1', '201MRN', '201JI9', '200VZY', '201MAC', '201OTH', '201TU3', '201XW3', '201LTI', '201MAO', '201H6V', '201PVB', '201NE6', '201TNJ', '201XD3', '20254O', '2020XC', '20192W', '201YC4', '200JM2', '201HEE', '201YMW', '201XQ6', '201XAK', '201W0A', '201KF2', '200UKA', '201HBW', '201EYQ', '2014GZ', '201JMM', '201PNT', '20170R', '201M2M', '2011E6', '2026MF', '201II4', '200WTO', '200Z0J', '200VCQ', '201L07', '201IXV', '200PGO', '20163H', '20254R', '201Q7H', '200VTA', '2010X8', '201GAG', '201W7C', '201X1J', '201VRS', '200IQY', '201GUG', '200ND1', '200YDS', '201MGQ', '2022AH', '200MMT', '201JZD', '201MO6', '201C5W', '201IU8', '2015U3', '201XFD', '200XMN', '20249E', '2011FA', '201GXI', '200ZD7', '201JLK', '201U8A', '201YC9', '2025M1', '200TFT', '200FGW', '200GSI', '201M1Y', '2011P9', '2016NQ', '200LQG', '2012IZ', '202499', '200SEC', '2011W0', '2019DF', '2023TU', '200NYC', '2004X1', '20161S', '200QII', '201L03', '201Q37', '201M2A', '200ICV', '200HF8', '200K6I', '200UZP', '200VKI', '200QL8', '200TE4', '2023VV', '200J6P', '200TS6', '201LJS', '201Q4B', '2016HM', '200FES', '200H4R', '2014T6', '201M2E', '201E62', '201NG4', '20246V', '200VN8', '200US7', '200VWI', '201CQX', '2017W7', '200NG6', '200WPI', '201UFR', '200PH2', '200K2U', '201YZ7', '200WDP', '200XIL', '201448', '201CB6', '200SVS', '201AAN', '200MF5', '201IZ0', '201HK4', '200QB6', '2017EJ', '201ZFG', '201JQJ', '2025XG', '201K44', '201SKT', '200S0X', '201CD1', '200HYF', '200XD1', '2014D8', '201EQC', '201JOB', '200PDD', '20249C', '202100', '200RX0', '201UNW', '201Z6E', '200QXR', '201BHQ', '201KST', '201B9O', '20250X', '201R7T', '2028H3', '201DMO', '201J0B', '200OYR', '200NM9', '2020WT', '201H85', '201UI8', '201REM', '200YEC', '200MI2', '201T9O', '200TTJ', '2015YU', '20243W', '201AZJ', '201OCX', '200L5M', '201N2D', '20200W', '20261N', '200R3E', '201AFJ', '200I05', '201JZE', '20236O', '201YNU', '201W5Y', '20170P', '201NY7', '20246P', '200B3Q', '2013PW', '2021H6', '200YZV', '200PW2', '201KRI', '20239M', '201JAF', '201B29', '200ZQK', '201B2Q', '201ZVO', '200Y4Y', '2011QQ', '201CS8', '2028RJ', '201HDR', '201KJQ', '201EZ4', '201HFZ', '200TZ2', '200Z7R', '2017P0', '2017GT', '200DBY', '201EZX', '200OIP', '20187F', '200OS1', '200UZI', '2018NE', '201JOW', '201I7L', '200QXM', '200MVF', '20132N', '201JKU', '201JHT', '200X3L', '200TLD', '200YK9', '20184D', '200X0C', '201JJN', '201M4G', '200VYQ', '201F88', '200P2M', '2013ER', '200TDE', '2011SN', '201IJS', '201IFU', '201NVW', '2018TS', '200ICU', '200R78', '200T39', '2006B1', '201NZL', '2018ZK', '200NNW', '2029DN', '200R6B', '200XNP', '20135H', '20296J', '200PQ6', '200Z1M', '201RAY', '200Q3M', '201ORB', '2029L5', '2016YU', '20106J', '201E7X', '201A19', '201JA0', '200WTC', '201AU2', '2028ZB', '201LB4', '201JZV', '200V9V', '2023OS', '2015YE', '200RLS', '201JE2', '201IIS', '20291I', '2019SA', '200R9B', '200V26', '201ISL', '201HNA', '2028OM', '201JQ0', '201HU0', '201L5N', '201ZJN', '2024QM', '2029FQ', '2018CG', '202AAN', '200PH4', '2012YR', '201J9M', '2029A3', '201IJP', '200HYJ', '201ILB', '202BDR', '2024TZ', '201I35', '2016AH', '201SRU', '2029FU', '20294S', '2012XX', '2029PK', '2024WO', '201CQZ', '20158U', '2011AX', '201FEA', '200T59', '20130I', '201EVX', '201IID', '2025MS', '201ZJQ'
    ];

    // 2019-09-24 黑名单
    public static $orgBlackListV5 = [
        '202GSP', '202AM1', '202AQL', '201H1S', '200MNR', '200P0I', '20108J', '200V12', '200UOZ', '201GK7', '200Q4S', '200WKE', '201UTS', '201MS8', '201VOK', '201RWX', '201XGB', '2025ZE', '201W6O', '20272M', '202F0G', '202F0R', '202FMY', '2026QS'
    ];

    public static $orgBlackListV6 = [
        '200MNR',//义米能源科技（上海）有限公司
        '200P0I',//盈科世名（厦门）石化有限公司
        '200UOZ',//上海盛海石油化工有限公司
        '200V12',//湖北盛宇源化工有限公司
        '20108J',//厦门永新城石油制品有限公司
        '201GK7',//江苏优联可能源科技有限公司
        '201H1S',//宁夏九鼎物流
        '200Q4S',//福佑（大连）能源有限公司
        '200WKE',//四川森能天然气销售有限公司
        '201UTS',//浙江舟山明文石化有限公司
        '201MS8',//武汉鑫强飞石油化工有限公司
        '201RWX',//宁夏中鸿源化工有限公司
        '201VOK',//黑龙江华盛宏图能源贸易有限公司
        '201XGB',//四川德盛巨能石油化工有限公司
        '2025ZE',//刘洪姣
        '201W6O',//山东坤瑞能源有限公司
        '20272M',//重庆国通石油
        '202AM1',//卓达能源（深圳）有限公司
        '202AQL',//物流宝兑通
        '2026QS',//陕西恒通包装物流有限公司
        '202F0G',//舟山陆泰石油化工有限公司
        '202F0R',//江苏卡满行物联科技有限公司-油品
        '202FMY',//无锡壹卡车网络科技有限公司
        '202GSP',//张海代理商
        '202VO7',//铁铁智慧物流（天津）有限公司
        '20391H',//广东汉森能源科技有限公司1
        '2034BK',//舟山励拓石油化工有限公司
        '2037V6',//上海亥亚石油化工有限公司
        '2016F3',//内蒙古金谷石化有限责任公司
        '202D6S',//河南科益气体股份有限公司
        '2018AA',//广东华特气体股份有限公司
        '2032DM',//深圳市候鸟互联网有限公司1
        '2038RE',//延边东北亚新能源开发有限公司天津分公司
        '200T9G',//壹米滴答
        '2038U8',//武汉海云丰物流有限公司-油品
        '203HQV',//深圳市佳润石油有限公司
        '203HN2',//大连港万通物流有限公司
        '203H9P',//浙江自贸区仲德能源有限公司
        '202LC1',//黑龙江中阔物流有限公司-油品代理
        '203IAI',//森荣（大连）能源有限公司 
        '203JFY',//上海虹鲁石油化工有限公司 
        '203JG8',//江苏安苏化工产品有限公司 
        '203KRK',//大连龙门石油化工有限公司 
        '203LZY',//河北跨省业务
        '203MTI',//宏智控股有限公司
        '203LN7',//辽宁启恒供应链管理有限公司
        '203NAF',//辽宁星斗生物科技有限公司
        '203MH9',//重庆油圈共享有限公司
        '203OYN',//浙江跨省业务销售有限公司
        '203FJ3',//辽宁阿拉丁供应链管理有限公司
        '202VK6',//济南捷安冷藏物流有限公司-1号卡
        '203RS4',//黑龙江天狼星物流有限公司
        '203RIB',//哈尔滨跃兴物流有限公司
        '203SCU',//哈尔滨龙鹏物流有限责任公司
        '203SG8',//海城云仓物流有限公司
        '203TC6',//浙江奥宸石化有限公司-油品
        '203SSE',//哈尔滨瑞锦鑫运输有限公司
        '203SSH',//黑龙江南翔国际物流有限公司
        '203SSL',//	海城飞扬物流有限公司
        '203SZT',//	天津市飞腾物流有限公司
        '203TKB',//天津市大通物流有限公司
        '203TQF',//	辽宁物流有限公司
        '203SHS',//四川李俊锋石油化工有限公司
        '203V29',//周来福商贸有限公司
        '203SOG',//山东启源石油化工有限公司
        '203NCY',//营口盛鑫隆泰物资有限公司
    ];

    public static $steps = [
        ['ge' => 30, 'lt' => 50, 'val' => -4],
        ['ge' => 50, 'lt' => 75, 'val' => -6],
        ['ge' => 75, 'lt' => 100, 'val' => -9],
        ['ge' => 100, 'lt' => 125, 'val' => -12],
        ['ge' => 125, 'lt' => 150, 'val' => -16],
        ['ge' => 150, 'lt' => 175, 'val' => -19],
        ['ge' => 175, 'lt' => 200, 'val' => -21],
        ['ge' => 200, 'lt' => 300, 'val' => -26],
        ['ge' => 300, 'lt' => 400, 'val' => -39],
        ['ge' => 400, 'lt' => 500, 'val' => -52],
        ['ge' => 500, 'lt' => 600, 'val' => -65],
        ['ge' => 600, 'lt' => 1000, 'val' => -78],
        ['ge' => 1000, 'lt' => 2000, 'val' => -130],
        ['ge' => 2000, 'lt' => 5000, 'val' => -260],
        ['ge' => 5000, 'lt' => 10000, 'val' => -650],
        ['ge' => 10000, 'lt' => 20000, 'val' => -1300],
        ['ge' => 20000, 'lt' => 9999999999999999999, 'val' => -2600],
    ];

    /**
     * @title   库存扣减计算
     * @desc
     * @param $trade_num
     * @param $price
     * @param $orgCode
     * @param $oil_type
     * @return int
     * @returns
     * []
     * @returns
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
     */
    public static function deductionTradeNum($trade_num, $price, $orgCode, $oil_type)
    {
        Log::error('$trade_num:' . $trade_num . '$price:.' . $price . '$orgcode:' . $orgCode . '$oil_type:' . $oil_type, [], 'deduction');
        //机构白名单
//        $whiteOrgCode =['200133','201E6U','2000T0','201ICO','201ICP','201TRR','2000BG','201ICN','201M66','201FH7','201MKB','201LP2','200826','201FLP','201MKF','201UWF','20007I','2008GA','200JHK','200IZE','201QIO','201OOJ','201H1S','201BEO','201CZ7','201MS8','200TWE','201LDR','201K4V','201K9F','201WPD','201JZS','200KX2','200UH3','200RKF','2026HV','201U25','200NCQ','200IF3','201Z0I','200PTR','2007RL','201JAD','2008JV','200P9I','201GH2','2019DR','201IZ5','2016Z3','201LY1','201BHP','200D8X','201VQI','2013MH','201R9K','201GGC','20239M','201E6U','2000T0','201ICO','201ICP','201TRR','2000BG','201ICN','201M66','201FH7','201MKB','201LP2','200826','201FLP','201MKF','201UWF','20007I','2008GA','200JHK','200IZE','201QIO','201OOJ','201H1S','201BEO','201CZ7','201MS8','200TWE','201LDR','201K4V','201K9F','201WPD','201JZS','200KX2','200UH3','200RKF','2026HV','201U25','200NCQ','200IF3','201Z0I','200PTR','2007RL','201JAD','2008JV','200P9I','201GH2','2019DR','201IZ5','2016Z3','201LY1','201BHP','200D8X','201VQI','2013MH','201R9K','201GGC','20239M'];
        $whiteOrgCode = self::$orgWhiteList;
        if (in_array(substr($orgCode, 0, 6), $whiteOrgCode) && $price <= 6.7 && $trade_num > 30 && $oil_type == \Fuel\Defines\OilType::CHAI_YOU) {
            $rules = self::$steps;
            $diff  = 0;
            foreach ($rules as $v) {
                if ($trade_num >= $v['ge'] && $trade_num < $v['lt']) {
                    $diff = $v['val'];
                    break;
                }
            }

            $trade_num = $trade_num + $diff;
        }
        Log::error('after:' . $trade_num, [], 'deduction');

        return $trade_num;

    }

    public static function getMonth($sign = "1", $monthNum = 1)
    {
        //得到系统的年月
        $tmp_date = date("Ym");
        //切割出年份
        $tmp_year = substr($tmp_date, 0, 4);
        //切割出月份
        $tmp_mon          = substr($tmp_date, 4, 2);
        $tmp_nextmonth    = mktime(0, 0, 0, $tmp_mon + $monthNum, 1, $tmp_year);
        $tmp_forwardmonth = mktime(0, 0, 0, $tmp_mon - $monthNum, 1, $tmp_year);
        if ($sign == 0) {
            //得到当前月的下一个月
            return $fm_next_month = date("Y-m", $tmp_nextmonth);
        } else {
            //得到当前月的上一个月
            return $fm_forward_month = date("Y-m", $tmp_forwardmonth);
        }
    }

    /**
     * 主卡开票消费限制白名单
     * @return array
     */
    public static function getOrgTradeForMainCardWhiteList()
    {
        if(in_array(API_ENV, ['prod', 'pro'])){
            return ['200B1F','204BPW'];
        }else{
            return ['200L2S'];
        }
    }

    public static function getReceiptSplitOilUnitMap()
    {
        return [
            'kg' => '千克',
            'kG' => '千克',
            'Kg' => '千克',
            'KG' => '千克',
            '千克' => '千克',
            '公斤' => '千克',
            '千克（公斤）' => '千克',
            '千克(公斤)' => '千克',
            'm3' => '立方米',
            'M3' => '立方米',
            '方' => '立方米',
            '立方' => '立方米',
            '立方米' => '立方米',
            '标方' => '立方米',
            '升' => '升',
            '公升' => '升',
            '吨' => '吨',
        ];
    }
}