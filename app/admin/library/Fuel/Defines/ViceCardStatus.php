<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;


use Framework\Helper;

class ViceCardStatus
{
    const USING   = '使用';
    const LOCKING = '锁定';
    const LOSS    = '挂失';
    const LIMITED = '受限';
    const DISCARD = '丢弃';
    const OTHER   = '其他';
    const FREEZE  = '冻结';

    static public $list = [
        self::USING   => self::USING,
        self::LOCKING => self::LOCKING,
        self::LOSS    => self::LOSS,
        self::LIMITED => self::LIMITED,
        self::DISCARD => self::DISCARD,
        self::OTHER   => self::OTHER,
        self::FREEZE   => self::FREEZE,
    ];

    static public $listForGos = [
        self::USING   => 10,
        self::LOCKING => 30,
        self::LOSS    => 20,
        self::LIMITED => 40,
        self::OTHER   => 90,
        self::DISCARD   => 60,
        self::FREEZE   => 70,
    ];

    static public function getById($id)
    {
        return isset(self::$list[ $id ]) ? self::$list[ $id ] : NULL;
    }
    static public function getStatusId($status)
    {
        $status = trim($status);
        return isset(self::$listForGos[ $status ]) ? self::$listForGos[ $status ] : NULL;
    }
    static public function getStatusName($status)
    {
        $status = trim($status);
        $_list = array_flip( self::$listForGos );
        return isset($_list[ $status ]) ? $_list[ $status ] : NULL;
    }

    static public function getAll()
    {
        return self::$list;
    }

    static public function getStatusForGos($key)
    {
        if($key == '--'){
            $key = '使用';
        }
        return isset(self::$listForGos[$key]) && self::$listForGos[$key] ? self::$listForGos[$key] : 90; //未匹配上都是其他
    }

    /************************余额实时刷新状态**********************/
    static public $ViceCardQueryStatus= array(
        '10'  => '余额刷新完成',
        '5'   => '余额刷新中',
        '0'   => '',
        '-10' => '余额刷新失败',
    );

    static public function getQueryStatusById($id)
    {
        return isset(self::$ViceCardQueryStatus[$id]) ? self::$ViceCardQueryStatus[$id] : null;
    }

    static public function getQueryStatusAll()
    {
        return self::$ViceCardQueryStatus;
    }

    static public function newAssignMains()
    {
 	return ['9130300001470713','9130300001626063','1000114400060124935','9130300001224001','9130300001293240','9130300000334001','9130250000456639','9130150000881601','9130300001293240','9130000001543332','9130290000791656','9130300001075834','9130300001224001','9130300001238318','9130300001294189','9130300001470713','9130170005484787','9130020002258888','9130160000468953','9130020002251111','9130020002252222','9130240001014204','9130240001051123','9130070000284957','9130300001626062','9130300001626063','9130300001626064','9130300001626008','9130060001409746','9130300001292027','9130010004164306','9130010005195986','9130010004980981','9130010005286597',
        '1000111100014496516','1000111100014491111','1000111300008518926','1000113700014194903','1000113300014512755','1000111100014502445','1000111200003287692','1000111200003307750','1000111200003329652','1000111200001234567','1000111400003661784','1000114400017484959','1000114400017977067','1000114200004878807','1000119000002264384','1000115300006403842','1000118621000715387','1000113200015370818','1000114300005363607','1000113600004119881','1000115100000505598','1000114200006032350','1000113200016016102','1000114100008829251','1000114300005595517','1000113700016658001','1000115100001822053','1000111400004095508','1000113200018606283','1000111100019401751','1000114400020125072','1000115300007014540','1000111400004194884','1000118621001121471','1000113700017204487','1000113200019440626','1000114200008106371','1000113700017362600','1000111100014492222','1000114200003720988','1000114400020465546','1000114200007636915','1000111100007729376','1000113200022033132','1000111100008718449','1000114200007619548','1000114400021177600','1000113200023178251','1000113100013000401','1000113100012915103','1000114400021791588','1000113700019369360','1000113100013735082','1000113200023226355','1000113100013901251','1000118615060014366','1000113100014043749','1000114400023042846','1000113300020259251','1000113100014254455','1000114300007980396','1000114200008077751','1000114100012024474','1000111100018656090','1000111300007239999','1000113200012028374','1000113200012028373','1000118622000239500','1000118615003361863','1000113200025677993','1000118623000626350','1000113300020295096','1000113200026667346','1000114400024101118','1000113100014420687','1000113300020658751','1000113300020659501','1000115200004286698','1000113600006377621','1000111100060064408','1000111100060065624','1000113100060038007','1000113100060003259','1000114400060087039','1000113700060058568','1000114100060030255','1000114200060116346','1000111200060000006','1000115300060067986','1000113300060013400','1000115100060000531','1000113200060075235','1000118621060010497','1000111100060092028','1000111400060002465','1000111300060184387','1000114300060108032','1000114400060092990','1000113200060000545','1000113500060033443','1000115200060030252','1000118615060049538','1000115000060010150','1000113600060000585','1000114400060124877','1000113100060097726','1000113700060150018','1000115100060044584','1000114400060124935','1000113100060120017','1000111100060015626','1000113200060075751','1000111100060015004','1000111100060015008','1000111100060015005','1000114200060268245']; //1000113200018606283
    }
}