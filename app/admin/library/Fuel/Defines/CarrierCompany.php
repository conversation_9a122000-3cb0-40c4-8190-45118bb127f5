<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;


class CarrierCompany
{
    const COMPANY_TA     = 10;

    static public $list = [
        self::COMPANY_TA      => '泰安市峰松电子科技有限公司'
    ];

    static public function getById($id)
    {
        return isset(self::$list[$id]) ? self::$list[$id] : NULL;
    }

    static public function getAll()
    {
        return self::$list;
    }
}