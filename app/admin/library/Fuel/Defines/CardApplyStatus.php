<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;

class CardApplyStatus
{
    //已驳回
    const REJECT = '-10';
    //待审核
    const PRE_AUDIT = '10';
    //已转卡务
    const ACCEPTED = '20';
    //已完成
    const COMPLETE = '30';
    //未提交
    const UNCOMMIT = '-20';
    //办卡
    const Banka    = '';
    //寄出
    const JICHU    = '40';

    static public $status = [
        self::REJECT    => '已退回',
        self::PRE_AUDIT => '待审核',
        self::ACCEPTED  => '已转卡务',
        self::COMPLETE  => '已完成',
    ];
    
   static public $appStatus = [
        self::UNCOMMIT  => '未提交',
        self::PRE_AUDIT => '已提交',//审核中【前端显示俩个流程点】
        self::ACCEPTED  => '已审核',//办卡中【前端显示俩个流程点】
        self::REJECT    => '已退回',
        self::COMPLETE  => '已完成',
        self::Banka     => '已办卡',//预开卡单状态==完成&&开卡单==已绑主卡
        self::JICHU     => '已寄出',//预开卡单状态==完成&&开卡单==完成
    ];

    static public $apiAppStatus = [
        self::UNCOMMIT  => '未提交',
        self::PRE_AUDIT => '审核中',//审核中【前端显示俩个流程点】
        self::ACCEPTED  => '办卡中',//办卡中【前端显示俩个流程点】
        self::REJECT    => '被退回',
        self::COMPLETE  => '待寄出',
        self::JICHU     => '已寄出',//预开卡单状态==完成&&开卡单==完成
    ];

    static public function getById($id)
    {
        return isset(self::$status[$id]) ? self::$status[$id] : NULL;
    }

    static public function getApiAppStatus($id){
        return isset(self::$apiAppStatus[$id]) ? self::$apiAppStatus[$id] : NULL;
    }

    static public function getAll()
    {
        return self::$status;
    }
}