<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;

/*$card_from = [
    '10' => '汇通卡',
    '20' => '中交卡',
    '30' => '撬装卡',
    '40' => '托管卡',
    '41' => '数据托管卡',
    '50' => '盛海卡',
    '60' => '无车承运',
];*/

class CardFrom
{
    const SELF_CARD     = 10;
    const ZCW_CARD      = 20;
    const GAS_CARD      = 30;
    const CUSTOMER_CARD = 40;
    const CUSTOMER_CARD_SIMPLE = 41; //数据托管业务
    const SHENGHAI_CARD = 50;
    const FREE_CARRIER_CARD = 60;//无车承运

    static public $list = [
        self::SELF_CARD     => [
            'name'     => '汇通卡',
            'sub_name' => '汇通卡',
        ],
        self::ZCW_CARD      => [
            'name'     => '中交卡',
            'sub_name' => '汇通卡'
        ],
        self::GAS_CARD      => [
            'name'     => '撬装卡',
            'sub_name' => '撬装卡'
        ],
        self::CUSTOMER_CARD => [
            'name'     => '托管卡',
            'sub_name' => '托管'
        ],
        self::CUSTOMER_CARD_SIMPLE => [
            'name'     => '数据托管卡',
            'sub_name' => '数据托管卡'
        ],
        self::SHENGHAI_CARD => [
            'name'     => '盛海卡',
            'sub_name' => '盛海卡'
        ],
        self::FREE_CARRIER_CARD => [
            'name'     => '无车承运',
            'sub_name' => '无车承运'
        ]
    ];

    static public function getById($id)
    {
        return isset(self::$list[$id]) ? self::$list[$id] : NULL;
    }

    /**
     * @title 获取卡来源名称(G7s)
     * <AUTHOR>
     * @param $id
     * @return mixed
     */
    static public function getByIdFromG7s($id)
    {
        $cardFrom =  self::$list[$id];

        return isset($cardFrom['sub_name']) ? $cardFrom['sub_name'] : '';
    }

    static public function getAll()
    {
        return self::$list;
    }

    static public function getFanliCardFrom()
    {
        return [10,20,30];
    }

    static public function getNotCustomerCard()
    {
        return [CardFrom::SELF_CARD, CardFrom::ZCW_CARD, CardFrom::SHENGHAI_CARD,CardFrom::GAS_CARD,CardFrom::FREE_CARRIER_CARD];
    }

    static public function getProductList($key)
    {
        $listArr = [
            10 => '石油石化',
            20 => 'G7能源账户'
        ];
        return $listArr[$key];
    }

    /**
     * 是否为数据托管业务
     * @param $key
     * @return bool
     */
    static public function isCustomerCard($key)
    {
        return in_array($key, [self::CUSTOMER_CARD, self::CUSTOMER_CARD_SIMPLE]);
    }
}