<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;


use Framework\Helper;

class AccountAssignStatus
{
    //已驳回
    const REJECT = '-1';
    //待审核
    const PRE_AUDIT = '0';
    //已审核
    const AUDITED = '1';
    //主站待分配
    const PRE_ASSIGN = '15';
    //主站分配中
    const ASSIGNING = '20';
    //主站分配完成
    const ASSIGNED = '10';
    //主站分配失败
    const ASSIGN_FAILED = '-10';
    //分配异常
    const ASSIGN_EXCEPTION = '-20';

    //圈回部分成功
    const ASSIGN_BACK_PART = '-8';

    //主站部分成功
    const ASSIGN_SUCCESS_PART = '8';
    //主站圈回工单 预定义机构code
    const MAIN_TURN_ORG_ID = "201Q4V";
    //主站圈回工单 预定义标识
    const MAIN_TURN_NO_TYPE = "QH";


    static public $AccountAssignStatus = [
        self::REJECT              => '已驳回',
        self::PRE_AUDIT           => '待审核',
        self::AUDITED             => '已审核',
        self::PRE_ASSIGN          => '主站待分配',
        self::ASSIGNING           => '主站分配中',
        self::ASSIGNED            => '主站分配完成',
        self::ASSIGN_FAILED       => '主站分配失败',
        self::ASSIGN_EXCEPTION    => '分配异常',
        self::ASSIGN_SUCCESS_PART => '主站部分成功',
        self::ASSIGN_BACK_PART => '圈回部分成功',
    ];

    static public $AccountAssignStatusForGos = [
        self::REJECT              => '-10',
        //self::PRE_AUDIT           => '10',
        self::PRE_AUDIT           => '30',
        self::AUDITED             => '40',
        self::PRE_ASSIGN          => '10',
        self::ASSIGNING           => '30',
        self::ASSIGN_EXCEPTION    => '30',
        self::ASSIGNED            => '30',
        self::ASSIGN_FAILED       => '30',
        '-101'                    => '30',
        self::ASSIGN_SUCCESS_PART => '30',
        self::ASSIGN_BACK_PART    => '30',
    ];

    /**
     * 不允许更改的状态
     * @var array
     */
    static public $mayUpdateStatus = [
        self::REJECT,
        self::ASSIGNING,
        self::ASSIGNED,
        self::ASSIGN_EXCEPTION,
        self::AUDITED,
        self::ASSIGN_FAILED,
    ];

    static public function getById($id)
    {
        return isset(self::$AccountAssignStatus[ $id ]) ? self::$AccountAssignStatus[ $id ] : NULL;
    }

    static public function getAll()
    {
        return self::$AccountAssignStatus;
    }

    static public function getAssignStatusForGos($key)
    {
        return Helper::statusToGos($key, self::$AccountAssignStatusForGos);
    }


    ############# 分配单类型 ###############

    const AUTO    = '10';
    const NO_AUTO = '20';
    const GAS     = '30';
    const JIFEN   = '40';

    static public $assign_type = [
        self::AUTO    => '自动分配',
        self::NO_AUTO => '非自动分配',
        self::GAS     => '撬装分配',
        self::JIFEN   => '积分分配',
    ];

    static public function getByTypeId($id)
    {
        return isset(self::$assign_type[ $id ]) ? self::$assign_type[ $id ] : NULL;
    }

    static public function getAllAssignType()
    {
        return self::$assign_type;
    }

    //g7s的分配状态
    static $list = [
        '-10' => '失败',
        '-20' => '已驳回',
        '10'  => '待审核',
        '20'  => '已受理',
        '30'  => '办理中',
        '40'  => '完成',
    ];

    //获取forg7s的分配单状态
    static public function getByKey($key)
    {
        return isset(self::$list[ $key ]) ? self::$list[ $key ] : \NULL;
    }

    //祥辉物流调价配置
    static public function getSpecialList()
    {
        //float_price:管理费; special_orglist 配置的机构
        //'20320F':线上祥辉物流机构
        //G7WALLET-6989
        return [
            'FLOAT_PRICE' => 0,
            'SPECIAL_ORGLIST' => []
        ];
        // return [
        //     'FLOAT_PRICE' => 0.015,
        //     'SPECIAL_ORGLIST' => [
        //         '20320F',
        //         '200Q1F', //test
        //         '200NW5', //test
        //         //'200021', //online
        //     ]
        // ];
    }
}