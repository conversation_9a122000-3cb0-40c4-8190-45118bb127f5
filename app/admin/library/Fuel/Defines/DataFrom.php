<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;


class DataFrom
{
    const GSP    = 1;
    const WEB    = 2;
    const APP    = 3;
    const WeChat = 4;
    const CRM    = 5; // 20190716 lpj add
    const OPENAPI    = 6; // 针对openapi获取

    static public $list = [
        self::GSP    => 'GSP',
        self::WEB    => 'WEB',
        self::APP    => 'APP',
        self::WeChat => 'WeChat',
        self::CRM    => 'CRM',
        self::OPENAPI    => 'OPENAPI',
    ];

    static public function getById($id)
    {
        return isset(self::$list[$id]) ? self::$list[$id] : NULL;
    }

    static public function getAll()
    {
        return self::$list;
    }
}