<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;

class ReceiptApplyStatus
{
    //已作废
    const REJECT = '-1';
    //待审核
    const PRE_AUDIT = '0';
    //已审核
    const AUDITED = '1';
    //处理中
    const HANDLING = '10';
    //开票中
    const OPENING = '15';
    //开票成功
    const SUCCESS = '20';
    //已邮寄
    const MAILED = '30';
    
    static public $status = [
        self::REJECT    => '已作废',
        self::PRE_AUDIT => '待审核',
        self::AUDITED   => '已审核',
        self::HANDLING  => '系统处理中',
        self::OPENING   => '开票中',
        self::SUCCESS   => '开票成功',
        self::MAILED    => '已邮寄',
    ];
    
    static public function getById($id)
    {
        return isset(self::$status[$id]) ? self::$status[$id] : NULL;
    }
    
    static public function getAll()
    {
        return self::$status;
    }
}