<?php
/**
 * Created by PhpStorm.
 * User: zlx66
 * Date: 2016/7/1/001
 * Time: 17:00
 */

namespace Fuel\ApiTest;

use Framework\Mailer\MailSender;

class RunTest
{
    /**
     * 无需执行的test
     * @var array
     */
    static private $whiteList = [
        '__construct'
    ];

    public function doTest()
    {
        global $app;

        $params = \helper::filterParams();

        if (!isset($params['caseName']) || !$params['caseName']) {
            throw new \RuntimeException('testCase name must not be null!');
        }

        $caseName = trim($params['caseName']);

        $classFile = dirname(__FILE__) . DIRECTORY_SEPARATOR . $caseName . '.php';
        require_once $classFile;

        $_caseClassName = \Fuel\ApiTest\ClassName::class;

        $caseClassName = str_replace('ClassName', $caseName, $_caseClassName);

        $methodList = get_class_methods($caseClassName);

        $testCaseClass = new \ReflectionClass($caseClassName);
        $testCaseObject = $testCaseClass->newInstance();

        if ($methodList) {
            foreach ($methodList as $v) {
                if (in_array($v, self::$whiteList)) {
                    continue;
                }
                try {
                    $testCaseObject->$v();
                } catch (\Exception $e) {
                    \Framework\Log::dataLog("runTest" . strval($e), 'testCaseError');
                }
                usleep(200);
            }

            $mailContent = file_get_contents(APP_ROOT . DIRECTORY_SEPARATOR . 'tmp' . DIRECTORY_SEPARATOR . 'cache' . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . $testCaseObject->logFile . '.php');
            $mailContent = "<table style='font-family: verdana,arial,sans-serif;font-size:14px;color:#333333;	border: 1px #666666 solid;border-collapse: collapse;'>
            <tr>
            <th width='10%' style='border: 1px #666 solid;padding: 8px;background-color: #dedede;'>状态</th>
            <th width='15%' style='border: 1px #666 solid;padding: 8px;background-color: #dedede;'>模块名</th>
            <th width='15%' style='border: 1px #666 solid;padding: 8px;background-color: #dedede;'>方法名</th>
            <th width='12%' style='border: 1px #666 solid;padding: 8px;background-color: #dedede;'>用时(s)</th>
            <th width='12%' style='border: 1px #666 solid;padding: 8px;background-color: #dedede;'>内存占用</th>
            <th align='center' width='16%' style='border: 1px #666 solid;padding: 8px;background-color: #dedede;'>日期</th>
            <th width='20%' style='border: 1px #666 solid;padding: 8px;background-color: #dedede;'>备注</th>
            </tr>" . $mailContent . '</table>';

            MailSender::sendEmail($app->config->api_env . '-' . $caseName . '-' . date("Y-m-d H:i:s"), $mailContent, \Framework\Config::get('alarmEmailList.list'), TRUE);
        }


        echo "Run Finished!";

    }

}