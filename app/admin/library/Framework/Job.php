<?php
/**
 * Created by PhpStorm.
 * @example
 * 请在对象方法下使用
 * (new \Framework\Job())
 * ->setTaskName($taskName)
 * ->pushTask(function () {
 *
 * })
 * ->channel('default')
 * ->delay(300)
 * ->tries(3)
 * ->exec();
 * User: zlx66
 * Date: 2016/8/5/005
 * Time: 15:00
 */

namespace Framework;

use Models\OilJobs;
use SuperClosure\Serializer;
use SuperClosure\Analyzer\AstAnalyzer;
use Framework\Log;

class Job implements JobInterface
{
    protected $taskName = '';

    protected $closure;

    protected $channel = 'default';

    protected $tries = 0;

    protected $afterTime = 0;

    static protected $sleep = 3;

    /**
     * 设置任务逻辑
     * @param callable $callBack
     * @return $this
     */
    public function pushTask(callable $callBack)
    {
        $serializer = new Serializer(new AstAnalyzer());
        $this->closure = json_encode($serializer->serialize($callBack));
//        $this->closure = $serializer->serialize($callBack);

        return $this;
    }

    /**
     * 设置队列通道
     * @param $channel
     * @return $this
     */
    public function channel($channel)
    {
        $this->channel = $channel;

        return $this;
    }

    /**
     * 最多允许尝试几次
     * @param $tries
     * @return $this
     */
    public function tries($tries = 0)
    {
        $this->tries = intval($tries);

        return $this;
    }

    /**
     * 延时多少秒执行
     * @param $delay
     * @return $this
     */
    public function delay($delay = 0)
    {
        $delay = intval($delay);
        if ($delay > 0) {
            $this->afterTime = time() + $delay;
        }

        return $this;
    }

    /**
     * 设置任务名称
     * @param $taskName
     * @return $this
     */
    public function setTaskName($taskName)
    {
        $this->taskName = $taskName;

        return $this;
    }

    /**
     * 执行
     * @return mixed|null
     */
    public function exec()
    {
        $data = NULL;
        if ($this->closure) {
            $params = [];
            $params['channel'] = $this->channel;
            $params['task_name'] = $this->taskName;
            $params['tries'] = $this->tries;
            $params['after_time'] = $this->afterTime;
            $params['data'] = $this->closure;

            $data = OilJobs::add($params);
        }

        return $data;
    }

    static function getmicrotime()
    {
        list($usec, $sec) = explode(" ", microtime());

        return ((float)$usec + (float)$sec);
    }

    /**
     * 获取任务并执行
     * @param string $channel
     */
    static function getTask($channel = 'default')
    {
        set_time_limit(0);

//        $task = OilJobs::getTask($channel);
        $task = OilJobs::getTask($channel);
        if ($task && isset($task->id) && $task->id) {
            OilJobs::edit(
                [
                    'id'     => $task->id,
                    'status' => 1,
                ]
            );
            $startTime1 = self::getmicrotime();
            Log::debug($task->id . ' - taskBegin', [
                'taskId' => $task->id, 'taskName' => $task->task_name, 'channel'
                         => $task->channel
            ], 'jobOk');
            $serializer = new Serializer();
            $taskInfo = json_decode($task->data);
            global $app;
            $app->myAdmin = $taskInfo->userInfo;
            $closure = $serializer->unserialize($taskInfo);
            self::fire($closure, $task);
            $startTime2 = self::getmicrotime();
            Log::debug($task->id . ' - taskEnd, timeUse:' . ($startTime2 - $startTime1), [
                'taskId'  => $task->id, 'taskName' => $task->task_name,
                'channel' => $task->channel
            ], 'jobOk');
            usleep(10);
        } else {
            sleep(self::$sleep);
        }

        self::getTask($channel);
    }

    /**
     * 执行并记录任务
     * @param $closure
     * @param $task
     */
    static public function fire($closure, $task)
    {
        try {
            $closure($task);
            \Framework\Log::error($task->id . ' - taskFinished, Remove Task', [
                'taskId' => $task->id, 'taskName' =>
                    $task->task_name
            ], 'jobOk');

            OilJobs::remove(['ids' => $task->id]);

        } catch (\Exception $e) {
            \Framework\Log::error($task->id . ' - task, Exception:' . strval($e), [
                'taskId' => $task->id, 'taskName' =>
                    $task->task_name, 'channel'
                         => $task->channel
            ], 'jobOk');
            $nextTried = $task->tried + 1;
            if ($task->tries > 0) {
                if ($task->tries > $nextTried) {
                    $upArr = [
                        'id'    => $task->id,
                        'tried' => $nextTried
                    ];
                } else {
                    $upArr = [
                        'id'     => $task->id,
                        'status' => -2,
                        'tried'  => $nextTried
                    ];
                    if ($task->channel == 'export') {
                        $closure($task, TRUE);
                    }
                }

            } else {
                $upArr = [
                    'id'     => $task->id,
                    'status' => -2
                ];
                if (in_array($task->channel, ['export1', 'export2'])) {
                    Log::error($task->id . ' - task, Exception', [
                        'taskId' => $task->id, 'taskName' => $task->task_name, 'channel'
                                 => $task->channel, 'exception' => strval($e)
                    ], 'jobOk');
                    $closure($task, TRUE);
                }
            }
            $upArr['message'] = 'CODE:' . $e->getCode() . ' | message:' . $e->getMessage() . ' | exception: ' . strval($e);
            OilJobs::edit($upArr);

        }
    }
}
