﻿
Installation
    Comment installer HTML Purifier

Attention: Ce document a encode en UTF-8. Si les lettres avec les accents
est essoreuse, prenez un mieux editeur de texte.

À L'Aide: Je ne suis pas un diseur natif de français. Si vous trouvez une
erreur dans ce document, racontez-moi! Merci.


L'installation de HTML Purifier est trés simple, parce qu'il ne doit pas
la configuration.  Dans le pied de de document, les utilisateurs
impatient peuvent trouver le code, mais je recommande que vous lisez
ce document pour quelques choses.


1.  Compatibilité

HTML Purifier fonctionne dans PHP 5. PHP 5.0.5 est le dernier
version que je le testais.  Il ne dépend de les autre librairies.

Les extensions optionnel est iconv (en général déjà installer) et
tidy (répandu aussi). Si vous utilisez UTF-8 et ne voulez pas
l'indentation, vous pouvez utiliser HTML Purifier sans ces extensions.


2.  Inclure la librarie

Utilisez:

    require_once '/path/to/library/HTMLPurifier.auto.php';

...quand vous devez utiliser HTML Purifier (ne inclure pas quand vous
ne devez pas, parce que HTML Purifier est trés grand.)

HTML Purifier utilise 'autoload'. Si vous avez définu la fonction
__autoload, vous doivez ajoute cet programme:

    spl_autoload_register('__autoload')

Plus d'information est dans le document 'INSTALL'.


3.   Installation vite

Si votre site web est en UTF-8 et XHTML Transitional, utilisez:

<?php
    require_once '/path/to/htmlpurifier/library/HTMLPurifier.auto.php';

    $purificateur = new HTMLPurifier();
    $html_propre = $purificateur->purify($html_salle);
?>

Sinon, utilisez:

<?php
    require_once '/path/to/htmlpurifier/library/HTMLPurifier.auto.php';

    $config = HTMLPurifier_Config::createDefault();
    $config->set('Core', 'Encoding', 'ISO-8859-1'); //remplacez avec votre encoding
    $config->set('Core', 'XHTML', true); //remplacez avec false si HTML 4.01
    $purificateur = new HTMLPurifier($config);

    $html_propre = $purificateur->purify($html_salle);
?>

    vim: et sw=4 sts=4
