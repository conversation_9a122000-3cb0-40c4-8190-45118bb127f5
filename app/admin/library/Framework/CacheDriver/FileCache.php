<?php
/**
 * Created by PhpStorm.
 * User: zlx66
 * Date: 2016/6/24/024
 * Time: 17:27
 */

namespace Framework\CacheDriver;
use  Framework\CacheDriver\DataFormat;
require_once 'CacheInterFace.php';
class FileCache implements CacheInterface
{
    /**
     * The Filesystem instance.
     */
    protected $files;

    /**
     * The file cache directory.
     *
     * @var string
     */
    protected $directory;

    /**
     * Create a new file cache store instance.
     * FileCache constructor.
     */
    public function __construct()
    {
        $this->directory = APP_ROOT.DIRECTORY_SEPARATOR.'tmp'.DIRECTORY_SEPARATOR.'cache'.DIRECTORY_SEPARATOR.'FileCache';
    }

    /**
     * Retrieve an item from the cache by key.
     *
     * @param  string  $key
     * @return mixed
     */
    public function get($key)
    {
        $data = NULL;

        $file = $this->path($key);
        if(file_exists($file)){
            $_content = file_get_contents($file);
            $expire = substr($_content, 0,10);

            if($expire < time()){
                $this->forget($key);
            }else{
                $data = $this->unSerialize(str_replace($expire,'',$_content));
            }
        }

        return $data;

    }

    /**
     * Store an item in the cache for a given number of minutes.
     *
     * @param  string  $key
     * @param  mixed   $value
     * @param  int     $seconds
     * @return void
     */
    public function put($key, $value, $seconds)
    {
        $value = $this->expiration($seconds).$this->serialize($value);

        $this->createCacheDirectory($path = $this->path($key));

        file_put_contents($path, $value);
    }

    /**
     * Create the file cache directory if necessary.
     *
     * @param  string  $path
     * @return void
     */
    protected function createCacheDirectory($path)
    {
        if (! file_exists(dirname($path))) {
            @mkdir(dirname($path), 0777, true);
        }
    }

    /**
     * Store an item in the cache indefinitely.
     *
     * @param  string  $key
     * @param  mixed   $value
     * @return void
     */
    public function forever($key, $value)
    {
        $this->put($key, $value, 0);
    }

    /**
     * Remove an item from the cache.
     *
     * @param  string  $key
     * @return bool
     */
    public function forget($key)
    {
        $file = $this->path($key);

        if (file_exists($file)) {
            return @unlink($file);
        }

        return TRUE;
    }


    /**
     * Get the full path for the given cache key.
     *
     * @param  string  $key
     * @return string
     */
    protected function path($key)
    {
        $parts = array_slice(str_split($hash = md5($key), 2), 0, 2);

        return $this->directory.DIRECTORY_SEPARATOR.implode(DIRECTORY_SEPARATOR, $parts).DIRECTORY_SEPARATOR.$hash;
    }

    /**
     * Get the expiration time based on the given minutes.
     *
     * @param  int  $seconds
     * @return int
     */
    protected function expiration($seconds)
    {
        $time = time() + $seconds;

        if ($seconds === 0 || $time > 9999999999) {
            return 9999999999;
        }

        return $time;
    }

    /**
     * Get the working directory of the cache.
     *
     * @return string
     */
    public function getDirectory()
    {
        return $this->directory;
    }

    /**
     * Get the cache key prefix.
     *
     * @return string
     */
    public function getPrefix()
    {
        return '';
    }

    /**
     * @param $content
     * @return string
     */
    private function serialize($content)
    {
        return DataFormat::enCode($content);
    }

    /**
     * @param $content
     * @return mixed
     */
    private function unSerialize($content)
    {
        return DataFormat::deCode($content);
    }

    public function add($key, $value, $seconds = 0)
    {

    }

    public function setPrefix($prefix)
    {
        // TODO: Implement setPrefix() method.
    }

    public function tag($tagName)
    {
        // TODO: Implement tag() method.
    }

    public function getAll()
    {
        // TODO: Implement getAll() method.
    }

    public function getCacheInstance()
    {
        // TODO: Implement getCacheInstance() method.
    }


    public function close()
    {

    }
}