<?php
/**
 * Created by PhpStorm.
 * User: zlx66
 * Date: 2016/6/24/024
 * Time: 17:16
 */

namespace Framework\CacheDriver;
use Framework\Config;
use Framework\CacheDriver\DataFormat;

require_once 'CacheInterFace.php';

class AliMemcached implements CacheInterface
{
    public $memcached = NULL;

    public $prefix = '';

    protected $cacheConfig = NULL;

    public function __construct($type = '')
    {
        $this->cacheConfig = !$this->cacheConfig ? Config::get('cache.AliMemcached') : $this->cacheConfig;
        $this->prefix = !$this->prefix ? Config::get('cache.prefix') : $this->prefix;

        if (!$this->cacheConfig) {
            throw new \RuntimeException('AliMemcached config does not exist!', 100);
        }
        if (!class_exists('Memcached')) {
            throw new \RuntimeException('Memcached Driver does not exists!', 101);
        }

        if (!$this->memcached) {
            $this->memcached = new \Memcached('youPinMemcached');
            $this->memcached->setOption(\Memcached::OPT_COMPRESSION, FALSE);
            $this->memcached->setOption(\Memcached::OPT_BINARY_PROTOCOL, TRUE);
            $this->memcached->addServer($this->cacheConfig['host'], $this->cacheConfig['port']);
            if ($this->cacheConfig['user'] && $this->cacheConfig['password']) {
                $this->memcached->setSaslAuthData($this->cacheConfig['user'], $this->cacheConfig['password']);
            }
        }
    }

    /**
     * Retrieve an item from the cache by key.
     *
     * @param  string $key
     * @return mixed
     */
    public function get($key)
    {
        $key = $this->getCacheKey($key);
        $value = $this->memcached->get($key);

        $value = $this->deCode($value);

        if ($this->memcached->getResultCode() == 0) {
            return $value;
        }

        return NULL;
    }

    /**
     * Store an item in the cache for a given number of minutes.
     *
     * @param  string $key
     * @param  mixed $value
     * @param  int $seconds
     * @return void
     */
    public function put($key, $value, $seconds = 0)
    {
        $key = $this->getCacheKey($key);
        $value = $this->enCode($value);

        if (intval($seconds) > 0) {
            $this->memcached->set($key, $value, $seconds + time());
        } else {
            $this->memcached->set($key, $value, 0);
        }
    }

    /**
     * Store an item in the cache if the key doesn't exist.
     *
     * @param  string $key
     * @param  mixed $value
     * @param  int $seconds
     * @return bool
     */
    public function add($key, $value, $seconds)
    {
        $key = $this->getCacheKey($key);
        $value = $this->enCode($value);

        return $this->memcached->add($key, $value, $seconds + time());
    }

    /**
     * Store an item in the cache indefinitely.
     *
     * @param  string $key
     * @param  mixed $value
     * @return void
     */
    public function forever($key, $value)
    {
        $key = $this->getCacheKey($key);
        $value = $this->enCode($value);

        $this->put($key, $value, 0);
    }

    /**
     * Remove an item from the cache.
     *
     * @param  string $key
     * @return bool
     */
    public function forget($key)
    {
        $key = $this->getCacheKey($key);

        return $this->memcached->delete($key);
    }

    /**
     * @return mixed
     */
    public function getCacheInstance()
    {
        return $this->memcached;
    }

    /**
     * Get the cache key prefix.
     *
     * @return string
     */
    public function getPrefix()
    {
        return $this->prefix;
    }

    /**
     * Set the cache key prefix.
     *
     * @param  string $prefix
     * @return void
     */
    public function setPrefix($prefix)
    {
        $this->prefix = !empty($prefix) ? $prefix . ':' : '';
    }

    private function getCacheKey($key)
    {
        return md5($this->prefix . $key);
    }

    public function tag($tagName)
    {
    }

    public function getAll()
    {

    }

    private function enCode($value)
    {
        return DataFormat::enCode($value);
    }

    private function deCode($value)
    {
        return DataFormat::deCode($value);
    }

    public function close()
    {

    }

}