<?php
/**
 * Created by PhpStorm.
 * User: zlx66
 * Date: 2016/6/24/024
 * Time: 15:46
 */

namespace Framework\CacheDriver;

interface CacheInterface
{

    /**
     * Store an item in the cache for a given number of seconds.
     * @param $key
     * @param $value
     * @param $seconds
     * @return mixed
     */
    public function put($key,$value,$seconds);

    /**
     * Retrieve an item from the cache by key.
     * @param $key
     * @return mixed
     */
    public function get($key);

    /**
     * Store an item in the cache indefinitely.
     * @param $key
     * @param $value
     * @return mixed
     */
    public function forever($key,$value);

    /**
     * Remove an item from the cache.
     * @param $key
     * @return void
     */
    public function forget($key);

    /**
     * Store an item in the cache if the key doesn't exist.
     * @param $key
     * @param $value
     * @param $seconds
     * @return mixed
     */
    public function add($key,$value,$seconds);

    /**
     * Set the cache key prefix.
     * @param $prefix
     * @return mixed
     */
    public function setPrefix($prefix);

    /**
     * Get the cache key prefix.
     * @return mixed
     */
    public function getPrefix();

    /**
     * get current cacheObject
     * @return mixed
     */
    public function getCacheInstance();

    /**
     * 缓存分组，仅支持redis
     * @param $tagName
     * @return mixed
     */
    public function tag($tagName);

    public function getAll();

    public function close();

    /**
     * 队列订阅，仅支持redis
     * @param $channel
     * @param callable|NULL $callBack
     * @return mixed
     */
//    public function subscribe($channel, callable $callBack=NULL);

    /**
     * 发表内容到某一个通道，仅支持redis
     * @param $channel
     * @param $message
     */
//    public function publish($channel, $message);

    /**
     * @param $key
     * @param $value
     * @return mixed
     */
//    public function lPush($key, $value);

    /**
     * @param $key
     * @param $value
     * @return mixed
     */
//    public function rPush($key, $value);

    /**
     * 输出名称为key的list左(头)起的第一个元素，删除该元素
     * @param $key
     * @return mixed
     */
//    public function lPop($key);

    /**
     * 输出名称为key的list右(尾)起的第一个元素，删除该元素
     * @param $key
     * @return mixed
     */
//    public function rPop($key);

//    public function lSize();

}