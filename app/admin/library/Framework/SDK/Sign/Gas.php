<?php
/**
 * 撬装平台签名规则.
 * User: zlx66
 * Date: 2016/5/25/025
 * Time: 13:46
 */

namespace Framework\SDK\Sign;
use Framework\Log;
use Framework\SDK\Sign\SignInterface;
use Framework\Config as Config;

class Gas implements SignInterface
{
    static public function createSign(array $postData)
    {
        $params['data'] = json_encode($postData['data']);
        $params['app_key'] = Config::get('gas.app_key');
        $params['timestamp'] = date("Y-m-d H:i:s");
        $params['method'] = $postData['method'];
        ksort($params);
        $postStr = \helper::createLinkString($params);
        //Log::error('createSign:'.$postStr,[],'createSign');
        $params['sign'] = self::md5Sign($postStr);
        //Log::error('sign:'.$params['sign'],[],'createSign');
        return $params;
    }

    /**
     * 除去数组中的空值和签名参数
     * @param $para
     * @return array
     */
    static protected function paraFilter($para)
    {
        $para_filter = [];
        while (list ($key, $val) = each($para)) {
            if (in_array($key, ['method', 'app_key', 'timestamp', 'data'])) {
                $para_filter[$key] = $val;
            }
        }

        return self::argSort($para_filter);
    }

    /**
     * 对数组排序
     * @param $para
     * @return mixed
     */
    static protected function argSort($para)
    {
        ksort($para);
        reset($para);

        return $para;
    }

    /**
     * 签名字符串
     * @param $preStr
     * @return string
     */
    static protected function md5Sign($preStr)
    {
        $key = Config::get('gas.app_secret');
        $preStr = $key . $preStr . $key;

        //Log::error('$preStr:'.$preStr,[],'createSign');

        return strtoupper(md5($preStr));
    }
}