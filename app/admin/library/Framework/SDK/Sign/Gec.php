<?php
/**
 * 撬装平台签名规则.
 * User: zlx66
 * Date: 2016/5/25/025
 * Time: 13:46
 */

namespace Framework\SDK\Sign;
use Framework\Cache;
use Framework\Config as Config;
use GuzzleHttp\Client;

class Gec implements SignInterface
{
    static public function createSign(array $postData)
    {
        $params['pay_req'] = $postData['data'];
        $params['token'] = self::getToken();

        return $params;
    }

    /**
     * 除去数组中的空值和签名参数
     * @param $para
     * @return array
     */
    static protected function getToken()
    {
        $cacheKey = "GecTokenCache";
        $username = Config::get('gec.username');
        $password = Config::get('gec.password');
        $url = Config::get('gec.apiUrl');
        $url .= "autocrud/cux.CUX8000.cux_interface_get_token/execute";

        $hasToken = Cache::get($cacheKey);
        if($hasToken){
            return  $hasToken;
        }

        $httpRequest = new Client();

        $response = $httpRequest->request('POST', $url, [
            'form_params' => [
                'username' => $username,
                'password' => $password
            ]
        ]);

        $resContent = $response->getBody()->getContents();

        $data = $resContent ? \GuzzleHttp\json_decode($resContent) : TRUE;

        if($data && $data->result && $data->result->token){
            $token = $data->result->token;
            Cache::put($cacheKey, $token,60);
            return $token;
        }else{
            \Framework\Log::error('获取Token异常:'.\GuzzleHttp\json_encode($data,true), [],'gecClient');
            throw new \RuntimeException("请求Gec获取Token异常",2);
        }
    }

}