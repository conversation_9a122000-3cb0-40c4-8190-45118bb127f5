<?php
/**
 * 撬装平台签名规则.
 * User: zlx66
 * Date: 2016/5/25/025
 * Time: 13:46
 */

namespace Framework\SDK\Sign;
use Framework\Log;
use Framework\SDK\Sign\SignInterface;
use Framework\Config as Config;

class Crm implements SignInterface
{
    static public function createSign(array $postData)
    {
        $params = $postData['data'];
        $params['accessid'] = Config::get('crm.app_key');
        $params['timestamp'] = strval(time());//'1569411035';//time();
        ksort($params);
        $postStr = strtoupper(MD5(\GuzzleHttp\json_encode($params,JSON_UNESCAPED_UNICODE)));
        Log::error('createSign:'.$postStr,[],'createSign');
        $params['sign'] = self::md5Sign($postStr);
        Log::error('sign:'.$params['sign'],[],'createSign');

        return $params;
    }

    /**
     * 除去数组中的空值和签名参数
     * @param $para
     * @return array
     */
    static protected function paraFilter($para)
    {
        $para_filter = [];
        while (list ($key, $val) = each($para)) {
            if (in_array($key, ['method', 'app_key', 'timestamp', 'data'])) {
                $para_filter[$key] = $val;
            }
        }

        return self::argSort($para_filter);
    }

    /**
     * 对数组排序
     * @param $para
     * @return mixed
     */
    static protected function argSort($para)
    {
        ksort($para);
        reset($para);

        return $para;
    }

    /**
     * 签名字符串
     * @param $preStr
     * @return string
     */
    static protected function md5Sign($preStr)
    {
        $key = Config::get('crm.app_secret');

        $sign = urlencode(bin2hex(hash_hmac('sha256', $preStr, $key,TRUE)));

        //Log::error('$sign:'.$sign,[],'createSign');

        return strtoupper($sign);
    }
}