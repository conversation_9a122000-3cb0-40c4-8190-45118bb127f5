<?php
/**
 * Created by PhpStorm.
 * User: zlx66
 * Date: 2016/9/27/027
 * Time: 11:38
 */

namespace Framework\SDK\EWei;

use \Fuel\Request\EweiClient as EWeiClient;

class workOrder extends EWeiAbstract
{
    /**
     * 查看工单
     * @param array $params
     * @return mixed
     */
    public function show(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        $id = $params['id'];
        unset($params['id']);

        return EweiClient::get([
            'method' => 'tickets/' . $id,
            'data'   => $params
        ]);
    }


    /**
     * 查看多个工单
     * @param array $params
     * @return mixed
     */
    public function showMany(array $params)
    {
        \helper::argumentCheck(['ids'], $params);
        if (!is_array($params['ids'])) {
            throw new \RuntimeException('ids参数为以逗号分割的字符串', 2);
        }

        return EweiClient::get([
            'method' => 'tickets/show_many',
            'data'   => $params
        ]);
    }

    /*
     * 
     */
    public function multiCondition(array $params)
    {
        return EweiClient::get([
            'method' => 'tickets/multi_condition',
            'data'   => $params
        ]);
    }

    /**
     * 客户工单列表
     * @param array $params
     * @return mixed
     */
    public function customerOrderList(array $params)
    {
        \helper::argumentCheck(['customer_id'], $params);

        $customer_id = $params['customer_id'];
        unset($params['customer_id']);

        return EweiClient::get([
            'method' => 'customers/' . $customer_id . '/tickets',
            'data'   => $params
        ]);
    }

    /**
     * 客服工单列表
     * @param array $params
     * @return mixed
     */
    public function engineersOrderList(array $params)
    {
        \helper::argumentCheck(['engineer_id'], $params);

        $engineer_id = $params['engineer_id'];
        unset($params['engineer_id']);

        return EweiClient::get([
            'method' => 'engineers/' . $engineer_id . '/tickets',
            'data'   => $params
        ]);
    }

    /**
     * 创建工单
     * @param array $params
     * @return mixed
     */
    public function create(array $params)
    {
        \helper::argumentCheck(['subject', 'status', 'requester', 'serviceDesk'], $params);

        $fillAble = [
            'subject', 'description', 'descriptionAttachments', 'status', 'priority', 'ticketType', 'serviceCatalog', 'requester', 'serviceDesk', 'engineer', 'tags', 'ccs', 'fromIp', 'via', 'user', 'workflowTemplate', 'ticketCustomFields'
        ];

        $apiParams = [];
        foreach ($params as $k => $v) {
            if (in_array($k, $fillAble)) {
                $apiParams[$k] = $v;
            }
        }

        return EweiClient::post([
            'method'    => 'tickets',
            'data'      => $apiParams,
            'data_from' => isset($params['data_from']) ? $params['data_from'] : 1
        ]);
    }

    /**
     * 修改工单
     * @param array $params
     * @return mixed
     */
    public function edit(array $params)
    {
        \helper::argumentCheck(['engineer_id'], $params);

        $id = $params['id'];
        unset($params['id']);

        $fillAble = [
            'subject', 'description', 'priority', 'ticketType', 'serviceCatalog', 'requester', 'fromIp', 'via', 'tags', 'ticketCustomFields'
        ];

        $apiParams = [];
        foreach ($params as $k => $v) {
            if (in_array($k, $fillAble)) {
                $apiParams[$k] = $v;
            }
        }

        return EweiClient::put([
            'method' => 'tickets/' . $id,
            'data'   => $params
        ]);
    }

    /**
     * 删除工单
     * @param array $params
     * @return mixed
     */
    public function delete(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return EweiClient::delete([
            'method' => 'tickets/' . $params['id'],
            'data'   => []
        ]);
    }

    /**
     * 修改工单处理人
     * @param array $params
     * @return mixed
     */
    public function editEngineer(array $params)
    {
        \Framework\Log::dataLog(var_export($params,true),'eWeiEditEngineerParams');
        \helper::argumentCheck(['ticket_id', 'service_desk_id', 'engineer_id'], $params);

        $ticket_id = $params['ticket_id'];
        unset($params['ticket_id']);

        return EweiClient::put([
            'method' => 'tickets/' . $ticket_id . '/engineer',
            'data'   => $params
        ]);
    }

    /**
     * 修改工单客服组
     * @param array $params
     * @return mixed
     */
    public function editEngineerGroup(array $params)
    {
        \helper::argumentCheck(['ticket_id', 'service_desk_id'], $params);

        $ticket_id = $params['ticket_id'];
        unset($params['ticket_id']);

        return EweiClient::put([
            'method' => 'tickets/' . $ticket_id . '/service_desk',
            'data'   => $params
        ]);
    }
}