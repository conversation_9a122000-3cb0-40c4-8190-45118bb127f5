<?php
/**
 * Created by PhpStorm.
 * User: zlx66
 * Date: 2016/5/24/024
 * Time: 20:36
 */

namespace Framework;


class Config
{
    static protected $configPool = [];
    /**
     * 获取配置文件配置
     * @param $key
     * @param $flag bool 只取一级
     * @return mixed|null
     */
    static public function get($key)
    {
        $keyArr = explode(".",$key);

        if(!isset(self::$configPool[$keyArr[0]])){
            $configFile = APP_CONFIG.DIRECTORY_SEPARATOR.$keyArr[0].'.php';
            if(file_exists($configFile)){
                $config = require $configFile;
                self::$configPool[$keyArr[0]] = $config;
            }
        }

        $configContent = self::$configPool[$keyArr[0]];

        for($i=1;$i<count($keyArr);$i++){
            $configContent = $configContent[$keyArr[$i]];
        }

        return $configContent;
    }

    /**
     * config compiler
     * <AUTHOR>
     * @since ${DATE}
     */
    static public function loadAllConfig()
    {
        foreach(scandir(APP_CONFIG) as $v){
            $ext = pathinfo($v);
            if($v != '.' && $v != '..' &&  $ext['extension'] != 'conf' && $ext['filename'] != 'config'){
                self::$configPool[$ext['filename']] = require APP_CONFIG.DIRECTORY_SEPARATOR.$v;
            }
        }
    }
}