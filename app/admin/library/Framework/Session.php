<?php
/**
 * Created by PhpStorm.
 * User: zlx66
 * Date: 2016/6/24/024
 * Time: 14:28
 */

namespace Framework;

use Framework\Cache;
use Framework\SessionDriver\PhpDriver;
use Framework\SessionDriver\CacheDriver;

class Session
{
    static private $sessionInstance;

    static private function getSessionInstance()
    {
        $sessionType = Config::get('session.driver');
        $_sessionType = '\Framework\SessionDriver\\'.$sessionType.'Driver';
//        $_sessionType = str_replace('className',$sessionType.'Driver' , $_sessionType);

        if(!class_exists($_sessionType)){
            throw new \RuntimeException($sessionType.' Driver does not supported!',3);
        }

        if(!self::$sessionInstance){
            self::$sessionInstance = new $_sessionType();
        }

        return self::$sessionInstance;
    }

    /**
     * 设置session
     * @param $key
     * @param $value
     */
    static public function put($key,$value, $sessionId=NULL)
    {
        return self::getSessionInstance()->put($key,$value,$sessionId);
    }

    /**
     * 获取session
     * @param $key
     * @return mixed
     */
    static public function get($key)
    {
        return self::getSessionInstance()->get($key);
    }

    /**
     * 清除session
     * @param $key
     */
    static public function forget($key)
    {
        return self::getSessionInstance()->forget($key);
    }

    static public function getBySessionId($key)
    {
        return self::getSessionInstance()->getBySessionId($key);
    }

}