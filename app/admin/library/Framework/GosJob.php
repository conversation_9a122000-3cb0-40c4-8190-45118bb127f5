<?php
/**
 * Created by PhpStorm.
 * @example
 * 请在对象方法下使用
 * (new \Framework\Job())
 * ->setTaskName($taskName)
 * ->pushTask(function () {
 *
 * })
 * ->channel('default')
 * ->delay(300)
 * ->tries(3)
 * ->exec();
 * User: zlx66
 * Date: 2016/8/5/005
 * Time: 15:00
 */

namespace Framework;

use Fuel\Service\GosTask;

class GosJob implements JobInterface
{
    protected $taskName = '';

    protected $closure;

    protected $channel = 'default';

    protected $tries = 0;

    protected $afterTime = 0;

    static protected $sleep = 3;

    protected $data;

    /**
     * 设置任务逻辑
     * @param callable $callBack
     * @return $this
     */
    public function pushTask(callable $callBack)
    {
        try{
            $this->data = $callBack();
        }catch (\Exception $e){

        }

        return $this;
    }

    /**
     * 设置队列通道
     * @param $channel
     * @return $this
     */
    public function channel($channel)
    {
        $this->channel = $channel;

        return $this;
    }

    /**
     * 最多允许尝试几次
     * @param $tries
     * @return $this
     */
    public function tries($tries = 0)
    {
        $this->tries = intval($tries);

        return $this;
    }

    /**
     * 延时多少秒执行
     * @param $delay
     * @return $this
     */
    public function delay($delay = 0)
    {
        $delay = intval($delay);
        if ($delay > 0) {
            $this->afterTime = time() + $delay;
        }

        return $this;
    }

    /**
     * 设置任务名称
     * @param $taskName
     * @return $this
     */
    public function setTaskName($taskName)
    {
        $this->taskName = $taskName;

        return $this;
    }

    /**
     * 执行
     * @return mixed|null
     */
    public function exec()
    {
        return $this->data;
    }

}