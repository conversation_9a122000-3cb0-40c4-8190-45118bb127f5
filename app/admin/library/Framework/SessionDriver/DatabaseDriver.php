<?php
/**
 * Created by PhpStorm.
 * User: zlx66
 * Date: 2016/6/29/029
 * Time: 14:02
 */

namespace Framework\SessionDriver;
use Models\OilSession as OilSession;
use \Framework\Config as Config;

class DatabaseDriver
{
    public function put($key, $value)
    {
        if(!$key){
            return null;
        }
        $sessionConfig = Config::get('session');
        return OilSession::add([
            'session_id'    =>  session_id(),
            'hash'  =>  md5($key),
            'data'  =>  json_encode($value),
            'expiretime'    =>  date("Y-m-d H:i:s", time() + $sessionConfig['expire'])
        ]);
    }

    public function get($key)
    {
        $data = NULL;
        $session = OilSession::getByHash(md5($key));


        if(isset($session->data) && $session->data){
            $data = json_decode($session->data);
        }

        return $data;
    }

    public function getBySessionId($key)
    {
        $data = NULL;
        $session = OilSession::getBySessionId($key);


        if(isset($session->data) && $session->data){
            $data = json_decode($session->data);
        }

        return $data;
    }

    public function forget($key)
    {
        OilSession::where('session_id','=',session_id())->delete();
        return OilSession::remove(['ids'=>[$key]]);
    }
}