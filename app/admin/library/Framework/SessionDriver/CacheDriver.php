<?php
/**
 * Created by PhpStorm.
 * User: zlx66
 * Date: 2016/6/29/029
 * Time: 14:04
 */

namespace Framework\SessionDriver;


use Framework\Cache;

class CacheDriver
{
    public function put($key, $value)
    {
        $key = self::getSessionKey($key);

        if (!$key) {
            return NULL;
        }

        Cache::put($key, $value, 86400*30*12);
    }

    public function get($key)
    {
        $key = self::getSessionKey($key);

        if (!$key) {
            return NULL;
        }

        return Cache::get($key);
    }

    public function forget($key)
    {
        $key = self::getSessionKey($key);

        if (!$key) {
            return NULL;
        }

        Cache::forget($key);
    }

    static private function getSessionKey($key)
    {
        $commonModel = new \commonModel();
        $cookieKey = $commonModel->cookieUid();

        return $cookieKey;
    }

    static public function getBySessionId($key)
    {
        return self::get($key);
    }
}