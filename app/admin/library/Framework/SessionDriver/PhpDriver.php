<?php
/**
 * Created by PhpStorm.
 * User: zlx66
 * Date: 2016/6/29/029
 * Time: 14:02
 */

namespace Framework\SessionDriver;


class PhpDriver
{
    public function put($key,$value)
    {
        return $_SESSION[md5($key)] = $value;
    }

    public function get($key)
    {
        return $_SESSION[md5($key)];
    }

    public function getBySessionId($key)
    {
        return $_SESSION[md5($key)];
    }

    public function forget($key)
    {
        unset($_SESSION[md5($key)]);
    }
}