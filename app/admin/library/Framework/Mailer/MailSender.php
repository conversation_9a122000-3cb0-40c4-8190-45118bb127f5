<?php
/**
 * Created by <PERSON><PERSON>Storm.
 * User: Kevin
 * Date: 2016/1/11/011
 * Time: 15:58
 */

namespace Framework\Mailer;

use Framework\DingTalk\DingTalkAlarm;
use Nette\Mail\Message;
use Nette\Mail\SmtpMailer;
use Framework\Job;
use Framework\Log;


/**
 * Class MailSender
 *
 * @package Framework\Mailer
 * @example
 * use Nette\Mail\Message;
 * use Framework\Mailer\MailSender;
 * $mail = new Message;
 * $mail->addTo('<EMAIL>')
 * ->addTo('<EMAIL>')
 * ->setSubject('Order Confirmation')
 * ->setBody("Hello, Your order has been accepted.")// setHTMLBody('<b>Sample HTML</b> <img src="background.gif">');
 * ->addAttachment('example.zip');
 *
 */
class MailSender
{
    static public function send(Message $mail)
    {
        if (!$mail) {
            return false;
        }

        global $app;

        $mail->setFrom($app->config->email->from, $app->config->email->fromName);

        $mailer = new SmtpMailer(array(
            'host'     => $app->config->email->host,
            'username' => $app->config->email->from,
            'password' => $app->config->email->password,
            'port'     => $app->config->email->Port,
            'secure'   => $app->config->email->SMTPSecure,
        ));

        try {
            $mailer->send($mail);
        } catch (\Exception $e) {
            Log::error('Framework\Mailer -- ', [strval($e)], 'Error');
        }

        return true;
    }

    /**
     * @param string $title
     * @param        $content
     * @param        $mailList
     * @param bool   $isHtml
     */
    static public function sendEmail($title = '', $content, $mailList, $isHtml = FALSE)
    {
        $mailQueue = new MailSender();
        $mailQueue->sendMailQueue($title, $content, $mailList, $isHtml);
    }


    /**
     * 队列发送
     *
     * @param string $title
     * @param        $content
     * @param        $mailList
     * @param bool   $isHtml
     */
    public function sendMailQueue($title = '', $content, $mailList, $isHtml = FALSE)
    {
        global $app;

//        (new DingTalkAlarm())->alarmToGroup($content);

        $params = [
            'title'    => $app->config->api_env . ' - ' . $title,
            'content'  => $content,
            'mailList' => $mailList,
            'isHtml'   => $isHtml
        ];

        global $app;
        if ( $app->config->scheduler->switch == 1 ) {
            return (new \Jobs\SendAlarmEmailJob($params))
                ->setTaskName('发送报警邮件')
                ->onQueue('sendAlarmMail')
                ->setTries(3)
                ->dispatch();
        }else {

            (new Job())
                ->setTaskName('sendAlarmMail')
                ->pushTask(function ($taskInfo) use ($params) {
                    try {
                        global $app;

                        $mail = new Message;
                        if (is_array($params['mailList']) && $params['mailList']) {
                            foreach ($params['mailList'] as $v) {
                                $mail->addTo($v);
                            }
                        }

                        $mail->setSubject($params['title']);

                        if ($params['isHtml']) {
                            $mail->setHTMLBody($params['content']);
                        } else {
                            $mail->setBody($params['content']);
                        }


                        $mail->setFrom($app->config->email->from, $app->config->email->fromName);

                        $mailer = new SmtpMailer(array(
                            'host' => $app->config->email->host,
                            'username' => $app->config->email->from,
                            'password' => $app->config->email->password,
                            'port' => $app->config->email->Port,
                            'secure' => $app->config->email->SMTPSecure,
                        ));

                        try {
                            $mailer->send($mail);
                        } catch (\Exception $e) {
                            Log::error('Framework\Mailer ', [strval($e)], 'Error');
                        }

                        return true;
                    } catch (\Exception $e) {
                        Log::error('Framework\Mailer', [strval($e)], 'Error');
                    }
                })
                ->channel('default')
                ->tries(1)
                ->exec();
        }
    }

    /**
     * @param string $title
     * @param        $content
     * @param        $mailList
     * @param bool   $isHtml
     * @param bool   $isAttach
     * @param array  $filePath
     * @param bool   $showEnv
     * @return bool|void
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    static public function sendNow($title = '', $content, $mailList, $isHtml = FALSE, $isAttach = false, array $filePath = [], $showEnv = true)
    {
        global $app;

        $title = $showEnv ? $app->config->api_env . ' - ' . $title : $title;

        $params = [
            'title'    => $title,
            'content'  => $content,
            'mailList' => $mailList,
            'isHtml'   => $isHtml
        ];

        try {
            global $app;

            $mail = new Message;
            if (is_array($params['mailList']) && $params['mailList']) {
                foreach ($params['mailList'] as $v) {
                    $mail->addTo($v);
                }
            }

            $mail->setSubject($params['title']);

            if ($params['isHtml']) {
                $mail->setHTMLBody($params['content']);
            } else {
                $mail->setBody($params['content']);
            }

            $mail->setFrom($app->config->email->from, $app->config->email->fromName);

            if ($isAttach) {
                foreach ($filePath as $file) {
                    $mail->addAttachment($file);
                }
            }

            $configs = array(
                'host'     => $app->config->email->host,
                'username' => $app->config->email->from,
                'password' => $app->config->email->password,
                'port'     => $app->config->email->Port,
            );
            if ($app->config->email->SMTPSecure) {
                $configs['secure'] = $app->config->email->SMTPSecure;
            }

            $mailer = new SmtpMailer($configs);

            try {
                return $mailer->send($mail);
            } catch (\Exception $e) {
                try {
                    (new DingTalkAlarm())->alarmToGroup("发送邮件异常", $title, [], TRUE, TRUE);
                } catch (\Exception $e) {
                    Log::error('DingTalk-fail' . $e->getMessage(), [strval($e)], 'Error');
                }
                Log::error('Framework\Mailer ', [strval($e)], 'Error');
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Framework\Mailer', [strval($e)], 'Error');
        }
    }
}