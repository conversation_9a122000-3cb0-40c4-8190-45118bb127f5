<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2015/11/25/025
 * Time: 11:16
 */

namespace Framework\Sms;

use Framework\Log;

class DingTalk implements Sms
{
    /**
     * 钉钉通知发送
     * @param $args
     * @return string
     */
    static public function send($args)
    {
        try {
            return self::postNew(
                [
                    'app_key'    => DINGTALK_KEY,
                    'app_secret' => DINGTALK_SECRET,
                    'mobiles'    => $args['phone'],
                    'content'    => $args['message'] . "\n运行环境：".API_ENV,
                    'channel'    => 'dingtalk',
                ]
            );
        } catch (\Exception $e) {
            echo strval($e);
            Log::error(strval($e), [], 'SmsSendError');
        }
    }

    static public function post($params)
    {
        $paramArr = [
            'method'    => 'huoyunren.sms.sender',
            'userId'    => 1000,
            'content'   => urlencode($params['content']),
            'mobiles'   => urlencode($params['mobiles']),
            'channel'   => isset($params['channel']) && $params['channel'] ? $params['channel'] : 'modem',
            'format'    => 'json',
            'app_key'   => $params['app_key'],
            'timestamp' => urlencode(date("Y-m-d H:i:s")),

        ];
        $sign = \Util::createSign($paramArr, $params['app_secret']); //生成签名
        $paramArr['sign'] = $sign;

        $urlArr = [];
        foreach ($paramArr as $k => $v) {
            $urlArr[] = $k . '=' . $v;
        }

        $urlStr = "http://smsservice.huoyunren.com/router/rest?" . implode('&', $urlArr);

        $result = NULL;
        try {
            $result = file_get_contents($urlStr);
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        return $result;
    }

    //发送钉钉,请求dsp调整新的签名方式
    static public function postNew($params){
        $msgObj = new \stdClass();
        $msgObj->content = $params['content'];
        $msgObj->title = $params['title'] ? $params['title'] : "标题";

        $timestamp = time() * 1000;
        $paramArr = [
            'mobiles'   => $params['mobiles'],
            'access_key'=> $params['app_key'],
            'timestamp' => $timestamp,
            'json_ext'  => $msgObj,
            'access_secret' => $params['app_secret'],

        ];

        //生成签名
        ksort($paramArr);
        $pcontent = json_encode($paramArr, JSON_UNESCAPED_UNICODE);
        $sign =  md5($pcontent);
        $paramArr['sign'] = $sign;
        unset($paramArr['access_secret']);

        $result = NULL;
        try {
            $result = self::http_post_data(DINGTALK_HOST,$paramArr);
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        return $result;
    }
    static public function http_post_data($url, $data)
    {
        $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json; charset=utf-8',
                'Content-Length: ' . strlen($data))
        );
        ob_start();
        curl_exec($ch);
        $return_content = ob_get_contents();
        ob_end_clean();
        $return_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        return $return_content;
    }
}