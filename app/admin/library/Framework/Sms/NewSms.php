<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2015/11/25/025
 * Time: 11:16
 */

namespace Framework\Sms;

use Framework\Config;
use Framework\Log;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\RequestException;
use UCenterSDK\UCenterAbstract;
use UCenterSDK\VegaSms;

class NewSms implements Sms
{
    /**
     * 对接成都新短信通道
     * @param $args
     * @return string
     */
    static public function send($params)
    {
        \helper::argumentCheck(['mobiles',"content"], $params);
        //接收短信手机号码，多个手机号码以英文逗号","分隔。最多支持100个手机号码
        if(is_array($params['mobiles'])){
            if(count($params['mobiles']) > 100){
                throw new RuntimeException('最多支持100个手机号码', 2);
            }
            $params['mobiles'] = implode(",",$params['mobiles']);
        }
        $params['operator'] = "FOSS雷庆";
        $params['timestamp'] = time() * 1000;
        if(!isset($params['path']) || empty($params['path'])){
            $params['path'] = "/message/general";
        }
        //$path = $params['path'] ? $params['path'] : '/message/general';
        $smsConfig = Config::get('sms.newSms');

        /*global $app;
        switch ($app->config->api_env) {
            case 'pro':
                $smsUrl = $smsConfig['onlineUrl'];
                break;
            default:
                $smsUrl = $smsConfig['url'];
        }
        $url  = $smsUrl.$path;*/
        $params['path'] = "/v1/sms-agent-server".$params['path'];
        $params['access_key'] = $smsConfig['sms_key'];
        $params['access_secret'] = $smsConfig['sms_secret'];
        $sign = self::createSign($params);
        $params['sign'] = $sign;
        unset($params['access_secret']);
        return self::vegatHttpRequest($params);
        //return self::curlPost($params,$url);
    }

    /**
     * 指定模版发送短信
     * @param $args
     * @return string
     */
    static public function sendSmsByTemplateId($params)
    {
        \helper::argumentCheck(['mobiles',"template_id","params"], $params);
        //接收短信手机号码，多个手机号码以英文逗号","分隔。最多支持100个手机号码
        if(is_array($params['mobiles'])){
            if(count($params['mobiles']) > 100){
                throw new RuntimeException('最多支持100个手机号码', 2);
            }
            $params['mobiles'] = implode(",",$params['mobiles']);
        }
        $params['operator'] = "FOSS雷庆";
        $params['timestamp'] = time() * 1000;
        if(!isset($params['path']) || empty($params['path'])){
            $params['path'] = "/message/vcode";
        }
        //$path = $params['path'] ? $params['path'] : '/message/general';
        $smsConfig = Config::get('sms.newSms');

        /*global $app;
        switch ($app->config->api_env) {
            case 'pro':
                $smsUrl = $smsConfig['onlineUrl'];
                break;
            default:
                $smsUrl = $smsConfig['url'];
        }
        $url  = $smsUrl.$path;*/
        $params['path'] = "/v1/sms-agent-server".$params['path'];
        $params['access_key'] = $smsConfig['sms_key'];
        $params['access_secret'] = $smsConfig['sms_secret'];
        $sign = self::createSign($params);
        $params['sign'] = $sign;
        unset($params['access_secret']);
        return self::vegatHttpRequest($params);
        //return self::curlPost($params,$url);
    }

    /**
     * @title   请求
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package UCenterSDK
     * @since
     * @params  type filedName required?
     * @return bool|mixed
     * @returns
     * []
     * @returns
     */
    static public function vegatHttpRequest($params,$method = "POST")
    {
        $vegaConfig = (object)Config::get('uCenter');
        $timestamp = time() * 1000;
        $path = $params['path'];
        unset($params['path']);
        $sign = self::vegaSign($vegaConfig->secretKey,$timestamp,$path,$method);

        $urlParams = [
            'accessid'  =>  $vegaConfig->accessId,
            'sign'      =>  $sign,
            'g7timestamp'   =>  $timestamp,
        ];
        $url = $vegaConfig->apiUrl .$path;
        try {
            $client = new Client();
            $_params = [
                'query' => $urlParams,
            ];
            if( isset($params['is_form']) && $params['is_form'] == 1 ){
                $_params['headers'] = array(
                    "Content-type: application/x-www-form-urlencoded;charset=UTF-8"
                );
                unset($params['is_form']);
                $_params['form_params'] = $params;
                $_params['query'] = array_merge($_params['query'],$params);
            }else{
                $_params['json'] = $params;
            }
            Log::error('参数-|', ['params'=>$_params,'url'=>$url], 'SmsSendError_');

            $response = $client->request($method, $url, $_params);

            if ($response->getStatusCode() !== 200) {
                throw new \RuntimeException($response->getBody(), $response->getStatusCode());
            }

            $resCode = $response->getStatusCode();
            $resContent = $response->getBody()->getContents();
            Log::error('返回结果-|' . $resCode . '--' . var_export($resContent, TRUE), [], 'SmsSendError_');

            $data = $resContent ? \GuzzleHttp\json_decode($resContent) : TRUE;

            if ($data->code != 0) {
                throw new \RuntimeException('gateway' . $data->msg, $data->code);
            }
            if ($data->sub_code != 0) {
                if ($data->sub_code != 12077) {
                    throw new \RuntimeException('error' . $data->sub_msg, $data->sub_code);
                }
            }

        } catch (RequestException $e) {
            Log::error('RequestException---|' . $e->getCode() . '--' . strval($e) . "| result:" . var_export($resContent, TRUE) .
                ' | params' . var_export($_params, TRUE), [], 'SmsSendError_');

            throw new \RuntimeException($e->getMessage(), $e->getCode());
        } catch (ClientException $e) {
            Log::error('RequestException---|' . $e->getCode() . '--' . strval($e) . "| result:" . var_export($resContent, TRUE) .
                ' | params' . var_export($_params, TRUE), [], 'SmsSendError_');

            throw new \RuntimeException($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            Log::error('RequestException---|' . $e->getCode() . '--' . strval($e) . "| result:" . var_export($resContent, TRUE) .
                ' | params' . var_export($_params, TRUE), [], 'SmsSendError_');

            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        return $data;
    }

    static public function vegaSign($secretKey,$timestamp,$apiPath,$method = "POST"){
        ini_set('time_zone','Asia/Shanghai');
        $string = $method. "\n" . $timestamp . "\n" . $apiPath;
        return base64_encode(hash_hmac('sha1',utf8_encode($string),$secretKey,true));
    }

    static public function curlPost($params,$url)
    {
        if(function_exists("curl_exec"))
        {
            $postData = json_encode($params, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            $headers = array(
                "Content-type: application/json;charset=UTF-8",
                'Content-Length: ' . strlen($postData),
            );
            Log::error("curlRes".var_export($postData,true).":url".$url,[],"SmsSendError_");
            try{
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL,$url);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch,CURLOPT_POSTFIELDS,$postData);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER,true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 300);
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                $response = curl_exec($ch);
                Log::error("curlRes".var_export($response,true),[],"SmsSendError_");
                if($response === false)
                    $response = curl_error($ch);
                $str =  curl_multi_getcontent ($ch);
                curl_close($ch);
                return $str;
            }catch (\Exception $e){
                Log::error(strval($e),true,true,'SmsSendError');
            }

        }else{
            Log::error('不支持curl_exec',true,true,'SmsSendError');
        }
    }

    //生成签名
    static public function createSign($paramArr)
    {
        ksort($paramArr);
        $content = json_encode($paramArr, JSON_UNESCAPED_UNICODE);
        $sign = md5($content);
        return $sign;
    }
}