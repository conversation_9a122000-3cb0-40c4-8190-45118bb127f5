<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2015/11/25/025
 * Time: 11:16
 */

namespace Framework\Sms;
use Framework\Sms\Sms;
require_once dirname(dirname(dirname(dirname(__FILE__)))).DIRECTORY_SEPARATOR.'module'.DIRECTORY_SEPARATOR.'client'.DIRECTORY_SEPARATOR.'model.php';

class DuanXinMao implements Sms
{
    /**
     * 掌上通短信发送
     * @param $args
     * @return string
     */
    static public function send($args)
    {
        try{
            $client = new \clientService();
//            return $client->sendSMS(array(
//                'app_key'   =>  GSPADMIN_KEY,
//                'app_secret'    => GSPADMIN_SECRET,
//                'username' => 'chinaway',
//                'mobiles' => $args->phone,
//                'content' => $args->message.' 【汇通天下】',
//                'channel' => 'modem',
//            ));
        }catch (\Exception $e){
            \Log::error(strval($e),true,true,'SmsSendError');
        }
    }
}