<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2015/11/25/025
 * Time: 11:12
 */

namespace Framework\Sms;
use Framework\Sms\Sms;

class QiXinTong implements Sms
{
    /**
     * 企信通短信发送
     * @param $args
     * @return string
     */
    static public function send($args)
    {
        $params = (object)$args;

        $date = date('ymdHi');
        $cpmid = guid32();
        $cpid = '010001000244';
        $port = '5656';
        $phone = (string)$params->phone;
        $message = $params->message."【汇通天下】";
        $signature = md5('6c1ecc122ca95acf49c183f2f41d3516'.$date);
        $xml_data = "<?xml version='1.0' encoding='UTF-8'?>
		<MtPacket>
		<cpid>$cpid</cpid>
		<mid>0</mid>
		<cpmid>$cpmid</cpmid>
		<mobile>$phone</mobile>
		<port>$port</port>
		<msg>$message</msg>
		<msgtype>1</msgtype>
		<signature>$signature</signature>
		<timestamp>$date</timestamp>
		<validtime>0</validtime>
		</MtPacket>
		";

        if(function_exists("curl_exec")){
            try{
                $ch = curl_init();
                $url = 'http://api.10690909.com/newmt';
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_HEADER, 0);
                curl_setopt($ch, CURLOPT_URL,$url);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $xml_data);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER,true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 300);
                $response = curl_exec($ch);

                if($response === false)
                    $response = curl_error($ch);
                $str =  curl_multi_getcontent ($ch);
                curl_close($ch);

                return $str;
            }catch (\Exception $e){
                //echo strval($e);
                \Log::error(strval($e),true,true,'SmsSendError');
            }


        }

    }
}