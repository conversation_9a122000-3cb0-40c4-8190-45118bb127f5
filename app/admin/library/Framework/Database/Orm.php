<?php

namespace Framework\Database;

/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/2/29/029
 * Time: 11:39
 */

//use Framework\Paginator;
use \Framework\Config;
use Illuminate\Events\Dispatcher;
use Illuminate\Container\Container;
use Illuminate\Database\Capsule\Manager as Capsule;

class Orm
{
    public $capsule;

    public function __construct()
    {
        $this->capsule = new Capsule;

        $databaseConfig = Config::get('database');

        if ($databaseConfig) {
            foreach ($databaseConfig as $k => $v) {
                $this->capsule->addConnection($v, $k);
            }
        } else {
            throw new \RuntimeException('database config not found', 501);
        }

//        var_dump(Capsule::connection()->getPdo());

        $this->capsule->setEventDispatcher(new Dispatcher(new Container));

        // 注册全局静态类
        $this->capsule->setAsGlobal();

        // 注册分页类
        //Capsule::setPaginator(function() use ($app, $configs) {
        //    return new Paginator($_REQUEST, $configs->get('pager', 'page'));
        //});

        $this->capsule->bootEloquent();
    }

    public function setPdo($connection_name)
    {
        Capsule::connection()->setPdo();
    }
}

(new Orm());