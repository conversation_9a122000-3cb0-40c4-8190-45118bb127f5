<?php

namespace UnitTest;
/**
 * Created by PhpStorm.
 * User: tim
 * Date: 19-7-25
 * Time: 上午11:46
 */

use Framework\DingTalk\DingTalkAlarm;
use Framework\Helper;
use Framework\Log;
use Framework\SDK\HangXin\ReceiptSales;
use Fuel\Defines\OilType;
use Fuel\Defines\ReceiptConfig;
use Fuel\Service\OperatorReceipt;
use Fuel\Service\ReceiptSplit;
use Fuel\Service\ReceiptSplitApply;
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilCardViceTrades;
use Models\OilReceiptApply;
use Models\OilReceiptApplyDetails;
use Models\OilReceiptSalesDetails;
use Models\OilReceiptSalesDetailsRelation;
use Models\OilReceiptManage;
use Models\OilReceiptManageAll;
use Fuel\Defines\ReceiptType;
use Models\OilReceiptDetails;
use Models\OilInvoiceImportDetail;
use Models\OilInvoiceImportStatistics;
use Fuel\Defines\OilOperators;
use Models\OilTypeNo;
use Models\OilGasStockReport;
use Models\OilReceiptReturn;
use Models\OilReceiptReturnDetail;
use Illuminate\Support\Facades\DB;

class ReceiptCase
{

    //冲红的销售单据，重新提交航信开票
    public function receiptSaleRed()
    {
        $receipt_id = 25324;
        $oil_type = 101;
        //$oil_type = 101;
        $sql = "select rel.* from oil_receipt_sales_details as sa
left join oil_receipt_sales_details_relation as rel on sa.id = rel.sales_id
where sa.receipt_apply_id = ".$receipt_id." and sa.color_type = 10 and rel.oil_sec_type = ".$oil_type." order by rel.sku asc";
        $result = Capsule::connection()->select($sql);
        if(count($result) == 0){
            die("销售单据不存在");
        }

        $maxAmount = 112900;
        $subAmount = 0;
        $k = 0;
        $saleDetail = [];
        foreach ($result as $_val){
            $subAmount += abs($_val->amount);
            if($subAmount > $maxAmount){
                $k++;
                $subAmount = abs($_val->amount);
            }
            $arr = Helper::stdToArray($_val);
            unset($arr['id']);
            unset($arr['sales_id']);
            unset($arr['sale_no']);
            unset($arr['line_code']);
            $arr['snapshot_num'] *= -1;
            $arr['num'] *= -1;
            $arr['amount'] *= -1;
            $arr['createtime'] = \helper::nowTime();
            $arr['updatetime'] = \helper::nowTime();
            //$saleDetail[$k][] = $_val->id;
            $saleDetail[$k][] = $arr;
        }
        //print_r($saleDetail);
        foreach ($saleDetail as $_k => $_item){
            $amount = array_sum(array_column($_item,"amount"));
            $main['receipt_apply_id'] = 25324;
            $main['sale_no'] = (new ReceiptSplit())->makeReceiptNo('FSDZ08');
            $main['receipt_type'] = 10;
            $main['oil_type'] = $oil_type == 101 ? 1 : 2;
            $main['check_status'] = 10;
            $main['receipt_status'] = 10;
            $main['color_type'] = 20;
            $main['document_type'] = 10;
            $main['receipt_amount'] = $amount;
            $main['use_fanli_money'] = 0;
            $main['real_amount'] = $amount;
            $main['receipt_title'] = '泰安市峰松电子科技有限公司';
            $main['taxpayer_no'] = 913709025652463230;
            $main['addr_tel'] = '山东省泰安市岱岳区一天门大街 峰松电子20层财务***********';
            $main['bank_account'] = '中国邮政储蓄银行有限责任公司山东省泰安市分行********';
            $main['drawer'] = '张蒙';
            $main['payee'] = '洪玉洁';
            $main['checker'] = '王聪';
            $main['tax_rate'] = 13;
            $main['open_channel'] = 10;
            $main['is_repeat'] = 2;
            $main['is_open'] = 2;
            $main['is_auto'] = 10;
            $main['creator_id'] = 1001327;
            $main['creator_name'] = '孟培';
            $main['last_operator'] = '孟培';
            $main['remark'] = $main['sale_no'].';*****************;';
            $main['createtime'] = \helper::nowTime();
            $main['updatetime'] = \helper::nowTime();
            print_r($main);
            //$info = OilReceiptSalesDetails::add($main);
            foreach ($_item as $_line => $_de){
                //$_de['sales_id'] = $info->id;
                $_de['sale_no'] = $main['sale_no'];
                $_de['line_code'] = $_line+1;
                //print_r($_de);
                //$rel = OilReceiptSalesDetailsRelation::add($_de);
            }
        }
    }

    public function testReceiptDeductionTradeNum()
    {
        $detailsInfo = [
            'trade_num'   => 19999.99,
            'trade_price' => 6.6,
            'orgcode'     => '2001331',
            'oil_type'    => OilType::CHAI_YOU
        ];
        $result = ReceiptConfig::deductionTradeNum($detailsInfo['trade_num'], $detailsInfo['trade_price'], $detailsInfo['orgcode'], $detailsInfo['oil_type']);


        $detailsInfo['afterResult'] = $result;

        return $detailsInfo;
    }

    public function testGetMonth()
    {
        $result = ReceiptConfig::getMonth(1);

        return $result;
    }

    public function testSplitTrades()
    {
        require_once APP_MODULE_ROOT . '/oil_receipt_apply/control.php';
//        $params = array (   'id' => '1016',   'isEdit' => '1',   'org_id' => '1',   'receipt_title_id'=> '185',   'receipt_amount' => '45417.36',   'org_contact_id' => '9be5c96c-c27a-4b98-9aa1-6a62c329539c',   'org_addr_id' => '1f48de02-a813-4e8b-8528-1a28c48a4fd4',   'address' => 'u诶蓉儿无哦惹我',   'org_addr_name' => '',   'org_addr_mobile' => '',   'org_addr_address' => '',   'org_addr_zip' => '',   'init_org_addr_name' => '',   'init_org_addr_mobile' => '',   'init_org_addr_address' => '',   'init_org_addr_zip' => '',   'apply_time' => '2022-05-13 16:12:36',   'admin_remark' => '',   'custom_remark' => '',   'receipt_remark' => '',   'org_code' => '200I1A',   'org_name' => '油品测试',   'user_id' => 1,   'last_operator' => '超级管理员',   'oil_type' => 12,   'receipt_type' => '专票',   'seller_name' => '中启行物联科
//技（北京）有限公司',   'seller_taxpayer_no' => '91210244MA0QE1K32Y',   'seller_company_code' => '11.11',   'pay_company_id' => 25,   'corp_name' => '油品测试&顺丰P1',   'pay_company_name' => '顺丰北京分公司',   'receipt_status' => '10',   'receipt_apply_id' => '1016');
        $params = array (   'max_amount' => '29067.99',   'sn' => '1652757258028',   'org_id' => '650',   'receipt_type' => '专票',   'oil_type' => '12',   'receipt_title_id' => '205',   'receipt_amount' => '29067.99',   'org_contact_id' => '3d9eb5e5-b0a3-47b0-92fd-310e5bde358a',   'org_addr_id' => '9f16b7d9-c4d8-49db-bfda-76073e0a3a4a',   'address' => '广东省东莞市塘厦镇区平山188工业区高尔夫大道高裕南路62号台华纸业（东莞）有限公司',   'org_addr_name' => '',   'org_addr_mobile' => '',   'org_addr_address' => '',   'org_addr_zip' => '',   'init_org_addr_name' => '',   'init_org_addr_mobile' => '',   'init_org_addr_address' => '',   'init_org_addr_zip' => '',   'apply_time' => '2022-05-17 11:14:18',   'admin_remark' => '',   'custom_remark' => '',   'receipt_remark' => '',   'data_from' => 1,   'org_code' => '200133',   'org_name' => '油品管理责任有>限公司',   'seller_name' => '汇通天下石油化工（大连）有限公司',   'seller_taxpayer_no' => '91210244MA0QE1K31X',   'seller_company_code' => '11.11',   'pay_company_id' => 54,   'corp_name' => '汇通天下物联科技有限公司',   'pay_company_name' => '汇通天下物联科
技有限公司',   'receipt_status' => 10,   'receipt_apply_id' => 1025,   'no' => 'FA220517395838764',   'user_id' => 1,   'last_operator' => '超级管理员', );
        (new \oil_receipt_Apply())->addDetail($params);
    }

    public function testInWhiteOrgList()
    {
        $whiteMap = [
            '2000T0', '2000BG', '200J3I', '200WF3', '2008GA', '200D8X', '20007Z', '201K4V', '201FXC', '20197G', '20266M', '200IF3', '201ICN', '200V3A', '2013LY', '201E6U', '200ZIK', '201ICO', '200L9K', '200MOX', '202448', '2007RL', '2016Z3', '201ICP', '201JU8', '200TOP', '200I8U', '200ZXE', '201CZ7', '2015UF', '200GYU', '200R80', '20005W', '200QWK', '20007J', '201L84', '200HF3', '201J9R', '2016V4', '201H5Z', '200MP2', '201K8E', '201LY1', '201LB8', '200D8V', '200IZE', '201M66', '200TWE', '200PZE', '201LTO', '2000HS', '2008JV', '200OPF', '20181V', '2018IP', '200JD2', '2014GB', '200D5I', '2006KF', '200ND6', '200FJ4', '200URT', '201IOH', '201AG9', '201N03', '201K45', '201LAD', '2010PK', '200G3V', '200GTD', '20115S', '2015LG', '201KNJ', '2019SW', '201JAD', '201LDR', '200FWF', '200V7L', '2001CW', '201INM', '200U2V', '201J6X', '200YI9', '201BGU', '201AO6', '2016F6', '200ZP6', '20007K', '201JZK', '2013MH', '2008EM', '201AGB', '201JJO', '201B7J', '2014GI', '201MGM', '200LNY', '200SKM', '201NE2', '201I0T', '201S3C', '201KZV', '201MBJ', '201BOT', '201GGX', '200I7I', '201LUP', '200X1T', '2013Q7', '201AGC', '2015ML', '200S3K', '201M4O', '200JB0', '201WU2', '200NBQ', '201OP5', '200SBM', '200IDD', '20170X', '201QIO', '201FSO', '200P76', '200BNE', '201AFK', '201B6U', '201VR4', '201JZB', '200B0M', '201C4G', '200PM1', '2015T0', '2001U0', '201H4X', '201BV1', '201IFB', '200YF1', '201AWB', '201KUQ', '2013TE', '200MWY', '201HP2', '201KKD', '200XBX', '201NVC', '200U2X', '201BOO', '2012N8', '2021D1', '201FET', '200XDD', '20145T', '200XTJ', '201Y1U', '200Q52', '201UUV', '201HYC', '200LL9', '201FLY', '201IRN', '201L2Q', '2009JN', '201IGR', '2016F3', '201LSQ', '2014FA', '201BUI', '2015DK', '201WNJ', '201MMP', '201IUF', '201IWD', '200JCV', '200P30', '201HZU', '2014KS', '200WPN', '201H92', '200V3E', '200MTY', '200JHK', '200R0Z', '200G8F', '201JYB', '202160', '200R0E', '200S2E', '201HOT', '200VLO', '201B3W', '201Z3S', '201LQL', '200U19', '200GAL', '200ZX0', '201HVT', '201UWQ', '201DM7', '200HJG', '200IKL', '201AEO', '201OFQ', '20162Q', '201OQL', '201NV7', '201M2C', '200J6T', '201S2H', '200I3F', '201LLH', '200MWK', '2010VS', '201VSQ', '201GAU', '2004AY', '201SV7', '200LS1', '200GSM', '2015PB', '201SZG', '201A5U', '200MKJ', '200Q1E', '200TO4', '201GF6', '201WBQ', '201NJA', '201IJM', '201JOI', '200UK2', '201OQT', '201KFH', '201Q2Y', '200Y1Q', '201RI5', '201F2T', '2017ZX', '200I1R', '201NL8', '200R89', '201IB9', '2021MS', '2018QN', '201INB', '200EV8', '200J46', '201UMS', '201HXV', '201UEQ', '2021H2', '201NIN', '200QJE', '201Z0I', '200SPV', '200SHC', '200Y4P', '201NN4', '201HCZ', '20218A', '201K9F', '200WHE', '200UTH', '200JWG', '201L3F', '200P9I', '200K8G', '201OA8', '200O37', '200K77', '200U9Q', '201JWZ', '200QB8', '200K82', '201IQ0', '201URW', '201O0J', '200UL3', '201K39', '201WD2', '200QI7', '201IAC', '201TB7', '201984', '201WBR', '20081Q', '201J2T', '200IF8', '2014XO', '2001LF', '2017GY', '201OIQ', '2017GJ', '202498', '201ICX', '201B19', '200WTS', '201CWK', '201529', '2015SO', '201EDT', '201VBU', '201JWY', '201GF5', '201Y6T', '200HWI', '200WEN', '200NVS', '200WPT', '201FEL', '201BHP', '200JMG', '201S3H', '201BEO', '201J09', '200P2Q', '201L58', '201KHV', '200TEA', '200NCQ', '201QNF', '201VBP', '200UR7', '201IJT', '201JI7', '200VEC', '201LXN', '201QCM', '201JI1', '201YDW', '201P5A', '201DJ1', '201ML7', '201SIW', '201UYP', '200X7T', '201P5B', '200IYP', '200575', '201B5X', '2020S1', '2019DR', '201W9F', '201FWB', '201MKB', '2026HV', '200N3H', '200J1J', '201LEK', '201TSH', '200VRW', '2016HN', '20120J', '201P8Q', '201P59', '201GTY', '201HS8', '200MZ7', '201N7Y', '201DUC', '201MAP', '200X5N', '200RUC', '201PUF', '201HKB', '200JDV', '201JQ4', '201RXM', '200UWX', '200UQ9', '200XOV', '200RWE', '200IO4', '200TA3', '200W2J', '200SU2', '201QSR', '201CSH', '201TNK', '201BE9', '2023PP', '2020AD', '201I3B', '201CWJ', '200G6R', '201IAT', '200ZEO', '200YP0', '200V1O', '201KAG', '200TP1', '200PHF', '200PXD', '2015UI', '200YLH', '201U8O', '201YDY', '2016TR', '201RWX', '201MKF', '201Y0J', '200OZQ', '201FPH', '200YPO', '200UJ7', '200UHC', '201EU9', '201LIP', '200QV3', '201IMP', '201ISA', '201GF7', '200TG9', '201WNK', '20190D', '200IGV', '201SE4', '20183O', '201EOK', '201LB0', '200GOB', '200JWE', '201E3P', '2021SF', '200WZS', '200IWH', '201MKV', '200MRR', '201FKH', '201JZS', '201SGK', '201DXR', '201LAW', '20141D', '200Z4G', '201137', '201G3L', '20154F', '201MAA', '201K6F', '201Y1P', '2025RA', '200TLX', '202AE3', '2012DI', '200V5S', '200SNH', '201UHB', '20231N', '2012BF', '201I07', '200JQK', '201SIY', '200NY9', '200J8O', '201LP2', '201F21', '201RTL', '200FJ0', '2016C6', '200HYE', '2024M3', '201S2T', '200QNA', '200TGJ', '200F9Z', '200LKT', '201IAH', '201HWW', '201ABR', '201BRQ', '200TJP', '201K58', '200Q4S', '201SSO', '201OOJ', '2025JS', '200R5R', '201J3X', '20123V', '201YOO', '201IKZ', '201HKZ', '201XQ9', '201WV8', '200TJO', '2015XX', '200BSP', '200I6G', '201LMW', '201U25', '201VW4', '20207T', '201YBG', '200XN2', '200KT1', '201MRN', '201JI9', '200VZY', '201MAC', '201OTH', '201TU3', '201XW3', '201LTI', '201MAO', '201H6V', '201PVB', '201NE6', '201TNJ', '201XD3', '20254O', '2020XC', '20192W', '201YC4', '200JM2', '201HEE', '201YMW', '201XQ6', '201XAK', '201W0A', '201KF2', '200UKA', '201HBW', '201EYQ', '2014GZ', '201JMM', '201PNT', '20170R', '201M2M', '2011E6', '2026MF', '201II4', '200WTO', '200Z0J', '200VCQ', '201L07', '201IXV', '200PGO', '20163H', '20254R', '201Q7H', '200VTA', '2010X8', '201GAG', '201W7C', '201X1J', '201VRS', '200IQY', '201GUG', '200ND1', '200YDS', '201MGQ', '2022AH', '200MMT', '201JZD', '201MO6', '201C5W', '201IU8', '2015U3', '201XFD', '200XMN', '20249E', '2011FA', '201GXI', '200ZD7', '201JLK', '201U8A', '201YC9', '2025M1', '200TFT', '200FGW', '200GSI', '201M1Y', '2011P9', '2016NQ', '200LQG', '2012IZ', '202499', '200SEC', '2011W0', '2019DF', '2023TU', '200NYC', '2004X1', '20161S', '200QII', '201L03', '201Q37', '201M2A', '200ICV', '200HF8', '200K6I', '200UZP', '200VKI', '200QL8', '200TE4', '2023VV', '200J6P', '200TS6', '201LJS', '201Q4B', '2016HM', '200FES', '200H4R', '2014T6', '201M2E', '201E62', '201NG4', '20246V', '200VN8', '200US7', '200VWI', '201CQX', '2017W7', '200NG6', '200WPI', '201UFR', '200PH2', '200K2U', '201YZ7', '200WDP', '200XIL', '201448', '201CB6', '200SVS', '201AAN', '200MF5', '201IZ0', '201HK4', '200QB6', '2017EJ', '201ZFG', '201JQJ', '2025XG', '201K44', '201SKT', '200S0X', '201CD1', '200HYF', '200XD1', '2014D8', '201EQC', '201JOB', '200PDD', '20249C', '202100', '200RX0', '201UNW', '201Z6E', '200QXR', '201BHQ', '201KST', '201B9O', '20250X', '201R7T', '2028H3', '201DMO', '201J0B', '200OYR', '200NM9', '2020WT', '201H85', '201UI8', '201REM', '200YEC', '200MI2', '201T9O', '200TTJ', '2015YU', '20243W', '201AZJ', '201OCX', '200L5M', '201N2D', '20200W', '20261N', '200R3E', '201AFJ', '200I05', '201JZE', '20236O', '201YNU', '201W5Y', '20170P', '201NY7', '20246P', '200B3Q', '2013PW', '2021H6', '200YZV', '200PW2', '201KRI', '20239M', '201JAF', '201B29', '200ZQK', '201B2Q', '201ZVO', '200Y4Y', '2011QQ', '201CS8', '2028RJ', '201HDR', '201KJQ', '201EZ4', '201HFZ', '200TZ2', '200Z7R', '2017P0', '2017GT', '200DBY', '201EZX', '200OIP', '20187F', '200OS1', '200UZI', '2018NE', '201JOW', '201I7L', '200QXM', '200MVF', '20132N', '201JKU', '201JHT', '200X3L', '200TLD', '200YK9', '20184D', '200X0C', '201JJN', '201M4G', '200VYQ', '201F88', '200P2M', '2013ER', '200TDE', '2011SN', '201IJS', '201IFU', '201NVW', '2018TS', '200ICU', '200R78', '200T39', '2006B1', '201NZL', '2018ZK', '200NNW', '2029DN', '200R6B', '200XNP', '20135H', '20296J', '200PQ6', '200Z1M', '201RAY', '200Q3M', '201ORB', '2029L5', '2016YU', '20106J', '201E7X', '201A19', '201JA0', '200WTC', '201AU2', '2028ZB', '201LB4', '201JZV', '200V9V', '2023OS', '2015YE', '200RLS', '201JE2', '201IIS', '20291I', '2019SA', '200R9B', '200V26', '201ISL', '201HNA', '2028OM', '201JQ0', '201HU0', '201L5N', '201ZJN', '2024QM', '2029FQ', '2018CG', '202AAN', '200PH4', '2012YR', '201J9M', '2029A3', '201IJP', '200HYJ', '201ILB', '202BDR', '2024TZ', '201I35', '2016AH', '201SRU', '2029FU', '20294S', '2012XX', '2029PK', '2024WO', '201CQZ', '20158U', '2011AX', '201FEA', '200T59', '20130I', '201EVX', '201IID', '2025MS', '201ZJQ'
        ];

        $orgCode = '201EZ4';


        return [strtoupper(substr($orgCode, 0, 6)), in_array(strtoupper(substr($orgCode, 0, 6)), $whiteMap)];
    }

    public function checkReceiptAmountNotEqual()
    {
        $sql = "SELECT
  *
FROM
  (SELECT
  a.id,
  a.org_id,
  a.org_code,
  a.no,
  a.receipt_amount,
  sum( b.receipt_money ) AS receipt_amount_2 
FROM
  oil_receipt_apply a
  LEFT JOIN oil_receipt_apply_details b ON a.id = b.receipt_apply_id 
WHERE
  a.apply_time >= '" . date("Y-m") . "-01' 
  AND receipt_status >= 0 and a.is_internal = 2
GROUP BY
  a.id 
  ) c 
WHERE
  c.receipt_amount <> c.receipt_amount_2";

        $result = Capsule::connection()->select($sql);

        if (count($result) > 0) {
            $max = null;
            $min = null;
            $no_map = [];
            $title = date("Y-m") . '开票有' . count($result) . '金额与明细不一致';
            foreach ($result as $v) {
                $diff = number_format(($v->receipt_amount - $v->receipt_amount_2),2,".","");
                $no_map[] = $v->no.":(".$diff.")";
                if (!$max) {
                    $max = $diff;
                } else {
                    $max = $diff > $max ? $diff : $max;
                }

                if (!$min) {
                    $min = $diff;
                } else {
                    $min = $diff < $min ? $diff : $min;
                }
            }
            $content = [];
            $content[] = "* 描述：" . $title;
            $content[] = "* 条数：" . count($result);
            $content[] = "* 差异值最大：" . round($max, 2);
            $content[] = "* 差异值最小：" . round($min, 2);
            $content[] = "* 单号：" . implode(",",$no_map);
            (new DingTalkAlarm())->alarmToGroup($title, implode("\n", $content));
        }

        return $result;
    }

    public function setReceiptAmountDetail()
    {
        $end_time = date("Y-m-d H:i:s",strtotime("-5 min",time()));
        //$end_time = date("Y-m-d H:i:s",strtotime("-1 year",time()));
        $begin_time = date("Y-m-d",strtotime("-1 day",strtotime($end_time)))." 00:00:00";
        //$end_time = \helper::nowTime();
        $receiptSql = "SELECT
	d.receipt_apply_id,
	a.no,
	sum( receipt_money ) AS receipt_fee,
	(a.receipt_amount - sum( receipt_money )) as diff,
	sum(
	IFNULL( d.use_fanli_money, 0 )) as fanli_fee,
	a.receipt_amount,
	GROUP_CONCAT(  d.id,'#',d.trade_money ,'#',d.use_fanli_money ORDER BY d.use_fanli_money desc) as trade_id_str
FROM
	oil_receipt_apply_details AS d
	LEFT JOIN oil_receipt_apply AS a ON d.receipt_apply_id = a.id 
WHERE
	a.receipt_status IN ( 0 ) 
	AND is_internal = 2 
	AND a.createtime >= '".$begin_time."' 
	AND a.createtime < '".$end_time."' 
	GROUP BY d.receipt_apply_id 
	HAVING a.receipt_amount - receipt_fee >= 0.01 AND a.receipt_amount - receipt_fee < 1;";
        $receiptData = Capsule::connection()->select($receiptSql);

        $updateSql = [];
        if(count($receiptData) > 0){
            foreach ($receiptData as $_one){
                if($_one->diff != 0) {
                    Log::error("receipt-diff:".$_one->diff,[$_one->no],"diff_receipt_");
                    $_tmp = explode("#", $_one->trade_id_str);
                    if ($_one->fanli_fee > 0) {
                        $updateSql[] = "update oil_receipt_apply_details set use_fanli_money = use_fanli_money - " . $_one->diff . ",receipt_money = receipt_money + ".$_one->diff." where id = " . $_tmp[0] . " and receipt_apply_id = " . $_one->receipt_apply_id . ";";
                    } else {
                        $updateSql[] = "update oil_receipt_apply_details set trade_money = trade_money + " . $_one->diff . ",receipt_money = receipt_money + " . $_one->diff . " where id = " . $_tmp[0] . " and receipt_apply_id = " . $_one->receipt_apply_id . ";";
                    }
                }
            }
        }

        $saleSql = "SELECT
    r.sales_id,
	sum( amount ) as fee,
	d.sale_no,
	(d.receipt_amount - sum( amount )) as diff,
	GROUP_CONCAT(r.id,'#',r.amount ,'#',r.disamount order by amount desc) as id_str,
	d.receipt_amount,
	d.real_amount 
FROM
	oil_receipt_sales_details_relation AS r
	LEFT JOIN oil_receipt_sales_details AS d ON r.sales_id = d.id 
WHERE
	d.createtime >= '".$begin_time."' 
	AND d.createtime < '".$end_time."' 
	AND d.receipt_status = 10 
	AND d.is_open = 2 
GROUP BY
	r.sales_id HAVING d.receipt_amount - fee >= 0.01 and d.receipt_amount - fee < 1";
        $saleData = Capsule::connection()->select($saleSql);

        if(count($saleData) > 0){
            foreach ($saleData as $_item){
                if($_item->diff != 0) {
                    Log::error("sale-diff:".$_item->diff,[$_item->sale_no],"diff_receipt_");
                    $_tmp = explode("#", $_item->id_str);
                    $updateSql[] = "update oil_receipt_sales_details_relation set amount = amount + " . $_item->diff . " where id = " . $_tmp[0] . " and sales_id = " . $_item->sales_id . ";";
                }
            }
        }

        print_r($updateSql);
        if(count($updateSql) > 0){
            Log::error("update-Sql",[$updateSql],"diff_receipt_");
            Capsule::connection()->getPdo()->exec(implode(";",$updateSql).";");
        }
        return true;

    }

    public function testSplit()
    {
        $res = (new ReceiptSplit())->testSplit();
        exit(\GuzzleHttp\json_encode($res));
    }

    public function testPreSplit()
    {
        $res = (new ReceiptSplitApply())->testExportData();
        exit(\GuzzleHttp\json_encode($res, JSON_UNESCAPED_UNICODE));
    }

    public function testAddDetail()
    {
        $params = [
            'org_id' => 1,
            'receipt_amount' => 1,
            'oil_type' => 12
        ];

        $obj = new \oil_receipt_Apply();
        $obj->addDetail($params);
    }

    /**
     * 替换已开票消费的撤销
     */
    public function reciptTradeReject()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['nos'], $params);

        $nos = explode(",",$params['nos']);

        $oil_name=(isset($params['oil_name']) && $params['oil_name'] != '')?$params['oil_name']:'';

        foreach ($nos as $_no) {
            $updateSql = [];
            $_noStr = strval($_no);
            $useTrade = OilCardViceTrades::getOneInfo(['api_id' => $_noStr]);
            $str = "订单号：".$_no;
            echo $str.",原交易ID：".$useTrade->id."\r\n";
            if( !$useTrade ){
                throw new \RuntimeException($str.",交易不存在",2);
            }

            $applyList = OilReceiptApplyDetails::getApplyByTrade(['trades_id'=>$useTrade->id]);
            if(count($applyList) == 0){
                throw new \RuntimeException($str.",没有开票",2);
            }

            if(count($applyList) > 1){
                throw new \RuntimeException($str.",被重复占用",2);
            }

            $_item = $applyList[0];
            echo $str.",原可开票金额：".$_item['receipt_money']."，开票明细ID：".$_item['detail_id'],"，发票单号：".$_item['no']."\r\n";

            $condition['oil_name'] = $oil_name != '' ? $oil_name : $_item['oil_name'];
            $condition['org_id'] = $_item['org_id'];
            $condition['moneyGt'] = $_item['receipt_money'];
            $condition['nos'] = $nos;
            $trade = $this->getTrades($condition);
            if(count($trade) == 0){
                throw new \RuntimeException($str.",没有合适的消费",2);
            }
            $canUseTrade = $trade[0];

            echo $str.",替换的交易ID：".$canUseTrade->id.",可开票金额".$canUseTrade->receipt_remain."\r\n";
            print_r($canUseTrade);

            if(bccomp($canUseTrade->receipt_remain,$_item['receipt_money'],2) > 0){
                $left_money = bcsub($canUseTrade->receipt_remain,$_item['receipt_money'],2);
                $invoce = 20;
            }elseif(bccomp($canUseTrade->receipt_remain,$_item['receipt_money'],2) == 0){
                $invoce = 10;
                $left_money = 0;
            }

            Capsule::connection()->beginTransaction();
            try {
                $updateSql[] = "update oil_card_vice_trades set is_open_invoice = null,receipt_remain = trade_money-use_fanli_money where id = " . $useTrade->id;
                $updateSql[] = "update oil_card_vice_trades set is_open_invoice = " . $invoce . ",receipt_remain = " . $left_money . " where id = " . $canUseTrade->id;
                $updateSql[] = "update oil_receipt_apply_details set trades_id = " . $canUseTrade->id . ",trade_money=" . $canUseTrade->trade_money . " where id = " . $_item['detail_id'];

                print_r($updateSql);
                $upSqlStr = implode(";",$updateSql);
                //echo $upSqlStr;exit;
                $result = Capsule::connection()->getPdo()->exec($upSqlStr);
                print_r($result);
                Capsule::connection()->commit();
            }catch (\Exception $exception){
                Capsule::connection()->rollBack();
            }
        }
        echo "success";
    }

    public function getTrades($params = [])
    {
        $sql = "SELECT id,api_id,receipt_remain,oil_name,trade_money from oil_card_vice_trades where is_open_invoice is null and receipt_remain > 0 and cancel_sn is null";

        if(isset($params['oil_name'])){
            $where[] = "oil_name = '".$params['oil_name']."'";
        }
        if(isset($params['org_id'])){
            $where[] = "org_id = ".$params['org_id'];
        }
        if(isset($params['moneyGt'])){
            $where[] = "receipt_remain >= ".$params['moneyGt'];
        }

        if(count($where) > 0){
            $sql .= " and ". implode(" and ",$where);
        }
        $sql .= " and api_id not in ('".implode("','",$params['nos'])."')";
        $sql .= " order by createtime asc, receipt_remain desc limit 1";
        echo "\r\n".$sql."\r\n";
        $result = Capsule::connection()->select($sql);
        return $result;
    }

    /**
     * 替换已开票的消费
     */
    public function useTradesForReceipt()
    {
        $conf = [
            #"107" => ['org_id'=>107,"oil_type"=>2,"money"=>1500],
        ];
        $allIds = $allSQL = [];
        $partIds = $partSQL = [];
        foreach ($conf as $orgId => $_item){
            $sql = "SELECT
	trades.id,
	receipt_remain,
	oil_name,
	type.oil_type,
	trades.trade_money 
FROM
	oil_card_vice_trades as trades
	LEFT JOIN oil_type_no as type on trades.oil_name = type.oil_no
WHERE
	trades.is_open_invoice IS NULL 
	AND trades.receipt_remain > 0 
	and trades.use_fanli_money = 0
	AND trades.cancel_sn IS NULL
	and trades.createtime >= '2022-02-01'
	and type.oil_type = ".$_item['oil_type']."
	and trades.org_id = ".$_item['org_id']." order by trades.createtime asc limit 2000 ";
            $result = Capsule::connection()->select($sql);
            $_tmpFee = 0;
            print_r($result);
            if(count($result) > 0) {
                foreach ($result as $_trade){
                    $_tmpFee += $_trade->trade_money;
                    echo "交易金额".$_trade->trade_money.",累计金额：".$_tmpFee."\r\n";
                    if( bccomp($_item['money'],$_tmpFee,2) >= 0 ) {
                        $allIds[$orgId][] = $_trade->id;
                        $allSQL[$orgId][] = "update oil_card_vice_trades set receipt_remain = 0,is_open_invoice = 10,org_id = ".$orgId." where id = ".$_trade->id.";";
                    }else{
                        $left_fee = $_trade->trade_money - ($_item['money'] - ($_tmpFee - $_trade->trade_money));
                        echo "剩余金额：".$left_fee."\r\n";
                        $partIds[$orgId][] = $_trade->id;
                        $partSQL[$orgId][] = "update oil_card_vice_trades set receipt_remain = ".$left_fee.",is_open_invoice = 20,org_id = ".$orgId." where id = ".$_trade->id.";";
                        break;
                    }
                }
            }else{
                echo "没有获取到机构的消费：".$_item['org_id']."\r\n";
            }
        }

        file_put_contents("./deleteTrades.php",json_encode($allIds),FILE_APPEND);
        file_put_contents("./deleteTrades.php",json_encode($partIds),FILE_APPEND);
    }

    /**
     * Desc:刷新中游转化票 G7WALLET-4104
     *  ---------------------------------------------
     * @throws \GuzzleHttp\Exception\GuzzleException
     *  ---------------------------------------------
     * <AUTHOR>
     * @DateTime: 2023-03-14 11:58
     */
    public function updateOperatorDayTrades() {

        $up_operator_ids = ['6','10','16','14'];
        $day_trades_params = [
            'start_time'=>'2023-02-19',
            'end_time'=>'2023-03-13',
            'up_operator_id' => '6'
        ];
        Log::error('Job == 重新计算updateOperatorDayTrades入参:',$day_trades_params, 'unitTest_updateOperatorDayTrades');
        //调用
        $info = OperatorReceipt::updateOperatorDayTrades($day_trades_params);
        Log::error('Job == 重新计算updateOperatorDayTrades---success返回:',['success'=>$info], 'unitTest_updateOperatorDayTrades');
    }
    
    /**
      *  拉取开灵
      *  <AUTHOR> yanglei <<EMAIL>>
      *  @since  :2024年12月25日
      *
     */
    public function pullReceiptKl($params = [])
    {

        $TIME = strtotime('-7 day');
        $start_time = isset($params['stime']) ? $params['stime'] :date("Ymd", $TIME);
        $end_time = isset($params['etime']) ? $params['etime'] : date("Ymd", time());
        $page = 1;
        $data = [];
        while (true){
            Log::error('第'.$page.'页',[],'handleReceiptDataForKl');
            $params = [
                'status' => 2,
                'startTime' => date('Y-m-d',strtotime($start_time)),
                'endTime' => date('Y-m-d',strtotime($end_time)),
                'pageNum' => $page,
                'pageSize' => 200,
            ];

            try{
                $data = \Fuel\Service\Efficacy::getReceiptManage($params);
            }catch (\Exception $e){
                Log::error('Exception:',[$e],'handleReceiptDataForKl');
            }

            if(!$data){
                break;
            }

            // 拉取发票并核对
            if ($data) {
                $this->batchInsertDataForKl($data);
            }

            usleep(50);
            log::error('请求=>getKlByHttp,count:'.count($data), var_export($params, true), 'handleReceiptDataForKl');

            $page ++;
        }
    }
    
    private function batchInsertDataForKl($data)
    {
        foreach ($data as $receiptInfo) {
            //只要已开票数据
            if($receiptInfo['status'] != 2) {
                continue;
            }
            if(empty($receiptInfo['invoiceNo'])) {
                continue;
            }
            
            //先判断是否存在,存在先删除
            $oldInfo = OilReceiptManageAll::where('receipt_no', $receiptInfo['invoiceNo'])->first();
            if(!empty($oldInfo)){
                OilReceiptManageAll::where('id', $oldInfo->id)->delete();
                \Models\OilReceiptDetails::where('receipt_manage_id', $oldInfo->id)->delete();
                
            }

            $receiptTypeMap = array_flip(ReceiptType::$receipt_type_to_kl_mapping);
            if(isset($receiptInfo['orderno']) && !empty($receiptInfo['orderno'])){
                $checkSalesInfo = \Models\OilReceiptSalesDetails::getInfoByFilter([
                    'sale_no' => $receiptInfo['orderno'],
                ]);
            }
            $createInfo = [
                'sale_no' => $receiptInfo['orderno'] ?? '',
                'receipt_type' => $receiptTypeMap[$receiptInfo['invoiceKind']] ?? 90, //开票类型
                'receipt_time' => date('Y-m-d H;i:s',$receiptInfo['invoiceDate']/1000), //开票时间
                'color_type' => $receiptInfo['invoiceType'] == 1 ? 20 : 10, //红蓝票
                'receipt_no' => $receiptInfo['invoiceNo'], //发票号码
                'receipt_code' => $receiptInfo['invoiceCode'], //发票号码
                'hx_receipt_id' => $receiptInfo['id'], // 直接沿用航信id
                'tax_amount' => $receiptInfo['taxAmount'], //税额
                'no_tax_amount' => $receiptInfo['extaxAmount'], //不含税金额
                'receipt_amount' => $receiptInfo['orderAmount'], //价税合计
                'receipt_apply_no' => $receiptInfo['invoiceNo'],//$checkReceiptApplyInfo['no'],
                'oil_type' => '',//\Fuel\Defines\OilType::$oil_type[$checkSalesInfo['oil_type']],
                'tax_rate' => $receiptInfo['invoiceItems'][0]['itemTaxRate'] * 100,//$checkSalesInfo['tax_rate'],
                'receipt_title' => $receiptInfo['payerName'],//$checkSalesInfo['receipt_title'],
                'taxpayer_no' => $receiptInfo['payerTaxNo'],//$checkSalesInfo['taxpayer_no'],
                'addr_tel' => $receiptInfo['salerAddress'],//$checkSalesInfo['addr_tel'],
                'bank_account' => $receiptInfo['salerAccount'],//$checkSalesInfo['bank_account'],
                'drawer' => $receiptInfo['clerk'],
                'payee' => $receiptInfo['payee'],
                'checker' => $receiptInfo['checker'],
                'golden_remark' => $checkSalesInfo['remark'] ?? '',
                'creator_name' => '系统自动',
//                 'document_type' => $checkSalesInfo['document_type'], 
                'download_url' => $receiptInfo['pdfUrl'], //发票下载地址
                'receipt_channel' => \Fuel\Defines\ReceiptManageStatus::RECEIPTCHANNELKL, //获票渠道
                'seller_name' => $receiptInfo['saleName'] ?? '',//销售方信息
                'seller_taxpayer_no' => $receiptInfo['salerTaxNum'],
            ];
            
            $item_codes = [];
            $oilCategory = \Models\OilTypeCategory::getAllNotOneLevelOil(true);
            if($receiptInfo['invoiceItems']){
                foreach ($receiptInfo['invoiceItems'] as $details){
                    $item_codes[] = $details['itemCode'];
                }
            }
            
            $item_codes = array_unique($item_codes);
            if(count($item_codes) == 1){
                $oilType = OilTypeNo::where('tax_no',$item_codes[0])->first();
                if(isset($oilType->oil_base_id) && isset($oilCategory[$oilType->oil_base_id])){
                   $createInfo['oil_type'] = $oilCategory[$oilType->oil_base_id]['name'];
                }
            }
    
            $res = OilReceiptManageAll::create($createInfo);
    
            //明细处理
            if($receiptInfo['invoiceItems']){
                $details_add = [];
                foreach ($receiptInfo['invoiceItems'] as $details){
                    $details_add[] = [
                        'receipt_manage_id' => $res->id,
                        'inv_id' => $receiptInfo['id'],
                        'goods_name' => $details['itemCodeAbb'],
                        'unit' => $details['itemUnit'],
                        'sku' => $details['itemSpec'],
                        'num' => $details['itemNum'],
                        'unit_price' => $details['itemPrice'],
                        'amount' => $details['itemAmount'],
                        'tax_rate' => $details['itemTaxRate'],
                        'tax' => $details['itemTaxAmount'],
                        'createtime' => \helper::nowTime(),
                        'item_code' => $details['itemCode'],
                    ];
                }
    
                if($details_add){
                    \Models\OilReceiptDetails::insert($details_add);
                }
            }
    
        }
    
        return true;
    }
    
    /**
      *  新销项票统计 每月1号
      *  <AUTHOR> yanglei <<EMAIL>>
      *  @since  :2024年12月25日
      *
     */
    public function receiptStatistics()
    {
        $receiptAll = OilReceiptManageAll::where('receipt_time','>=',date('Y-m-d', strtotime('-1 month')))->get()->toArray();
        $ids = $arr = $detailsArr = $data = [];
        
        foreach($receiptAll as $r){
            $ids[] = $r['id'];
            $arr[$r['id']] = $r;
        }
        
        //查询明细
        $details = OilReceiptDetails::whereIn('receipt_manage_id', $ids)->get()->toArray();
        
        foreach($details as $k => $d){
            $details[$k]['month'] = date('Y-m',strtotime($arr[$d['receipt_manage_id']]['receipt_time']));
            $details[$k]['seller_taxpayer_no'] = $arr[$d['receipt_manage_id']]['seller_taxpayer_no'];
            $details[$k]['buyer_taxpayer_no'] = $arr[$d['receipt_manage_id']]['taxpayer_no'];
            $details[$k]['receipt_no'] = $arr[$d['receipt_manage_id']]['receipt_no'];
            $details[$k]['receipt_code'] = $arr[$d['receipt_manage_id']]['receipt_code'];
            $details[$k]['buyer_name'] = $arr[$d['receipt_manage_id']]['receipt_title'];
            $details[$k]['seller_name'] = $arr[$d['receipt_manage_id']]['seller_name'];
            
        }
        
        foreach($details as $k => $d){
            $key = $d['month'].'-'.$d['seller_taxpayer_no'].'-'.$d['item_code'];
            $data[$key][] = $d;
        }
        $data = array_values($data);
      
        $params['data'] = $data;
        self::importInvoiceAndStatisticsOtput($params);
        
    }
    
    public static function importInvoiceAndStatisticsOtput($params)
    {
    
//         $operators = OilOperators::getById(['id'=>$params['operator_id']]);
//         $yearStartTime = date('Y-01-01 00:00:00', strtotime($params['businessTime']));
//         $yearEndTime = date('Y-12-31 23:59:59', strtotime($params['businessTime']));
//         $businessTime = date('Y-m-01 00:00:00', strtotime($params['businessTime']));
//         $operatorsId = $operators->id;
    
        // 准备数据
        $detailList = [];
        $goodsNameList = [];
        foreach ($params['data'] as $rowData) {
            
            $afterConvert =  self::dataConversionOutput($rowData);
//             Log::info('afterConvert:'.var_export($afterConvert, TRUE), [], 'importInvoiceAndStatistics');
            if (!empty($afterConvert['msg'])) {
                continue;
            }
            $operators = \Models\OilOperators::where('taxpayer_no', $rowData[0]['seller_taxpayer_no'])->first();
            $operatorsId = $operators->id;
            $businessTime = date('Y-m-01 00:00:00', strtotime($rowData[0]['month']));
            $yearStartTime = date('Y-01-01 00:00:00', strtotime($businessTime));
            $yearEndTime = date('Y-12-31 23:59:59', strtotime($businessTime));
            
            $total_amount = $out = 0;
            foreach($rowData as $d){
                $total_amount += $d['amount'];
//                 $total_amount += $d['tax'];
            }
            foreach($rowData as $k =>$d){
                $detail = [
                    'invoice_type' => 2,
                    'operator_name' => $operators->company_name,
                    'operator_id' => $operators->id,
                    'business_time' => $businessTime,
                    'receipt_no' => $d['receipt_no'],
                    'receipt_code' => $d['receipt_code'],
                    'tax_code' => $d['item_code'] ?? '',//$afterConvert['tax_code'],
                    'unit' => $d['unit'],//$afterConvert['unit'],
                    'num' => $d['num'],//$afterConvert['num'],
                    'goods_name' => $afterConvert['oil_title'],
                    'receipt_amount' => $d['amount'],//$total_amount,  // oil_receipt_return.receipt_tax_total
                    'num_statistics' => $afterConvert['detail'][$k]['num_statistics'] ?? 0,
                    'num_taxation'  => $afterConvert['detail'][$k]['num_taxation'] ?? 0,
                    'taxpayer_no' => $d['buyer_taxpayer_no'],
                    'buyer_name' => $d['buyer_name'],
                    'seller_taxpayer_no' => $d['seller_taxpayer_no'],
                    'seller_name' => $d['seller_name'],
                    'province_in' => $afterConvert['detail'][$k]['province_in'] ?? 0, // 0 省外 1 省内
//                     'creator_name' => $rowData['creator_name'],
//                     'last_operator' => $rowData['last_operator'],
                    'createtime'=> \helper::nowTime(),
                    'updatetime' => \helper::nowTime(),
//                     'invoice_goods_name' => $d['name'],
                ];
                if($detail['province_in'] == 0){
                    $out += $d['amount'];
                    $out += $d['tax'];
                }
                $detailList[] = $detail;
            }
    
            // 本月合计
            $goodsName = $afterConvert['oil_title'];
            $sumDetail = [];
            if (!empty($goodsNameList[$goodsName])) {
                $sumDetail = $goodsNameList[$goodsName];
            }
    
            // 数量保存税务局数据
            $sumDetail['m_quantity'] = (empty($sumDetail['m_quantity']) ? 0 : $sumDetail['m_quantity']) + $afterConvert['num_taxation'];
            $sumDetail['m_amount'] = (empty($sumDetail['m_amount']) ? 0 : $sumDetail['m_amount']) + $total_amount;
            $sumDetail['m_quantity_statistics'] = (empty($sumDetail['m_quantity_statistics']) ? 0 : $sumDetail['m_quantity_statistics']) + $afterConvert['num_statistics'];
            
            $sumDetail['m_quantity_province_out'] = (empty($sumDetail['m_quantity_province_out']) ? 0 : $sumDetail['m_quantity_province_out']) + $afterConvert['num_taxation_out'];
            $sumDetail['m_amount_province_out'] = (empty($sumDetail['m_amount_province_out']) ? 0 : $sumDetail['m_amount_province_out']) + $out;
            $sumDetail['y_quantity_statistics_province_out'] = (empty($sumDetail['y_quantity_statistics_province_out']) ? 0 : $sumDetail['y_quantity_statistics_province_out']) + $afterConvert['num_statistics_out'];
            
            $sumDetail['goodsName'] = $goodsName;
            $sumDetail['operator_name'] = $operators->company_name;
            $sumDetail['operator_id'] = $operators->id;
            $goodsNameList[] = $sumDetail;
        }
    
    
        // 初始化汇总表
        $monthList = [];
        $currentM = date('m', strtotime($businessTime));
        for ($i = 1; $i < $currentM; $i++) {
            $monthList[] = $i;
        }
        $invoiceStatisticsList = [];
        foreach ($goodsNameList as $oilTypeItem => $value) {
            $invoiceStatisticsBase = [
                'invoice_type' => 2,
                'operator_name' => $value['operator_name'],//$operators->company_name,
                'operator_id' => $value['operator_id'],//$operators->id,
                'business_time' => $businessTime,
                'creator_name' => '',//$params['last_operator'],
                'last_operator'=> '',//$params['last_operator'],
                'createtime'=> \helper::nowTime(),
                'updatetime' => \helper::nowTime(),
                'import_status' => 1,
                'oil_type' => $value['goodsName'],//$oilTypeItem,
                'm_quantity' => $value['m_quantity'] ?? 0,
                'm_quantity_province_out' => $value['m_quantity_province_out'] ?? 0,
                'm_amount' => $value['m_amount'] ?? 0,
                'm_amount_province_out' => $value['m_amount_province_out'] ?? 0,
                'm_quantity_statistics' => $value['m_quantity_statistics'] ?? 0,
                'm_quantity_statistics_province_out' => $value['y_quantity_statistics_province_out'] ?? 0,
            ];
            $goodsName = $value['goodsName'];
            $operatorsId = $value['operator_id'];
            //统计全年数据(不包含当月)
            // select sum(xxxx) from _detail where orgcode = '' and   business_time <  $business_time and business_time > 年初 and invoice_type = 0
            $sum_y_quantity = OilInvoiceImportDetail::querySum($yearStartTime, $businessTime, $operatorsId, $goodsName, 'num_taxation', 2);
            $invoiceStatisticsBase['y_quantity'] = $sum_y_quantity + $invoiceStatisticsBase['m_quantity'];
            $sum_y_quantity_province_out = OilInvoiceImportDetail::querySum($yearStartTime, $businessTime, $operatorsId, $goodsName, 'num_taxation', 2, 0);
            $invoiceStatisticsBase['y_quantity_province_out'] = $sum_y_quantity_province_out + $invoiceStatisticsBase['m_quantity_province_out'];
    
            $sum_y_amount = OilInvoiceImportDetail::querySum($yearStartTime, $businessTime, $operatorsId, $goodsName, 'receipt_amount', 2);
            $invoiceStatisticsBase['y_amount'] = $sum_y_amount + $invoiceStatisticsBase['m_amount'];
            $sum_y_amount_province_out = OilInvoiceImportDetail::querySum($yearStartTime, $businessTime, $operatorsId, $goodsName, 'receipt_amount', 2,0);
            $invoiceStatisticsBase['y_amount_province_out'] = $sum_y_amount_province_out + $invoiceStatisticsBase['m_amount_province_out'];
    
    
            $sum_y_quantity_statistics = OilInvoiceImportDetail::querySum($yearStartTime, $businessTime, $operatorsId, $goodsName, 'num_statistics', 2);
            $invoiceStatisticsBase['y_quantity_statistics'] = $sum_y_quantity_statistics + $invoiceStatisticsBase['m_quantity_statistics'];
            $sum_y_quantity_statistics_province_out = OilInvoiceImportDetail::querySum($yearStartTime, $businessTime, $operatorsId, $goodsName, 'num_statistics', 2,0);
            $invoiceStatisticsBase['y_quantity_statistics_province_out'] = $sum_y_quantity_statistics_province_out + $invoiceStatisticsBase['m_quantity_statistics_province_out'];
    
            $disMonth = OilInvoiceImportDetail::distinctMonth($yearStartTime, $businessTime, $operatorsId, 2);
    
            // 未添加的月份
            $hasImportMonth = array_column($disMonth, 'm');
            $invoiceStatisticsBase['not_count_month'] = implode(',', array_diff($monthList, $hasImportMonth));
    
            $invoiceStatisticsList[] = $invoiceStatisticsBase;
        }
    
//         Log::info('$invoiceStatisticsList:'.var_export($invoiceStatisticsList, TRUE), [], 'importInvoiceAndStatisticsBase');
    
        Capsule::connection()->beginTransaction();
        try {
            // 删除当月历史数据
            if(isset($params['reset'])){
                OilInvoiceImportDetail::where('business_time','=', $businessTime)
                ->where('operator_id',$params['reset']['operator_id'])
                ->where('goods_name',$params['reset']['oil_type'])
                ->where('invoice_type', 2)
                ->delete();
                OilInvoiceImportStatistics::where('business_time','=', $businessTime)
                ->where('operator_id',$params['reset']['operator_id'])
                ->where('oil_type',$params['reset']['oil_type'])
                ->where('invoice_type', 2)
                ->delete();
            }else{
                OilInvoiceImportDetail::where('business_time','=', $businessTime)
                ->where('invoice_type', 2)
                ->delete();
                OilInvoiceImportStatistics::where('business_time','=', $businessTime)
                ->where('invoice_type', 2)
                ->delete();
            }
  
//             OilInvoiceImportDetail::deleteDetails($businessTime, $operatorsId, 2);
//             OilInvoiceImportStatistics::deleteStatistics($businessTime, $operatorsId, 2);
    
            // 创建数据
            
            $batchSize = 1000; // 每批插入的行数
            $totalRows = count($detailList);
            $insertData = [];
            
            for ($i = 0; $i < $totalRows; $i++) {
                $insertData[] = $detailList[$i];
                if (($i + 1) % $batchSize === 0 || $i + 1 === $totalRows) {
                    OilInvoiceImportDetail::insert($insertData);
                    $insertData = []; // 重置插入数据
                }
            }
            OilInvoiceImportStatistics::add($invoiceStatisticsList);
//             Log::info('$detailList:'.var_export($detailList, TRUE), [], 'importInvoiceAndStatisticsBase');
    
            // 计算其他汇总
            $otherBusinessTime = date('Y-m-02',$businessTime);
            $monthTimeList = OilInvoiceImportDetail::distinctMonth($otherBusinessTime, $yearEndTime, $operatorsId, 1);
    
            if (!empty($monthTimeList)) {
                // 处理之后的每月的年数据
                $needUpdate = [];
                foreach ($monthTimeList as $itemMonth) {
                    $itemTime = date('Y-'.$itemMonth->m.'-01 00:00:00', strtotime($businessTime));
                    $dbList = OilInvoiceImportStatistics::getByMonth($operatorsId, $itemTime, 1);
                    if (empty($dbList)) {
                        continue;
                    }
                    $sumResult = OilInvoiceImportDetail::querySumByGoodsName($yearStartTime, $itemTime, $operatorsId, 1);
                    if (empty($sumResult)) {
                        continue;
                    }
                    $sumResultMap = [];
                    foreach ($sumResult as $item) {
                        $sumResultMap[$item->goods_name] = $item;
                    }
    
                    $sumResultOutProvince = OilInvoiceImportDetail::querySumByGoodsName($yearStartTime, $itemTime, $operatorsId, 1, 0);
                    $sumResultOutProvinceMap = [];
                    if (!empty($sumResultOutProvince)) {
                        foreach ($sumResultOutProvince as $item) {
                            $sumResultOutProvinceMap[$item->goods_name] = $item;
                        }
                    }
    
                    foreach ($dbList as &$item) {
                        //                        dd(var_export($item['oil_type'],true).var_export($sumResultMap,true));
                        if ($sumResultMap[$item['oil_type']]) {
                            $item['y_quantity'] = $sumResultMap[$item['oil_type']]->total_num_t;
                            $item['y_amount'] = $sumResultMap[$item['oil_type']]->total_amount;
                            $item['y_quantity_statistics'] = $sumResultMap[$item['oil_type']]->total_num_s;
                        } else {
                            $item['y_quantity'] = 0;
                            $item['y_amount'] = 0;
                            $item['y_quantity_statistics'] = 0;
                        }
                        if ($sumResultOutProvinceMap[$item['oil_type']]) {
                            $item['y_quantity_province_out'] = $sumResultOutProvinceMap[$item['oil_type']]->total_num_t;
                            $item['y_amount_province_out'] = $sumResultOutProvinceMap[$item['oil_type']]->total_amount;
                            $item['y_quantity_statistics_province_out'] = $sumResultOutProvinceMap[$item['oil_type']]->total_num_s;
                        } else {
                            $item['y_quantity_province_out'] = 0;
                            $item['y_amount_province_out'] = 0;
                            $item['y_quantity_statistics_province_out'] = 0;
                        }
                        $needUpdate[] = $item;
                    }
                }
                // 更新汇总
                if (!empty($needUpdate)) {
                    OilInvoiceImportStatistics::batchUpdate($needUpdate);
                }
            }
    
            Capsule::connection()->commit();
        } catch (\Exception $e) {
            Capsule::connection()->rollBack();
            Log::error('import-exception' . strval($e), [], 'OilInvoiceImport');
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
    
        return [ 'code'=>1, 'msg'=>'success'];
    }
    
    public static function dataConversionOutput($rowData) {
        $num_statistics_all = $num_taxation_all = $num_statistics_out = $num_taxation_out = 0;
        $arr = [];
        foreach($rowData as $k => $v){
            $taxCode = $v['item_code'];//$rowData['tax_code'];
            $unit = $v['unit'];//$rowData['unit'];
            $num = $v['num'];//$rowData['num'];
            
            
            $result = ['success'=> false];
            $oilType = OilType::$oil_sec_type_for_import_input_new[OilType::$taxNoOilName[$taxCode]];
            if (!isset($oilType)) {
                $result['msg'] = '税收编码不存在';
                return $result;
            }
            if(!in_array($unit, $oilType['unit']) && !empty($unit)) {
                $result['msg'] = '单位不正确,可用单位:'.var_export($oilType['unit'], true).'入参:'.var_export($unit, true);
                return $result;
            }
            if (!isset($num)) {
                $num = 0;
            }
            
            // 税务局
            $num_taxation = $num;
            
            $num_taxation = (double)$num_taxation * (double)$oilType['tax_factor'][$unit];
            $num_taxation = round($num_taxation, 8);
            
            // 统计局
            $num_statistics = $num;
            
            $num_statistics = (double)$num_statistics * (double)$oilType['statistics_factor'][$unit];
            
            $num_statistics = round($num_statistics, 8);
            // 省内 省外
            // 规则判断如下:
            //1、税号15位的用第一二位,税号18位的用第三四位:
            //2、购方税号的2位=销方税号的2位则为省内:否则为省外
            $buyerTaxNo = $v['buyer_taxpayer_no'];
            $sellerTaxNo = $v['seller_taxpayer_no'];
            
            
            $buyerStart = strlen($buyerTaxNo) == 15 ? 0 : 2;
            $buyerProvince = substr($buyerTaxNo, $buyerStart, 2);
            $sellerStart = strlen($sellerTaxNo) == 15 ? 0 : 2;
            $sellerProvince = substr($sellerTaxNo, $sellerStart, 2);
            
            $num_statistics_all += $num_statistics;
            $num_taxation_all += $num_taxation;
            
            if($buyerProvince != $sellerProvince){
                $num_statistics_out += $num_statistics;
                $num_taxation_out += $num_taxation;
            }
            $arr[$k] = [
            'num_statistics' => $num_statistics,
            'num_taxation' => $num_taxation,
            'tax_code' => $taxCode,
            'num' => $num,
            'unit' => $unit,
            'oil_title' => $oilType['title'],
            'province_in' => $buyerProvince == $sellerProvince ? 1 : 0,
        ];
        }



        return [
            'num_statistics_out' => $num_statistics_out,
            'num_taxation_out' => $num_taxation_out,
            'num_statistics' => $num_statistics_all,
            'num_taxation' => $num_taxation_all,
            'tax_code' => $taxCode,
            'num' => $num,
            'unit' => $unit,
            'oil_title' => $oilType['title'],
            'province_in' => $buyerProvince == $sellerProvince ? 1 : 0,
            'detail' => $arr
        ];
    }
    
   /**
     *  天然气库存报表生成
     *  <AUTHOR> yanglei <<EMAIL>>
     *  @since  :2025年3月11日
     *
    */
    public function oilGasStockReport($params = [])
    {
        $true_name = $params['true_name'] ?? '';
        if(!empty($params)){
            $preInfos = OilGasStockReport::where('id',$params['id'])->get()->toArray();
            $time = $preInfos[0]['business_time'];
            $date = date('Y-m',strtotime($time.' -1 month'));
            $preInfos = OilGasStockReport::where('business_time',$date)->where('operator_id', $preInfos[0]['operator_id'])->get()->toArray();
            $stime = date('Y-m-01',strtotime($time));
            $etime = date("Y-m-d", strtotime($stime." +1 month -1 min"));
            $now_date = $time;
        }else{
            //获取上月数据
            if(date('d') == 1) {
                $date = date('Y-m',strtotime('-2 month'));
                $stime = date('Y-m-01',strtotime('-1 month'));
                $etime = date("Y-m-d", strtotime(date("Y-m-01 00:00:00")." -1 min"));
            }else{
                $date = date('Y-m',strtotime('-1 month'));
                $stime = date('Y-m-01');
                $etime = date("Y-m-d");
            }
            $now_date = date('Y-m',strtotime($date.' +1 month'));
            $preInfos = OilGasStockReport::where('business_time',$date)->get()->toArray();
            
        }
        
        try{
            foreach ($preInfos as $info){
                if($info['push_status'] == 1) continue;
                Capsule::connection()->beginTransaction();
                //先删除本月
                OilGasStockReport::where('operator_id',$info['operator_id'])->where('business_time',$now_date)->delete();
                //查询本月进项票和销项票 计算本月库存
                $compay = \Models\OilOperators::where('id',$info['operator_id'])->first();
                $taxNo = $compay->taxpayer_no ?? '';
                if(empty($taxNo)) continue;
            
                //进项票
                $oilRetuenInfos = OilReceiptReturn::where('buyer_taxpayer_no',$taxNo)->where('receipt_date','>=',$stime)
                ->where('receipt_date','<=',$etime)->get()->toArray();
                $ids = $arr = $detailsArr = $data = [];
             
                foreach($oilRetuenInfos as $r){
                    $ids[] = $r['id'];
                    $arr[$r['id']] = $r;
                }
                $unit_arr = [
                    '吨' => 1,
                    '公斤' => 0.001,
                    'KG' => 0.001,
                    'kg' => 0.001,
                    'Kg' => 0.001,
                    'kG' => 0.001,
                    '千克' => 0.001,
                    '立方米' => 0.*********,
                    'm3' => 0.*********,
                    'M3' => 0.*********,
                    '立方' => 0.*********,
                    'm³' => 0.*********,
                    'M³' => 0.*********,
                ];
            
                $m_quantity_in = $m_quantity_province_out_in = $m_amount_in = $m_amount_province_out_in = 0;
                //查询明细
                $returnDetails = OilReceiptReturnDetail::whereIn('receipt_return_id', $ids)->whereIn('code',['1020202000000000000', '1020203000000000000',  '1100202020000000000',   '1100202030000000000'])->get()->toArray();
            
                foreach($returnDetails as $k => $d){
                    if(strpos($d['name'],'压缩') !== false || strpos($d['name'],'CNG') !== false || strpos($d['name'],'Cng') !== false || strpos($d['name'],'cng') !== false) {
                        unset($returnDetails[$k]);
                        continue;
                    }
                    $unit = $unit_arr[$d['unit']] ?? '';
                    if(empty($unit)) {
                            $content = [];
                            $content[] = "* 描述：天然气库存进项票异常报警";
                            $content[] = "* 发票号码：" . $arr[$d['receipt_return_id']]['receipt_no'] ?? '';
                            $content[] = "* 发票代码：" . $arr[$d['receipt_return_id']]['receipt_code'] ?? '';
                            $content[] = "* 项目名称：" .$d['name'];
                            $content[] = "* 异常单位：" .$d['unit'];
                            (new DingTalkAlarm())->alarmToGroup('天然气库存报表计算异常报警', implode("\n", $content));
                        continue;
                    }
                    $buyerStart = strlen($arr[$d['receipt_return_id']]['buyer_taxpayer_no']) == 15 ? 0 : 2;
                    $buyerProvince = substr($arr[$d['receipt_return_id']]['buyer_taxpayer_no'], $buyerStart, 2);
                    $sellerStart = strlen($arr[$d['receipt_return_id']]['seller_taxpayer_no']) == 15 ? 0 : 2;
                    $sellerProvince = substr($arr[$d['receipt_return_id']]['seller_taxpayer_no'], $sellerStart, 2);
            
                    if(!in_array(strlen($arr[$d['receipt_return_id']]['buyer_taxpayer_no']), [15,18]) || !in_array(strlen($arr[$d['receipt_return_id']]['seller_taxpayer_no']), [15,18])){
                        $content = [];
                        $content[] = "* 描述：天然气库存进项票异常报警";
                        $content[] = "* 发票号码：" . $arr[$d['receipt_return_id']]['receipt_no'] ?? '';
                        $content[] = "* 发票代码：" . $arr[$d['receipt_return_id']]['receipt_code'] ?? '';
                        $content[] = "* 异常销方税号：" .$arr[$d['receipt_return_id']]['buyer_taxpayer_no'];
                        $content[] = "* 异常购方税号：" .$arr[$d['receipt_return_id']]['seller_taxpayer_no'];
                        (new DingTalkAlarm())->alarmToGroup('天然气库存报表计算异常报警', implode("\n", $content));
                    }
                    
                    $m_quantity_in += round($unit*$d['num'], 8);
                    $m_amount_in += $d['money'];
                    $m_amount_in += $d['tax'];
            
            
                    if($buyerProvince != $sellerProvince){
                        $m_quantity_province_out_in += round($unit*$d['num'], 8);
                        $m_amount_province_out_in += $d['money'];
                        $m_amount_province_out_in += $d['tax'];
                    }
            
            
                }
            
                $etime = $etime.' 23:59:59';
                //销项票
                $oilInfos = OilReceiptManageAll::where('seller_taxpayer_no',$taxNo)->where('receipt_time','>=',$stime)->where('receipt_time','<=',$etime)->get()->toArray();
                $ids = $arr = $detailsArr = $data = [];
            
                foreach($oilInfos as $r){
                    $ids[] = $r['id'];
                    $arr[$r['id']] = $r;
                }
            
                //查询明细
                $returnDetails = OilReceiptDetails::whereIn('receipt_manage_id', $ids)->whereIn('item_code',['1020202000000000000', '1020203000000000000',  '1100202020000000000',   '1100202030000000000'])->get()->toArray();
                $m_quantity = $m_quantity_province_out = $m_amount = $m_amount_province_out = 0;
                foreach($returnDetails as $k => $d){
                    $unit = $unit_arr[$d['unit']] ?? '';
                    if(empty($unit)) {
                        $content = [];
                        $content[] = "* 描述：天然气库存销项票异常报警";
                        $content[] = "* 发票号码：" . $arr[$d['receipt_manage_id']]['receipt_no'];
                        $content[] = "* 发票代码：" . $arr[$d['receipt_manage_id']]['receipt_code'];
                        $content[] = "* 项目名称：" .$d['project_name'];
                        $content[] = "* 异常单位：" .$d['unit'];
                        (new DingTalkAlarm())->alarmToGroup('天然气库存报表计算异常报警', implode("\n", $content));
                        continue;
                    }
                    if(strpos($d['project_name'],'压缩') !== false || strpos($d['project_name'],'CNG') !== false || strpos($d['project_name'],'Cng') !== false || strpos($d['project_name'],'cng') !== false) {
                        unset($returnDetails[$k]);
                        continue;
                    }
                    
                    $buyerStart = strlen($arr[$d['receipt_manage_id']]['taxpayer_no']) == 15 ? 0 : 2;
                    $buyerProvince = substr($arr[$d['receipt_manage_id']]['taxpayer_no'], $buyerStart, 2);
                    $sellerStart = strlen($arr[$d['receipt_manage_id']]['seller_taxpayer_no']) == 15 ? 0 : 2;
                    $sellerProvince = substr($arr[$d['receipt_manage_id']]['seller_taxpayer_no'], $sellerStart, 2);
            
                    
                    if(!in_array(strlen($arr[$d['receipt_manage_id']]['taxpayer_no']), [15,18]) || !in_array(strlen($arr[$d['receipt_manage_id']]['seller_taxpayer_no']), [15,18])){
                        $content = [];  
                        $content[] = "* 描述：天然气库存销项票异常报警";
                        $content[] = "* 发票号码：" . $arr[$d['receipt_manage_id']]['receipt_no'] ?? '';
                        $content[] = "* 发票代码：" . $arr[$d['receipt_manage_id']]['receipt_code'] ?? '';
                        $content[] = "* 异常销方税号：" .$arr[$d['receipt_manage_id']]['taxpayer_no'];
                        $content[] = "* 异常购方税号：" .$arr[$d['receipt_manage_id']]['seller_taxpayer_no'];
                        (new DingTalkAlarm())->alarmToGroup('天然气库存报表计算异常报警', implode("\n", $content));
                    }
                    
                    $m_quantity += round($unit*$d['num'], 8);
                    $m_amount += $d['amount'];
                    $m_amount += $d['tax'];
            
                    if($buyerProvince != $sellerProvince){
                        $m_quantity_province_out += round($unit*$d['num'], 8);
                        $m_amount_province_out += $d['amount'];
                        $m_amount_province_out += $d['tax'];
                    }
                }
            
                //插入数据
                $insertData = [
                    'operator_name' => $info['operator_name'],
                    'operator_id' => $info['operator_id'],
                    'business_time' => date('Y-m',strtotime($date.' +1 month')),
                    'm_quantity_in' => $m_quantity_in,
                    'm_quantity_province_out_in' => $m_quantity_province_out_in,
                    'm_amount_in' => $m_amount_in,
                    'm_amount_province_out_in' => $m_amount_province_out_in,
            
                    'm_quantity' => $m_quantity,
                    'm_quantity_province_out' => $m_quantity_province_out,
                    'm_amount' => $m_amount,
                    'm_amount_province_out' => $m_amount_province_out,
            
                    'stock' => $info['stock'] + $m_quantity_in - $m_quantity,
                    'createtime' => date('Y-m-d H:i:s'),
                    'last_operator' => $true_name,
                    'updatetime' =>  date('Y-m-d H:i:s')
                ];
                OilGasStockReport::create($insertData);
            
            
                //年累计数据
                $yearInfo = OilGasStockReport::where('operator_id',$info['operator_id'])
                ->where('business_time','<=',date('Y-m',strtotime($date.' +1 month')))
                ->select(
                    [ 
                    Capsule::raw('sum(m_quantity_in) as all_m_quantity_in'),
                    Capsule::raw('sum(m_quantity_province_out_in) as all_m_quantity_province_out_in'),
                    Capsule::raw('sum(m_amount_in) as all_m_amount_in'),
                    Capsule::raw('sum(m_amount_province_out_in) as all_m_amount_province_out_in'),
                    Capsule::raw('sum(m_quantity) as all_m_quantity'),
                    Capsule::raw('sum(m_quantity_province_out) as all_m_quantity_province_out'),
                    Capsule::raw('sum(m_amount) as all_m_amount'),
                    Capsule::raw('sum(m_amount_province_out) as all_m_amount_province_out')
            
            
                ])->first();
            
                $updateData = [
                    'y_quantity_in' => $yearInfo->all_m_quantity_in,
                    'y_quantity_province_out_in' => $yearInfo->all_m_quantity_province_out_in,
                    'y_amount_in' => $yearInfo->all_m_amount_in,
                    'y_amount_province_out_in' => $yearInfo->all_m_amount_province_out_in,
                    'y_quantity' => $yearInfo->all_m_quantity,
                    'y_quantity_province_out' => $yearInfo->all_m_quantity_province_out,
                    'y_amount' => $yearInfo->all_m_amount,
                    'y_amount_province_out' => $yearInfo->all_m_amount_province_out,
                ];
            
                OilGasStockReport::where('operator_id',$info['operator_id'])->where('business_time','=',date('Y-m',strtotime($date.' +1 month')))->update($updateData);
                Capsule::connection()->commit();
            }
        } catch (\Exception $e) {
            Capsule::connection()->rollBack();
            Log::error('import-exception' . strval($e), [], 'OilGasStock');
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
       
    }

}

