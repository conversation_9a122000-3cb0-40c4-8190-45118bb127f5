<?php


namespace UnitTest;


use Exception;
use Framework\Helper;
use Framework\Log;
use Fuel\Defines\OilCom;
use Fuel\Service\AccountAssignToGos;
use Fuel\Service\AccountCenter\AccountService;
use Fuel\Service\AccountCenter\TransferService;
use Fuel\Service\Assign;
use Fuel\Service\CardViceToGos;
use Fuel\Service\CardViceTradesToGos;
use Fuel\Service\Credit;
use Models\OilAccountAssign;
use Models\OilAccountAssignDetails;
use Models\OilAccountMoney;
use Models\OilAccountMoneyCharge;
use Models\OilCardAccount;
use Models\OilCardVice;
use Models\OilCardViceTrades;
use Models\OilCreditAccount;
use Models\OilCreditRepay;
use Models\OilOrg;
use Models\OilSyncCreditData;
use RuntimeException;

class CreditCancelCase
{
    private static $defualtOrg = '';
    private static $providerName = "G7保理(1138H)";
    public function cancelCredit()
    {
        $map = [
            ['trade_money' => '25689', 'billID' => '1168762236450328579', 'extID' => '373f2ccc-ce0e-11e9-8b04-6273c2818ea7'],
            ['trade_money' => '5210', 'billID' => '1168761771096506372', 'extID' => 'f51c84d4-ce0d-11e9-81f2-7b56a7bd4dc1'],
            ['trade_money' => '2222', 'billID' => '1168762535906861063', 'extID' => '61ce6c82-ce0e-11e9-a9d7-c60400fe739a'],
            ['trade_money' => '10', 'billID' => '1168762859019280386', 'extID' => '8fb966d8-ce0e-11e9-bb3c-db87809bc848'],
            ['trade_money' => '25689', 'billID' => '1168763053421056001', 'extID' => 'ab58a926-ce0e-11e9-8b4e-73226178e3c0'],
            ['trade_money' => '5600', 'billID' => '1168763252033945600', 'extID' => 'c792a394-ce0e-11e9-826a-4ebe0221ed3e'],
            ['trade_money' => '1111', 'billID' => '1168762354759065607', 'extID' => '480fedde-ce0e-11e9-980b-d3bb4021c592'],
            ['trade_money' => '500', 'billID' => '1168762005004443649', 'extID' => '1657e602-ce0e-11e9-a1a1-8826de41e7dd']
        ];

        foreach ($map as $v) {
            $apiParams = [
                'extID'       => $v['extID'],
                'billID'      => $v['billID'],
                'companyCode' => 'G7_DALIAN',
                'totalAmount' => intval($v['trade_money']),
                'comment'     => 'comment',
            ];

            var_dump($apiParams);
            $res = (new AccountService())->revokeCardConsume($apiParams);

            Log::error(__METHOD__, [$apiParams, $res], 'cancelCredit');
            usleep(500);
        }

    }

    public function mockConsume()
    {
        die("已停用");
        $map = [********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,10770145,10772652,10774254,10775269,10776144,10777919,10784943,10784972,10792977,10793707,10794799,10795221,10795295,10795829,10804564,10806791,10811912,10812223,10814194,10815448,10823395,10823537];

        $trades = OilCardViceTrades::whereIn("id", $map)->get();
        foreach ($trades as $insertArr) {
            var_dump("正在处理：" . $insertArr->id);
            try {
                $money    = $insertArr->trade_money * 100;
                $postData = [
                    'amount'       => round($money, 0),
                    'carNumber'    => $insertArr->truck_no ? $insertArr->truck_no : '未知',
                    'cardNo'       => $insertArr->vice_no,
                    'consumeArea'  => $insertArr->trade_address ? $insertArr->trade_address : '未知',
                    'consumePoint' => $insertArr->trade_place ? $insertArr->trade_place : "5",
                    'consumeTime'  => date('Y-m-d\TH:i:s.0000+08:00', strtotime($insertArr->trade_time)),
                    'consumeType'  => 'G7_FACTORING_CREDIT',
                    'extID'        => strval($insertArr->id),
                    'oilType'      => $insertArr->oil_name ? $insertArr->oil_name : '0#柴油'
                ];

                $orgInfo = self::getById(['id' => $insertArr->org_id]);
                if (!$orgInfo) {
                    var_dump('org不存在');
                    continue;
                }
                $postData['orgCode'] = $orgInfo->orgcode;

                if ($insertArr->oil_com == OilCom::GAS_FIRST_TALLY) {
                    $creditAccountInfo = OilCreditAccount::getByAccountNo($insertArr->account_no);
                    if (!$creditAccountInfo) {
                        var_dump('$creditAccountInfo不存在');
                        continue;
                    }
                    $postData['channelSubAccountID'] = $creditAccountInfo->subAccountID;
                    $postData['subAccountID']        = $creditAccountInfo->subAccountID;
                } elseif ($insertArr->oil_com == OilCom::GAS_FIRST_CHARGE) {
                    $postData['subAccountID'] = $insertArr->account_no;
                    $cardAccountInfo          = OilCardAccount::where('cardSubAccountID', $insertArr->account_no)->where('subAccountType', 'CREDIT')->first();
                    if (!$cardAccountInfo) {
                        var_dump('$cardAccountInfo不存在');
                        continue;
                    }
                    $creditAccountInfo = OilCreditAccount::getByAccountNo($cardAccountInfo->common_account_no);
                    if (!$creditAccountInfo) {
                        var_dump('$creditAccountInfo不存在');
                        continue;
                    }
                    $postData['channelSubAccountID'] = $creditAccountInfo->subAccountID;
                } else {
                    var_dump("不支持的卡种");
                }

                $domain                              = 'http://zqx.chinawayltd.com/';
                $postData['oilCompanyName']          = '汇通天下石油化工（大连）有限公司';
                $postData['unifiedSocialCreditCode'] = '91210244MA0QE1K31X';
                $postData['companyCode']             = 'G7_DALIAN';
                $postData['notifyUrl']               = $domain . "api.php?method=zbank.consume.pushCardConsume";
                Log::error('请求账户中心扣款参数：', [$postData], 'gasDeductMoney');

//                var_dump($postData);
//                die;
                $result = (new AccountService())->createCardConsume($postData);
                Log::error('请求账户中心扣款结果：', [$result], 'gasDeductMoney');
                if (!$result) {
                    throw new \RuntimeException('支付消费失败', 2);
                }
            } catch (\Exception $exception) {
                var_dump($exception->getMessage());
                continue;
            }

            usleep(100);

        }
        return true;
    }

    //为迁移机构的副卡创建负分配单
    public function cardNegativeRemain()
    {
        $orgIds = self::getByOrgcodeLike(self::$defualtOrg);
        if(count($orgIds) == 0){
            throw new \RuntimeException('机构不存在，orgcode:'.self::$defualtOrg, 2);
        }
        $accountList = OilAccountMoney::getOrgCashAccountNo(['OrgIn'=>[self::$defualtOrg]]);
        if(count($accountList) == 0){
            die("机构确实现金账户");
        }
        $condition['_export'] = 1;
        $condition['org_id_lk'] = $orgIds;
        $condition['oil_comIn'] = [20];
        $cardList = OilCardVice::getList($condition);
        if(count($cardList) > 0) {
            $nowCardList = $cardList->toArray();
            $newCardList = array_chunk($nowCardList, 200);
            foreach ($newCardList as $_first) {
                $assign_total = 0;
                $details = [];
                $assign_num = 0;
                foreach ($_first as $item) {
                    if (bccomp(0, $item['card_remain'], 2) >= 0) {
                        var_dump("卡号：" . $item['vice_no'] . ",余额小于等于0，余额:" . $item['card_remain']);
                        continue;
                    }
                    $filePath = APP_WWW_ROOT . DIRECTORY_SEPARATOR . 'download' . DIRECTORY_SEPARATOR . 'viceInfo.txt';
                    $viceTxt = $item['vice_no'] . "#" . $item['card_remain'] . "\r\n";
                    file_put_contents($filePath, $viceTxt, FILE_APPEND);
                    //todo 建立负充值单
                    $assign_num++;
                    $assign_total += $item['card_remain'];
                    $details[] = array("vice_no" => $item["vice_no"], "assign_amount" => $item['card_remain']);
                }
                $params['assign_total'] = $assign_total;
                $params['assign_num'] = $assign_num;
                $params['details'] = $details;
                $params['negative'] = 1;
                $params['account_no'] = $accountList[0]->account_no;
                $params['sn'] = Helper::uuid();
                $params['other_creator_id'] = '88888';
                $params['unit'] = 1;
                $params['other_creator'] = "系统迁移";
                $params['orgcode'] = self::$defualtOrg;
                $params['data_from'] = 1;

                $data = (new Assign($params))->formatParams()->splitOrder()->validate()->save();
                Log::error('accountAssign-return-data:--:' . var_export($data, TRUE), [$params], 'moveAssignNeg_');

                //push至G7Pay
                foreach ($data['assignIds'] as $id) {
                    (new \Fuel\Service\AccountCenter\AssignService())->createById($id);
                }
            }
        }
    }


    //获取机构的分配单
    public function getMoveAssign()
    {
        //todo 机构下的分配单，确定没有现金充值
        //todo 分配单的完成时间
        //todo 只取审核通过
        //todo 只取充值卡
        $orgIds = self::getByOrgcodeLike(self::$defualtOrg);
        if(count($orgIds) == 0){
            throw new \RuntimeException('机构不存在，orgcode:'.self::$defualtOrg, 2);
        }
        $condition['org_idList'] = $orgIds;
        $condition['status'] = 1;
        $condition['assign_type'] = 30;
        $condition['provider_flag'] = 2;
        $assignData = OilAccountAssign::getListForMove($condition);
        if(count($assignData) > 0){
            $batchData = [];
            foreach ($assignData as $item){
                $where['no_type'] = 'FP';
                $where['no'] = $item['no'];
                $isHas = $this->checkIsHas($where);
                if($isHas){
                    continue;
                }
                $_one['orgcode'] = self::$defualtOrg;
                $_one['no_id'] = $item['id'];
                $_one['no_type'] = 'FP';
                $_one['no'] = $item['no'];
                $_one['no_time'] = $item['updatetime'];
                $_one['status'] = 2;
                $_one['createtime'] = \helper::nowTime();
                $_one['updatetime'] = \helper::nowTime();
                $batchData[] = $_one;
            }
            $this->insertData($batchData);
        }
    }

    //获取机构的消费
    public function getMoveTrades()
    {
        //todo 只取充值卡
        //todo 使用createtime OR trade_time
        $orgIds = self::getByOrgcodeLike(self::$defualtOrg);
        if(count($orgIds) == 0){
            throw new \RuntimeException('机构不存在，orgcode:'.self::$defualtOrg, 2);
        }
        $condition['_export'] = 1;
        $condition['org_id_list'] = $orgIds;
        $condition['cancel_sn_null'] = 1;
        $condition['oil_comIn'] = [20,21];
        $condition['card_from'] = 30;
        $tradeList = OilCardViceTrades::getList($condition);
        if(count($tradeList) > 0){
            $batchData = [];
            foreach ($tradeList->toArray() as $item){
                $no = "XF".$item['id'];
                $where['no_type'] = 'XF';
                $where['no'] = $no;
                $isHas = $this->checkIsHas($where);
                if($isHas){
                    continue;
                }
                $_one['orgcode'] = self::$defualtOrg;
                $_one['no_id'] = $item['id'];
                $_one['no_type'] = 'XF';
                $_one['no'] = $no;
                $_one['no_time'] = $item['createtime'];
                $_one['status'] = 2;
                $_one['createtime'] = \helper::nowTime();
                $_one['updatetime'] = \helper::nowTime();
                $batchData[] = $_one;
            }
            $this->insertData($batchData);
        }
    }

    //获取机构的充值单
    public function getMoveCharge()
    {
        $condition['_export'] = 1;
        $condition['orgroot'] = self::$defualtOrg;
        $condition['charge_type'] = 1;
        $condition['status'] = 1;
        $chargeList = OilAccountMoneyCharge::getList($condition);
        if(count($chargeList) > 0){
            $batchData = [];
            foreach ($chargeList->toArray() as $_key => $item){
                //第一笔充值是授信额度，非还款数据
                if($_key == 0){
                    continue;
                }
                $where['no_type'] = 'HK';
                $where['no'] = $item['no'];
                $isHas = $this->checkIsHas($where);
                if($isHas){
                    continue;
                }
                $_one['orgcode'] = self::$defualtOrg;
                $_one['no_id'] = $item['id'];
                $_one['no_type'] = 'HK';
                $_one['no'] = $item['no'];
                $_one['no_time'] = $item['audit_time'];
                $_one['status'] = 2;
                $_one['createtime'] = \helper::nowTime();
                $_one['updatetime'] = \helper::nowTime();
                $batchData[] = $_one;
            }
            $this->insertData($batchData);
        }
    }

    //获取机构的还款单---废弃
    public function getMoveReplay()
    {
        $orgInfo = self::getByOrgcode(self::$defualtOrg);
        if(!$orgInfo){
            throw new \RuntimeException('机构不存在，orgcode:'.self::$defualtOrg, 2);
        }
        $condition['_export'] = 1;
        $condition['org_id'] = $orgInfo->id;
        $condition['no_way'] = 10;
        $condition['status'] = 1;
        $condition['is_del'] = '';
        $replyList = OilCreditRepay::getList($condition);
        if(count($replyList) > 0){
            $batchData = [];
            foreach ($replyList->toArray() as $item){
                $where['no_type'] = 'HK';
                $where['no'] = $item['no'];
                $isHas = $this->checkIsHas($where);
                if($isHas){
                    continue;
                }
                $_one['orgcode'] = self::$defualtOrg;
                $_one['no_id'] = $item['id'];
                $_one['no_type'] = 'HK';
                $_one['no'] = $item['no'];
                $_one['no_time'] = $item['updatetime'];
                $_one['status'] = 2;
                $_one['createtime'] = \helper::nowTime();
                $_one['updatetime'] = \helper::nowTime();
                $batchData[] = $_one;
            }
            $this->insertData($batchData);
        }
    }

    public function insertData(array $data)
    {
        OilSyncCreditData::batchAdd($data);
        return true;
    }

    public function checkIsHas(array $condition)
    {
        return OilSyncCreditData::getByNoType($condition);
    }

    //获取待推送数据
    public function getPushData()
    {
        $list = OilSyncCreditData::getList(['status'=>2,'_export'=>1]);
        if(count($list) > 0){
            foreach ($list->toArray() as $item){
                if($item['no_type'] == 'HK'){
                    die("请进行还款操作，还款单号：".$item['no']);
                    break;
                }
                if($item['no_type'] == 'FP'){
                    $this->transterCreditMoney($item['no']);
                }
                if($item['no_type'] == 'XF'){
                    $this->consumeCreditMoney($item['no_id']);
                }
                $upData['id'] = $item['id'];
                $upData['updatetime'] = \helper::nowTime();
                $upData['status'] = 1;
                $upRes = OilSyncCreditData::edit($upData);
                print_r($upRes);
            }
        }
    }

    //推送充值单和消费单到Gos
    public function pushChargeTradeToGos()
    {
        $list = OilSyncCreditData::getList(['status'=>1,'_export'=>1,"orgcode"=>self::$defualtOrg]);
        if(count($list) > 0){
            $assignIds = [];
            $tradeIds = [];
            foreach ($list->toArray() as $item){
                if($item['no_type'] == 'FP'){
                    $assignIds[] = $item["no_id"];
                }
                if($item['no_type'] == 'XF'){
                    $tradeIds[] = $item['no_id'];
                }
            }

            //更改分配单账户及推送gos
            if(count($assignIds) > 0){
                AccountAssignToGos::sendBatchUpdateTask($assignIds);
            }

            //更改消费记录的扣款账户及推送gos
            if(count($tradeIds) > 0){
                $newIds = array_chunk($tradeIds,200);
                foreach ($newIds as $ids ) {
                    CardViceTradesToGos::sendBatchUpdateTask($ids);
                }
            }
        }
    }

    //推送充值卡信息及推送gos
    public function pushCardViceInfo()
    {
        $orgIds = self::getByOrgcodeLike(self::$defualtOrg);
        if(count($orgIds) == 0){
            throw new \RuntimeException('机构不存在，orgcode:'.self::$defualtOrg, 2);
        }
        $condition['oil_com'] = 20;
        $condition['org_id_lk'] = $orgIds;
        $condition['_export'] = 1;
        $cardList = OilCardVice::getList($condition);
        if(count($cardList) > 0){
            $cardIds = [];
            foreach ($cardList->toArray() as $item){
                $cardIds[] = $item['id'];
            }
            $chunkCardId = array_chunk($cardIds,300);
            foreach ($chunkCardId as $_oneIds){
                CardViceToGos::sendBatchUpdateTask($_oneIds);
            }
        }
    }

    //账户中心转账接口
    public function transterCreditMoney($assign_no = '')
    {
        $map = [];
        if(!empty($assign_no)){
            array_push($map,$assign_no);
        }

        if(count($map) == 0){
            die("分配数据为空");
        }
        $condition['noIn'] = $map;
        $assignList = OilAccountAssign::getListForMove($condition);
        if(count($assignList) > 0) {
            foreach ($assignList as $assignInfo) {
                var_dump("处理分配单:".$assignInfo['no']);
                $account_no = $assignInfo['account_no'];
                if(substr($account_no,0,3) != '208'){
                    //todo 设置机构的授信账户
                    $orgInfo = self::getByOrgcode(self::$defualtOrg);
                    $where['oil_org.id'] = $orgInfo->id;
                    $accountInfo = self::getCreditAccountList([],$where);
                    if(count($accountInfo) == 0){
                        die("请开通授信账户");
                    }
                    $account_no = $accountInfo[0]->account_no;
                }
                $assignId = $assignInfo['id'];
                //校验机构授信账户
                $creditAccout = OilCreditAccount::checkCreditAccount($account_no, 'auditByTrans_');

                Log::error('$creditAccout' . var_export($creditAccout['credit_account_info']->toArray(), TRUE), [], 'checkIsTransfer');
                //判断是否需要请求授信转账
                if ($creditAccout['credit_account_info']->bill_way == 10 && $creditAccout['credit_account_info']->is_own == 0) {
                    $detail = OilAccountAssignDetails::getAssignDetails(['assign_id' => $assignId]);
                    $paramData = [];
                    $map = [];
                    $accountMap = [];
                    foreach ($detail as $item) {
                        $cardAccount = OilCardAccount::getCardAccountByAccountNO(['vice_id' => $item['vice_id'], 'common_account_no' => $account_no]);
                        if(!$cardAccount->cardSubAccountID){
                            die("请设置卡授信账户:vice_id:".$item['vice_id']);
                        }
                        $record['amount'] = intval($item['assign_money'] * 100);
                        $record['extID'] = Helper::uuid();
                        //圈回时，卡账户与机构账户互调
                        if (bccomp($record['amount'], 0) > 0) {
                            $record['inSubAccountID'] = strval($cardAccount->cardSubAccountID);
                            $record['outSubAccountID'] = strval($creditAccout['credit_account_info']->subAccountID);
                        } else {
                            $record['inSubAccountID'] = strval($creditAccout['credit_account_info']->subAccountID);
                            $record['outSubAccountID'] = strval($cardAccount->cardSubAccountID);
                        }
                        $record['amount'] = abs($record['amount']);
                        $paramData[] = $record;

                        $map[$record['extID']] = $item['id'];
                        $lastMoney = $cardAccount->amount + $item['assign_money'];
                        $accountMap[$record['extID']] = array("id"=>$cardAccount->id,"amount"=>$lastMoney);
                    }
                    //print_r($paramData);
                    //die();
                    if ($paramData) {
                        foreach ($map as $exiId => $oneId) {
                            OilAccountAssignDetails::edit(["id" => $oneId, 'extID' => $exiId]);
                        }
                        $result = (new TransferService())->transferDirectZbank(['transferDetail' => $paramData]);
                        Log::error("分配审核，转账结果" . var_export($result, TRUE), [$paramData], "moveCredit_");
                        //需要处理返回值
                        if ($result) {
                            $upData['id'] = $assignId;
                            $upData['account_type'] = 20;
                            $upData['account_no'] = $account_no;
                            OilAccountAssign::edit($upData);

                            foreach ($result->body as $res) {
                                if (array_key_exists($res->extID, $map)) {
                                    OilAccountAssignDetails::edit(["id" => $map[$res->extID], 'extID' => $res->extID, "billID" => $res->billID]);
                                }
                                if (array_key_exists($res->extID, $accountMap)) {
                                    $accountInfo = $accountMap[$res->extID];
                                    $upRes = OilCardAccount::edit(["id" => $accountInfo['id'], 'amount' => $accountInfo['amount']]);
                                    var_dump("处理分配单结果:".$upRes.",data".json_encode($accountInfo));
                                }
                            }
                        } else {
                            var_dump('授信转账异常');
                            continue;
                        }
                    }
                }
            }
        }
        return true;
    }

    //账户中心消费接口
    public function consumeCreditMoney($trade_id = '')
    {
        $map = [];
        if(!empty($trade_id)){
            array_push($map,$trade_id);
        }

        if(count($map) == 0){
            die("消费数据为空");
        }

        $trades = OilCardViceTrades::whereIn("id", $map)->get();
        foreach ($trades as $insertArr) {
            var_dump("正在处理：" . $insertArr->id);
            try {
                $money    = $insertArr->trade_money * 100;
                $postData = [
                    'amount'       => round($money, 0),
                    'carNumber'    => $insertArr->truck_no ? $insertArr->truck_no : '未知',
                    'cardNo'       => $insertArr->vice_no,
                    'consumeArea'  => $insertArr->trade_address ? $insertArr->trade_address : '未知',
                    'consumePoint' => $insertArr->trade_place ? $insertArr->trade_place : "5",
                    'consumeTime'  => date('Y-m-d\TH:i:s.0000+08:00', strtotime($insertArr->trade_time)),
                    'consumeType'  => 'G7_FACTORING_CREDIT',
                    'extID'        => strval($insertArr->id),
                    'oilType'      => $insertArr->oil_name ? $insertArr->oil_name : '0#柴油'
                ];

                //$orgInfo = OilOrg::getById(['id' => $insertArr->org_id]);
                $orgInfo = self::getByOrgcode(self::$defualtOrg);
                if (!$orgInfo) {
                    var_dump('org不存在');
                    continue;
                }
                $postData['orgCode'] = $orgInfo->orgcode;

                $trade_account_no = '';

                if ($insertArr->oil_com == OilCom::GAS_FIRST_TALLY) {
                    $where['oil_org.id'] = $orgInfo->id;
                    $creditAccountInfo = $accountInfo = self::getCreditAccountList([],$where);
                    if (!$creditAccountInfo) {
                        var_dump('$creditAccountInfo不存在');
                        continue;
                    }
                    $postData['channelSubAccountID'] = $creditAccountInfo[0]->subAccountID;
                    $postData['subAccountID']        = $creditAccountInfo[0]->subAccountID;
                    $trade_account_no = $creditAccountInfo[0]->subAccountID;
                } elseif ($insertArr->oil_com == OilCom::GAS_FIRST_CHARGE) {
                    $viceInfo =  (new OilCardVice())->getInfoByViceNo(['vice_no'=>$insertArr->vice_no]);
                    if (!$viceInfo) {
                        var_dump('卡号不存在');
                        continue;
                    }
                    $cardAccountInfo          = OilCardAccount::where('vice_id', $viceInfo->id)->where('subAccountType', 'CREDIT')->first();
                    if (!$cardAccountInfo) {
                        var_dump('$cardAccountInfo不存在');
                        continue;
                    }
                    if(count($cardAccountInfo) > 1){
                        die("卡信用账户有问题");
                    }
                    $trade_account_no = $cardAccountInfo->cardSubAccountID;
                    $postData['subAccountID'] = $cardAccountInfo->cardSubAccountID;
                    $creditAccountInfo = OilCreditAccount::getByAccountNo($cardAccountInfo->common_account_no);
                    if (!$creditAccountInfo) {
                        var_dump('$creditAccountInfo不存在');
                        continue;
                    }
                    $postData['channelSubAccountID'] = $creditAccountInfo->subAccountID;
                } else {
                    var_dump("不支持的卡种");
                }

                $domain                              = 'http://zqx.chinawayltd.com/';
                $postData['oilCompanyName']          = '汇通天下石油化工（大连）有限公司';
                $postData['unifiedSocialCreditCode'] = '91210244MA0QE1K31X';
                $postData['companyCode']             = 'G7_DALIAN';
                $postData['notifyUrl']               = $domain . "api.php?method=zbank.consume.pushCardConsume";
                Log::error('请求账户中心扣款参数：', [$postData], 'moveCredit_');

                //var_dump($postData);
                //die;
                $result = (new AccountService())->createCardConsume($postData);
                Log::error('请求账户中心扣款结果：', [$result], 'moveCredit_');

                if (!$result) {
                    die("支付消费失败");
                    throw new \RuntimeException('支付消费失败', 2);
                }


                if ($insertArr->oil_com == OilCom::GAS_FIRST_CHARGE) {
                    $lastAmount = $cardAccountInfo->amount - $insertArr->trade_money;
                    $upRes = OilCardAccount::edit(["id" => $cardAccountInfo->id, "amount" => $lastAmount]);
                    var_dump("更新消费账户结果:" . $upRes . ",amount" . $lastAmount);
                }
                //todo 更改账户名称
                $account_name = self::$providerName;
                //更新消费记录的扣款账户
                $tradeUpdata['id'] = $insertArr->id;
                $tradeUpdata['account_no'] = $trade_account_no;
                $tradeUpdata['account_name'] = $account_name;
                OilCardViceTrades::edit($tradeUpdata);

            } catch (\Exception $exception) {
                var_dump($exception->getMessage());
                if($exception->getMessage() == "可用授信额度不足"){
                    die("授信额度不足");
                }
                if($insertArr->oil_com == OilCom::GAS_FIRST_CHARGE) {
                    $lastAmount = $cardAccountInfo->amount - $insertArr->trade_money;
                    $upRes = OilCardAccount::edit(["id" => $cardAccountInfo->id, "amount" => $lastAmount]);
                    var_dump("更新消费账户结果:".$upRes.",amount".$lastAmount);
                }
                //todo 更改账户名称
                $account_name = self::$providerName;
                //更新消费记录的扣款账户
                $tradeUpdata['id'] = $insertArr->id;
                $tradeUpdata['account_no'] = $trade_account_no;
                $tradeUpdata['account_name'] = $account_name;
                OilCardViceTrades::edit($tradeUpdata);
                continue;
            }

            usleep(100);

        }
        //return true;
    }

    static public function getByOrgcodeLike($orgcode)
    {
        return OilOrg::where('orgcode','LIKE',$orgcode.'%')->pluck('id')->toArray();
    }

    static public function getByOrgcode($orgcode)
    {
        return OilOrg::where('orgcode','=',$orgcode)->first();
    }

    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        $data = OilOrg::where('id',$params['id'])->first();

        return $data;
    }

    static public function getCreditAccountList($params,$condition)
    {
        //Capsule::connection()->enableQueryLog();
        $data = OilCreditAccount::leftJoin('oil_credit_provider', 'oil_credit_provider.id', '=', 'oil_credit_account.credit_provider_id')
            ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_credit_account.org_id')
            #->whereIn('oil_credit_provider.name',  $params)
            ->whereNotNull("oil_credit_provider.product_code")
            ->where("oil_credit_provider.bill_way",10)
            ->where('oil_credit_account.is_own',0)
            #->where('oil_credit_provider.status',10)
            ->whereNotNull('oil_credit_account.subAccountID')
            ->where($condition)
            #->where("oil_org.is_del", 0)
            ->groupby('oil_credit_account.account_no')
            ->select("oil_credit_provider.name as providerName","oil_credit_account.account_no","oil_credit_account.org_id","oil_credit_account.credit_provider_id","oil_credit_account.subAccountID","oil_credit_account.status","oil_org.id","oil_org.orgcode")
            ->get();
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        return $data;
    }

    //查询卡授信额度
    public function getCardCreditAccount()
    {
        $orgIds = self::getByOrgcodeLike(self::$defualtOrg);
        if(count($orgIds) == 0){
            throw new \RuntimeException('机构不存在，orgcode:'.self::$defualtOrg, 2);
        }
        $condition['_export'] = 1;
        $condition['org_id_lk'] = $orgIds;
        $condition['oil_comIn'] = [20];
        $cardList = OilCardVice::getList($condition);

        foreach ($cardList as $viceInfo) {
            $cardAccount = OilCardAccount::where('vice_id', $viceInfo->id)->where('subAccountType', 'CREDIT')->get();

            if ($cardAccount) {
                foreach ($cardAccount as $value) {
                    try {
                        $res = (new AccountService())->getAccountInfoByProductCode(['subAccountID' => $value->cardSubAccountID, 'one' => 1], 'OIL_G7_45');
                        if($res){
                            $amount =  $res->restCreditAmount/100;
                            if(bccomp($amount,$value->amount,2) != 0){
                                echo $viceInfo->vice_no.'='.$value->amount .'-=-'.$amount."\n";
                            }
                            //$upRes = OilCardAccount::edit(["id" => $value->id, "amount" => $amount]);
                        }
                    } catch (Exception $e) {

                    }
                }
            }
        }
    }

    public function getCreditAccount()
    {
        $map = [
            //prod
            //'ZD24052300054'=>['ZD25022800046','ZD25010900050','ZD24120600050','ZD24113000050','ZD24110900048','ZD24110800051','ZD24110300048','ZD24110200047','ZD24103100048','ZD24102700050','ZD24101700048'],
            //'ZD24052300053'=>['ZD24060400048','ZD24053000052','ZD24052500050','ZD24052400050']
            //test
            'ZD25052100001' => ['ZD25052200003','ZD25052400001'],
        ];
        $res = Credit::mergeCreditBill($map); 
        print_r($res);
    }
}