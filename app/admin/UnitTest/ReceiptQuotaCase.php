<?php


namespace UnitTest;

use Framework\Excel\ExcelWriter;
use Framework\Log;
use Framework\Cache;
use Fuel\Defines\ReceiptConfig;
use Fuel\Response as Response;
use Fuel\Service\ApplySplit2Receipt;
use \Fuel\Service\OpenReceipt;
use helper;
use Illuminate\Database\Capsule\Manager as Capsule;
use \Models\OilOrg;
use Models\OilPayCompany;
use Models\OilReceiptQuotaDay;
use Models\OilReceiptTitle;

class ReceiptQuotaCase
{
    public function testG7sQuota()
    {
        $params['orgcode'] = '202CAO';
        $orgInfo           = OilOrg::getByOrgcode($params['orgcode']);
        if (!$orgInfo) {
            throw new \RuntimeException('机构号无效', 2);
        } else {
            $params['org_id'] = $orgInfo->id;
        }
        $data = OpenReceipt::getNewReceiptStyleForG7s($params);

        return $data;
    }

    public function testFossQuota()
    {
        $params    = [
            'receipt_title_id' => 1879,
            'org_id'           => 12946,
            'oil_type'         => 2
        ];
        $maxAmount = OpenReceipt::getQuota($params);

        return $maxAmount;
    }

    public function testMonth()
    {
        $month = ReceiptConfig::getMonth(1, 3);
        return $month;
    }

    public function receiptQuotaDayStatistic()
    {
        $companyOrgMap = Capsule::connection('slave')->select("select
b.id as org_id,a.company_name,b.orgcode,a.orgroot,b.receipt_mode,b.operators_id
from
oil_pay_company a
left join oil_org b on a.orgroot = b.orgcode
where a.status = 1 and b.is_del = 0 and b.is_test = 1
group by b.orgcode
order by org_id desc");

        $data = [];
        $chunk_data = array_chunk($companyOrgMap,5); //暂定5个一组
        foreach ($chunk_data as $v) {
            //入队列处理
            $data = (new \Jobs\ReceiptSnapShotJob($v))
                ->setTaskName('开票额度快照')
                ->onQueue('addReceiptSnapshotJob')
                ->setTries(2)
                ->dispatch();

            usleep(300);
        }

        Response::json($data);
    }

    public function getReceiptList()
    {
        $orgList = [
            ['id'=>26773,'code'=>'203TC60101'],
            ['id'=>26772,'code'=>'203TC60102'],
            ['id'=>26771,'code'=>'203TC60103'],
            ['id'=>26770,'code'=>'203TC60104'],
            ['id'=>26769,'code'=>'203TC60105'],
            ['id'=>26768,'code'=>'203TC60106'],
            ['id'=>26766,'code'=>'203TC60107'],
            ['id'=>26765,'code'=>'203TC60108'],
            ['id'=>26764,'code'=>'203TC60109'],
            ['id'=>26763,'code'=>'203TC6010A'],
            ['id'=>26762,'code'=>'203TC6010B'],
            ['id'=>26761,'code'=>'203TC6010C'],
            ['id'=>26760,'code'=>'203TC6010D'],
            ['id'=>26759,'code'=>'203TC6010E'],
            ['id'=>26758,'code'=>'203TC6010F'],
            ['id'=>26757,'code'=>'203TC6010G'],
            ['id'=>26756,'code'=>'203TC6010H'],
            ['id'=>26755,'code'=>'203TC6010I'],
            ['id'=>26754,'code'=>'203TC6010J'],
            ['id'=>26753,'code'=>'203TC6010K'],
            ['id'=>26752,'code'=>'203TC6010L'],
            ['id'=>26751,'code'=>'203TC6010M'],
            ['id'=>26750,'code'=>'203TC6010N'],
            ['id'=>26749,'code'=>'203TC6010O'],
            ['id'=>26748,'code'=>'203TC6010P'],
            ['id'=>26747,'code'=>'203TC6010Q'],
            ['id'=>26907,'code'=>'203TC6010R'],
            ['id'=>26906,'code'=>'203TC6010S'],
            ['id'=>26474,'code'=>'203TC60201'],
            ['id'=>26473,'code'=>'203TC60202'],
            ['id'=>26472,'code'=>'203TC60203'],
            ['id'=>26471,'code'=>'203TC60204'],
            ['id'=>26470,'code'=>'203TC60205'],
            ['id'=>26469,'code'=>'203TC60206'],
            ['id'=>26468,'code'=>'203TC60207'],
            ['id'=>26546,'code'=>'203TC60208'],
            ['id'=>26666,'code'=>'203TC60209'],
            ['id'=>27071,'code'=>'203TC6020A'],
            ['id'=>27216,'code'=>'203TC6020B'],
        ];
        foreach ($orgList as $key => $_val){
            $params = [
                'org_id'           => $_val['id'],
                'hiddenOrgCode'    => $_val['code'],
                'receipt_mode'     => 2,
            ];
            $_data = OilPayCompany::invoiceTitleQuotaSearch($params);
            foreach ($_data['data'] as $_one){
                $data[] = $_one;
            }
        }
        $this->exportExcel($data);
    }

    public function exportExcel($result = [])
    {
        if ($result) {
            $extType = 'csv';
            $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
            $exportData = [
                'filePath'  => $realPath . 'download' . DIRECTORY_SEPARATOR . 'oil_receipt_quota',
                'fileName'  => 'yuantong_' . date('YmdHis') . rand(100, 999),
                'fileExt'   => $extType,
                'sheetName' => "付款公司可开票额度",
                'download'  => 1,
                'title'     => [
                    'orgroot'                => '机构编码',
                    'org_name'               => '机构名称',
                    'company_name'           => '付款公司名称',
                    'corp_name'              => '发票抬头',
                    '_exclusive_custom'      => '专属客服',
                    'is_first_receipt_apply' => '首次开票',
                    'is_have_corp'           => '开票资料',
                    '_is_recepit_nowtime'    => '支持每日开票',

                    'max_amount'                                                   => 'A.可开票金额',
                    'total_charge_add_repayed_subtraction_receipted_receiptFrozen' => 'B.累计可用充值',
                    'total_receipt_trades'                                         => 'C.累计可开消费',
                    'total_charge'                                                 => 'D.累计充值(含期初)',
                    'total_repaid'                                                 => 'E.累计还款',
                    'total_charge_addition_repaid'                                 => 'F.累充加累还',

                    'total_receipt'        => 'G.累计开票',
                    'total_receipt_frozen' => 'H.累开冻结',
                    'total_trades'         => 'J.累计全部消费',

                    'total_fanli_calculate'    => 'K.累计计算返利',
                    'total_fanli_charge'       => 'L.累计充值返利',
                    'total_fanli'              => 'O.累计返利总和',
                    'total_use_fanli'          => 'M.累计使用返利',
                    'total_org_receipt'        => 'R.机构累计开票',
                    'total_org_receipt_frozen' => 'S.机构累开冻结',
                    '_is_test'                 => '是否测试机构',
                    'remark'                   => '备注',
                ],
                'data'      => $result,
            ];

            $fileUrl = ExcelWriter::exportXls(
                $exportData, function ($phpExcelObj, $data, $lineCell) {
                if (in_array($data['name'], ['vice_no', 'driver_tel', 'main_no', 'comp_remain'])) {
                    $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], \PHPExcel_Cell_DataType::TYPE_STRING);
                } else {
                    $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
                }
            }
            );

            Log::error('导出序号-$fileUrl:' . var_export($fileUrl, TRUE), [], 'ExportReceiptQuotaJob');

            return $fileUrl;
        }
    }

    public function receiptQuotaDayStatisticBak()
    {
        $dict = [
            '燃油'  => 12,
            '汽油'  => 1,
            '柴油'  => 2,
            '天然气' => 5,
            '尿素'  => 6,
        ];

//        $companyOrgMap = OilPayCompany::where("status", 1)->get();

        $companyOrgMap = Capsule::connection()->select("select
b.id as org_id,a.company_name,b.orgcode,a.orgroot,b.receipt_mode
from
oil_pay_company a
left join oil_org b on a.orgroot = b.orgcode
where a.status = 1 and b.is_del = 0 and b.is_test = 1
group by b.orgcode
order by org_id desc");

        $cacheNamePrefix = __METHOD__ . date("Ymd");
        $count           = 0;
        $maxCount        = 100000;

        $data = [];
        foreach ($companyOrgMap as $v) {
            //无车顶级下，只取前10的机构
            if( substr($v->orgroot,0,6) == '201XW3' && strlen($v->orgroot) < 10 ){
                continue;
            }
            Log::error($v->orgroot . "|" . $v->org_id . "|".$v->company_name."：开始", [], "receiptQuotaDayStatistic");
            if ($count > $maxCount) {
                var_dump("本次已执行完" . $count . "条");
                Log::error("本次已执行完" . $count . "条", [], "receiptQuotaDayStatistic");
                exit;
            }
            $cacheData = Cache::get($cacheNamePrefix . $v->org_id . $v->company_name);
            if ($cacheData) {
                Log::error($v->orgroot . "|" . $v->org_id . "：已计算过", [], "receiptQuotaDayStatistic");
                continue;
            }
//            $orgInfo = OilOrg::getByCacheOrgcode($v->orgroot);

            var_dump($v->orgroot . "|" . $v->org_id);

            $params = [
                'limit'            => 10000,
                //                'take'           => 10,
                //                'skip'           => 0,
                //                '_createtimeLe'  => date('Y-m-t', strtotime('-1 month')) . ' 23:59:59',
                'org_id'           => $v->org_id,
                'hiddenOrgCode'    => $v->orgroot,
                //'pay_company_name' => $v->company_name,
                'receipt_mode'     => $v->receipt_mode,
                //                'is_test'        => $orgInfo->is_test,
                //                'pay_company_id' => $v->id,
                'snapshot' => 'on',
            ];

            var_dump($params);
            Log::error($v->orgroot . "|" . $v->org_id . "：查询中...", [], "receiptQuotaDayStatistic");

            try{
                $_data = OilPayCompany::invoiceTitleQuotaSearch($params);
                Log::error($v->orgroot . "|" . $v->org_id . "：统计结果，" . var_export($_data, TRUE), [], "receiptQuotaDayStatistic");
                Log::error($v->orgroot . "|" . $v->org_id . "：查询结束，准备入库，" . count($_data), [], "receiptQuotaDayStatistic");

                if (isset($_data['data']) && count($_data['data']) > 0) {
                    foreach ($_data['data'] as $_k => &$_v) {
                        var_dump($v->orgcode, $_v['pay_company_id'], date("Ymd"), $dict[$_v['oil_classify']]);
                        $checkExist = OilReceiptQuotaDay::where('org_code', $v->orgcode)
                            ->where('pay_company_id', $_v['pay_company_id'])
                            ->where('end_date', date("Ymd"))
                            ->where('subcategory', $dict[$_v['oil_classify']])
                            ->get();

                        var_dump("已存在的记录条数为：" . count($checkExist));

                        Log::error("已存在的记录条数为：" . count($checkExist) . "，准备新增", [], "receiptQuotaDayStatistic");

                        $insertItem = [
                            'org_id'                         => $v->id,
                            'org_code'                       => $v->orgcode,
                            'subcategory'                    => $dict[$_v['oil_classify']],
                            'receipt_title_id'               => $_v['receipt_title_id'],
                            'pay_company_id'                 => $_v['pay_company_id'],
                            'end_date'                       => date("Ymd"),
                            'a_receipt_amount_remain'        => $_v['max_amount'] * 100,
                            'b_pay_total_recharge_remain'    => $_v['total_charge_add_repayed_subtraction_receipted_receiptFrozen'] * 100,
                            'c_org_total_trade_remain'       => $_v['total_receipt_trades'] * 100,
                            'd_pay_total_recharge'           => $_v['total_charge'] * 100,
                            'e_pay_total_repay'              => $_v['total_repaid'] * 100,
                            'f_pay_total_recharge_and_repay' => $_v['total_charge_addition_repaid'] * 100,
                            'g_pay_total_receipt'            => $_v['total_receipt'] * 100,
                            'h_pay_total_receipt_freeze'     => $_v['total_receipt_frozen'] * 100,
                            'j_org_total_trade'              => $_v['total_trades'] * 100,
                            'k_org_total_rebate_calculate'   => $_v['total_fanli_calculate'] * 100,
                            'l_org_total_rebate_recharge'    => $_v['total_fanli_charge'] * 100,
                            'o_org_total_rebate'             => $_v['total_fanli'] * 100,
                            'm_org_total_rebate_used'        => $_v['total_use_fanli'] * 100,
                            'r_org_total_receipt'            => $_v['total_org_receipt'] * 100,
                            's_org_total_receipt_freeze'     => $_v['total_org_receipt_frozen'] * 100,
                            //                                'createtime'                     => date("Y-m-d H:i:s")
                        ];

                        if (count($checkExist) == 0) {
                            $data[] = OilReceiptQuotaDay::add(
                                $insertItem
                            );

                            Log::error(var_export($v,TRUE) . "：新增成功", [], "receiptQuotaDayStatistic");
                        } elseif (count($checkExist) == 1) {
                            $insertItem['id'] = $checkExist[0]->id;
                            $data[]           = OilReceiptQuotaDay::edit($insertItem);
                            Log::error(var_export($v,TRUE) . "：修改成功", [], "receiptQuotaDayStatistic");
                        } else {
                            var_dump("已存在的记录为：" . count($checkExist));
                            Log::error($v->orgroot . "|" . $v->id . "：已存在的记录为" . count($checkExist), [], "receiptQuotaDayStatistic");
                        }

                        $count++;
                        Cache::put($cacheNamePrefix . $v->id . $v->company_name, [$v->orgroot, $v->id], 86400 * 2);
                    }
                }
            }catch (\Exception $e){
                echo strval($e);
            }
            usleep(300);
        }

        Response::json($data);
    }

    public function getCustomerReceiptQuota()
    {
    	$params = helper::filterParams();
	    if(!isset($params['orgcode']) || !$params['orgcode']){
		    throw new \RuntimeException('机构不能为空',2);
	    }
	    $params['receipt_type'] = '专票';
	    $params['is_del']=0;
	    $params['status'] = 1;
	    $params['requestFrom'] = 'g7s';
	    $params['multi_status']=array(0,1);

	    if(isset($params['orgcode']) && $params['orgcode']){
		    $info = \Models\OilOrg::getByOrgcode($params['orgcode']);
		    $params['org_id'] = $info->id;
		    $params['receipt_mode'] = $info->receipt_mode;
		    if($info->receipt_mode == 2){
			    $params['orgcodeNow'] = $params['orgcode'];
		    }else{
			    $params['orgroot'] = substr($params['orgcode'], 0, 6);
		    }
	    }
	    $params['oil_type'] = $params['oil_type'] ? $params['oil_type'] : 12;
	    $data = OilReceiptTitle::getList($params);

	    return $data;
    }

    public function testDetails()
    {
        $details = [
            'receipt_money'=>241.88,
            'receipt_num'=>33.70,
        ];
        $stock = 5000;
        $total = 2075.73;
        $res = ApplySplit2Receipt::getMinNum($details, $stock,6, $total);
        print_r($res);
    }
}