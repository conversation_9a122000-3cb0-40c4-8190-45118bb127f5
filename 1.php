<?php
$str = '15003603132 201XW32P5O201XW32P5O:63212419951028221X:15003603132
18897096765 201XW32P5O201XW32P5O:632822198304070014:18897096765
15897015259 201XW32P5O201XW32P5O:632125198612243117:15897015259
15992722288 201XW32P5O201XW32P5O:632124197512216831:15992722288
17609786593 201XW32P5O201XW32P5O:632124199306032213:17609786593
13709799919 201XW32P5O201XW32P5O:632801197807020012:13709799919
17697385465 201XW32P5O201XW32P5O:63212219930206483X:17697385465
13619785799 201XW32P5O201XW32P5O:630121198109208018:13619785799
13997350136 201XW32P5O201XW32P5O:632125197211180018:13997350136
18797107781 201XW32P5O201XW32P5O:632125199201253913:18797107781
13997392184 201XW32P5O201XW32P5O:632122197806055591:13997392184
13897444905 201XW32P5O201XW32P5O:632124198211092534:13897444905
13897367436 201XW32P5O201XW32P5O:632125197704080015:13897367436
18797308278 201XW32P5O201XW32P5O:630121199311036410:18797308278
13369795252 201XW32P5O201XW32P5O:632223198206040317:13369795252
17697103269 201XW32P5O201XW32P5O:630121199805086418:17697103269
13897064449 201XW32P5O201XW32P5O:632122198004198317:13897064449
18297275580 201XW32P5O201XW32P5O:632822198405100315:18297275580
13997048965 201XW32P5O201XW32P5O:632124197306082510:13997048965
13895507987 201XW32P5O201XW32P5O:642127196703042631:13895507987
13997499298 201XW32P5O201XW32P5O:632123197805241153:13997499298
17716029000 201XW32P5O201XW32P5O:632124198210180057:17716029000
18897067184 201XW32P5O201XW32P5O:63212519740323341X:18897067184
15202507731 201XW32P5O201XW32P5O:630121198006195111:15202507731
13909772804 201XW32P5O201XW32P5O:632822197303280015:13909772804
15352263655 201XW32P5O201XW32P5O:632122198806058076:15352263655
13619716295 201XW32P5O201XW32P5O:632124199208192213:13619716295
13897364993 201XW32P5O201XW32P5O:632125197204050514:13897364993
13519718844 201XW32P5O201XW32P5O:632124197301252234:13519718844
19897261117 201XW32P5O201XW32P5O:630121197608254831:19897261117
18097235926 201XW32P5O201XW32P5O:63212419821102251X:18097235926
15890289852 201XW32P5O201XW32P5O:411222197602167536:15890289852';
$strArr = explode("\n",$str);

// 引入必要的类和配置
require_once 'framework/model.class.php';
require_once 'app/admin/config/config.php';

// 初始化数据库连接
$model = new model();

// 统计变量
$totalProcessed = 0;
$successUpdated = 0;
$notFound = 0;
$alreadyHasUniqueId = 0;
$errors = [];

echo "开始处理副卡unique_id更新任务...\n";
echo "总共需要处理 " . count($strArr) . " 条记录\n\n";

foreach ($strArr as $index => $val){
    $val = trim($val);
    if (empty($val)) {
        continue;
    }

    $temp = explode(" ", $val);

    // 确保有两列数据
    if (count($temp) < 2) {
        $errors[] = "第" . ($index + 1) . "行数据格式错误: $val";
        continue;
    }

    $phone = trim($temp[0]); // 第一列：手机号
    $secondColumn = trim($temp[1]); // 第二列：要拼接的数据

    $totalProcessed++;

    try {
        // 查询副卡信息表中手机号匹配且unique_id为null的记录
        $sql = "SELECT id, vice_no, driver_tel, unique_id FROM oil_card_vice
                WHERE driver_tel = ? AND (unique_id IS NULL OR unique_id = '')";

        $result = $model->queryBySql($sql, [$phone]);

        if (empty($result)) {
            $notFound++;
            echo "第" . ($index + 1) . "行: 手机号 $phone 未找到unique_id为空的副卡记录\n";
            continue;
        }

        // 如果找到多条记录，处理所有记录
        foreach ($result as $card) {
            // 检查是否已经有unique_id
            if (!empty($card->unique_id)) {
                $alreadyHasUniqueId++;
                echo "第" . ($index + 1) . "行: 副卡 {$card->vice_no} 已有unique_id: {$card->unique_id}\n";
                continue;
            }

            // 拼接新的unique_id: 7145 + 第二列的值
            $newUniqueId = '7145' . $secondColumn;

            // 更新数据库
            $updateSql = "UPDATE oil_card_vice SET unique_id = ? WHERE id = ?";
            $updateResult = $model->exec($updateSql, [$newUniqueId, $card->id]);

            if ($updateResult) {
                $successUpdated++;
                echo "第" . ($index + 1) . "行: 成功更新副卡 {$card->vice_no} 的unique_id为: $newUniqueId\n";
            } else {
                $errors[] = "第" . ($index + 1) . "行: 更新副卡 {$card->vice_no} 失败";
                echo "第" . ($index + 1) . "行: 更新副卡 {$card->vice_no} 失败\n";
            }
        }

    } catch (Exception $e) {
        $errors[] = "第" . ($index + 1) . "行处理异常: " . $e->getMessage();
        echo "第" . ($index + 1) . "行处理异常: " . $e->getMessage() . "\n";
    }
}

// 输出统计结果
echo "\n=== 处理完成 ===\n";
echo "总处理记录数: $totalProcessed\n";
echo "成功更新记录数: $successUpdated\n";
echo "未找到匹配记录数: $notFound\n";
echo "已有unique_id记录数: $alreadyHasUniqueId\n";
echo "错误记录数: " . count($errors) . "\n";

if (!empty($errors)) {
    echo "\n=== 错误详情 ===\n";
    foreach ($errors as $error) {
        echo $error . "\n";
    }
}